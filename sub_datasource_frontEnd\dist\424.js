"use strict";(self.webpackChunkpageplug_datasource_frontend=self.webpackChunkpageplug_datasource_frontend||[]).push([[424],{5424:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval('{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(6070);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(212);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(8076);\n/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(antd__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5378);\n\r\n\r\n\r\n\r\nconst DataExporter = ({ data, format = \'csv\' }) => {\r\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Card, { title: "\\u6570\\u636E\\u5BFC\\u51FA", extra: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A, {}), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Empty, { description: "\\u6570\\u636E\\u5BFC\\u51FA\\u7EC4\\u4EF6\\u5F00\\u53D1\\u4E2D...", image: antd__WEBPACK_IMPORTED_MODULE_2__.Empty.PRESENTED_IMAGE_SIMPLE }) }));\r\n};\r\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DataExporter);\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTQyNC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUEwQjtBQUNTO0FBQ2tCO0FBT3JELE1BQU0sWUFBWSxHQUFnQyxDQUFDLEVBQUUsSUFBSSxFQUFFLE1BQU0sR0FBRyxLQUFLLEVBQUUsRUFBRSxFQUFFO0lBQzdFLE9BQU8sQ0FDTCx1REFBQyxzQ0FBSSxJQUFDLEtBQUssRUFBQywwQkFBTSxFQUFDLEtBQUssRUFBRSx1REFBQyxrRUFBZ0IsS0FBRyxZQUM1Qyx1REFBQyx1Q0FBSyxJQUNKLFdBQVcsRUFBQywyREFBYyxFQUMxQixLQUFLLEVBQUUsdUNBQUssQ0FBQyxzQkFBc0IsR0FDbkMsR0FDRyxDQUNSLENBQUM7QUFDSixDQUFDLENBQUM7QUFFRixpRUFBZSxZQUFZLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wYWdlcGx1Zy1kYXRhc291cmNlLWZyb250ZW5kLy4vc3JjL2NvbXBvbmVudHMvRGF0YUV4cG9ydGVyL2luZGV4LnRzeD9iMmNkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBDYXJkLCBFbXB0eSB9IGZyb20gJ2FudGQnO1xuaW1wb3J0IHsgRG93bmxvYWRPdXRsaW5lZCB9IGZyb20gJ0BhbnQtZGVzaWduL2ljb25zJztcblxuaW50ZXJmYWNlIERhdGFFeHBvcnRlclByb3BzIHtcbiAgZGF0YT86IGFueVtdO1xuICBmb3JtYXQ/OiAnY3N2JyB8ICdleGNlbCcgfCAnanNvbic7XG59XG5cbmNvbnN0IERhdGFFeHBvcnRlcjogUmVhY3QuRkM8RGF0YUV4cG9ydGVyUHJvcHM+ID0gKHsgZGF0YSwgZm9ybWF0ID0gJ2NzdicgfSkgPT4ge1xuICByZXR1cm4gKFxuICAgIDxDYXJkIHRpdGxlPVwi5pWw5o2u5a+85Ye6XCIgZXh0cmE9ezxEb3dubG9hZE91dGxpbmVkIC8+fT5cbiAgICAgIDxFbXB0eVxuICAgICAgICBkZXNjcmlwdGlvbj1cIuaVsOaNruWvvOWHuue7hOS7tuW8gOWPkeS4rS4uLlwiXG4gICAgICAgIGltYWdlPXtFbXB0eS5QUkVTRU5URURfSU1BR0VfU0lNUExFfVxuICAgICAgLz5cbiAgICA8L0NhcmQ+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBEYXRhRXhwb3J0ZXI7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///5424\n\n}')}}]);