import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { message } from 'antd';

import { Query, CreateQueryRequest, QueryExecutionResult, ExecuteQueryRequest } from '../../types/api';
import { apiClient } from '../../services/apiClient';

// 异步thunk actions
export const fetchQueries = createAsyncThunk(
  'query/fetchQueries',
  async (params: {
    datasourceId?: string;
    tag?: string;
  } = {}) => {
    const response = await apiClient.get('/queries', { params });
    return response.data;
  }
);

export const createQuery = createAsyncThunk(
  'query/createQuery',
  async (request: CreateQueryRequest) => {
    const response = await apiClient.post('/queries', request);
    message.success('查询保存成功');
    return response.data;
  }
);

export const executeQuery = createAsyncThunk(
  'query/executeQuery',
  async ({ datasourceId, request }: { datasourceId: string; request: ExecuteQueryRequest }) => {
    const response = await apiClient.post(`/datasources/${datasourceId}/execute`, request);
    return { datasourceId, request, result: response.data };
  }
);

export const executeSavedQuery = createAsyncThunk(
  'query/executeSavedQuery',
  async ({ queryId, parameters }: { queryId: string; parameters?: Array<{ key: string; value: any }> }) => {
    const response = await apiClient.post(`/queries/${queryId}/execute`, { parameters });
    return { queryId, result: response.data };
  }
);

// 状态接口
interface QueryState {
  // 保存的查询列表
  savedQueries: Query[];
  
  // 当前查询
  currentQuery: {
    text: string;
    datasourceId: string;
    parameters: Array<{ key: string; value: any; type?: string }>;
  };
  
  // 执行结果
  executionResults: Record<string, QueryExecutionResult>;
  
  // 查询历史
  queryHistory: Array<{
    id: string;
    query: string;
    datasourceId: string;
    executedAt: string;
    success: boolean;
    executionTime: number;
  }>;
  
  // 加载状态
  loading: {
    list: boolean;
    create: boolean;
    execute: boolean;
  };
  
  // 错误信息
  error: string | null;
  
  // 过滤条件
  filters: {
    datasourceId?: string;
    tag?: string;
  };
}

// 初始状态
const initialState: QueryState = {
  savedQueries: [],
  currentQuery: {
    text: '',
    datasourceId: '',
    parameters: []
  },
  executionResults: {},
  queryHistory: [],
  loading: {
    list: false,
    create: false,
    execute: false
  },
  error: null,
  filters: {}
};

// 创建slice
const querySlice = createSlice({
  name: 'query',
  initialState,
  reducers: {
    // 设置当前查询文本
    setCurrentQueryText: (state, action: PayloadAction<string>) => {
      state.currentQuery.text = action.payload;
    },
    
    // 设置当前数据源
    setCurrentDatasourceId: (state, action: PayloadAction<string>) => {
      state.currentQuery.datasourceId = action.payload;
    },
    
    // 设置查询参数
    setQueryParameters: (state, action: PayloadAction<Array<{ key: string; value: any; type?: string }>>) => {
      state.currentQuery.parameters = action.payload;
    },
    
    // 添加查询参数
    addQueryParameter: (state, action: PayloadAction<{ key: string; value: any; type?: string }>) => {
      state.currentQuery.parameters.push(action.payload);
    },
    
    // 移除查询参数
    removeQueryParameter: (state, action: PayloadAction<number>) => {
      state.currentQuery.parameters.splice(action.payload, 1);
    },
    
    // 更新查询参数
    updateQueryParameter: (state, action: PayloadAction<{ index: number; parameter: { key: string; value: any; type?: string } }>) => {
      const { index, parameter } = action.payload;
      if (state.currentQuery.parameters[index]) {
        state.currentQuery.parameters[index] = parameter;
      }
    },
    
    // 设置过滤条件
    setFilters: (state, action: PayloadAction<Partial<QueryState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    
    // 清除错误
    clearError: (state) => {
      state.error = null;
    },
    
    // 清除执行结果
    clearExecutionResult: (state, action: PayloadAction<string>) => {
      delete state.executionResults[action.payload];
    },
    
    // 清除所有执行结果
    clearAllExecutionResults: (state) => {
      state.executionResults = {};
    },
    
    // 加载查询到编辑器
    loadQueryToEditor: (state, action: PayloadAction<Query>) => {
      const query = action.payload;
      state.currentQuery = {
        text: query.query,
        datasourceId: query.datasourceId,
        parameters: query.parameters
      };
    },
    
    // 清除查询历史
    clearQueryHistory: (state) => {
      state.queryHistory = [];
    }
  },
  extraReducers: (builder) => {
    // 获取查询列表
    builder
      .addCase(fetchQueries.pending, (state) => {
        state.loading.list = true;
        state.error = null;
      })
      .addCase(fetchQueries.fulfilled, (state, action: PayloadAction<Query[]>) => {
        state.loading.list = false;
        state.savedQueries = action.payload;
      })
      .addCase(fetchQueries.rejected, (state, action) => {
        state.loading.list = false;
        state.error = action.error.message || '获取查询列表失败';
      });

    // 创建查询
    builder
      .addCase(createQuery.pending, (state) => {
        state.loading.create = true;
        state.error = null;
      })
      .addCase(createQuery.fulfilled, (state, action: PayloadAction<Query>) => {
        state.loading.create = false;
        state.savedQueries.unshift(action.payload);
      })
      .addCase(createQuery.rejected, (state, action) => {
        state.loading.create = false;
        state.error = action.error.message || '保存查询失败';
      });

    // 执行查询
    builder
      .addCase(executeQuery.pending, (state) => {
        state.loading.execute = true;
        state.error = null;
      })
      .addCase(executeQuery.fulfilled, (state, action) => {
        state.loading.execute = false;
        const { datasourceId, request, result } = action.payload;
        
        // 保存执行结果
        const resultKey = `${datasourceId}_${Date.now()}`;
        state.executionResults[resultKey] = result;
        
        // 添加到查询历史
        state.queryHistory.unshift({
          id: resultKey,
          query: request.query,
          datasourceId,
          executedAt: new Date().toISOString(),
          success: result.isExecutionSuccess,
          executionTime: result.executionTime
        });
        
        // 限制历史记录数量
        if (state.queryHistory.length > 50) {
          state.queryHistory = state.queryHistory.slice(0, 50);
        }
      })
      .addCase(executeQuery.rejected, (state, action) => {
        state.loading.execute = false;
        state.error = action.error.message || '查询执行失败';
      });

    // 执行保存的查询
    builder
      .addCase(executeSavedQuery.pending, (state) => {
        state.loading.execute = true;
        state.error = null;
      })
      .addCase(executeSavedQuery.fulfilled, (state, action) => {
        state.loading.execute = false;
        const { queryId, result } = action.payload;
        
        // 保存执行结果
        state.executionResults[queryId] = result;
        
        // 更新查询的最后执行时间
        const query = state.savedQueries.find(q => q.id === queryId);
        if (query) {
          query.lastExecuted = new Date().toISOString();
          
          // 添加到查询历史
          state.queryHistory.unshift({
            id: `${queryId}_${Date.now()}`,
            query: query.query,
            datasourceId: query.datasourceId,
            executedAt: new Date().toISOString(),
            success: result.isExecutionSuccess,
            executionTime: result.executionTime
          });
        }
      })
      .addCase(executeSavedQuery.rejected, (state, action) => {
        state.loading.execute = false;
        state.error = action.error.message || '执行保存的查询失败';
      });
  }
});

// 导出actions
export const {
  setCurrentQueryText,
  setCurrentDatasourceId,
  setQueryParameters,
  addQueryParameter,
  removeQueryParameter,
  updateQueryParameter,
  setFilters,
  clearError,
  clearExecutionResult,
  clearAllExecutionResults,
  loadQueryToEditor,
  clearQueryHistory
} = querySlice.actions;

// 导出reducer
export default querySlice.reducer;

// 选择器
export const selectSavedQueries = (state: { query: QueryState }) => state.query.savedQueries;
export const selectCurrentQuery = (state: { query: QueryState }) => state.query.currentQuery;
export const selectQueryLoading = (state: { query: QueryState }) => state.query.loading;
export const selectQueryError = (state: { query: QueryState }) => state.query.error;
export const selectQueryFilters = (state: { query: QueryState }) => state.query.filters;
export const selectQueryHistory = (state: { query: QueryState }) => state.query.queryHistory;
export const selectExecutionResults = (state: { query: QueryState }) => state.query.executionResults;
export const selectExecutionResult = (key: string) => (state: { query: QueryState }) => state.query.executionResults[key];

// 过滤后的查询列表选择器
export const selectFilteredQueries = (state: { query: QueryState }) => {
  const { savedQueries, filters } = state.query;
  
  return savedQueries.filter(query => {
    // 数据源过滤
    if (filters.datasourceId && query.datasourceId !== filters.datasourceId) {
      return false;
    }
    
    // 标签过滤
    if (filters.tag && !query.tags.includes(filters.tag)) {
      return false;
    }
    
    return true;
  });
};
