// API 响应基础类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  timestamp: string;
}

// 分页响应类型
export interface PaginatedResponse<T> {
  content: T[];
  pagination: {
    page: number;
    size: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrevious: boolean;
  };
}

// 错误响应类型
export interface ApiError {
  code: number;
  message: string;
  error: {
    type: string;
    details?: Array<{
      field: string;
      message: string;
    }>;
  };
  timestamp: string;
}

// 数据源类型
export interface Datasource {
  id: string;
  name: string;
  pluginId: string;
  pluginName: string;
  workspaceId: string;
  isValid: boolean;
  isConfigured: boolean;
  lastUsed?: string;
  createdAt: string;
  updatedAt: string;
  datasourceConfiguration: DatasourceConfiguration;
}

// 数据源配置类型
export interface DatasourceConfiguration {
  url: string;
  databaseName?: string;
  authentication?: {
    authType: 'basic' | 'oauth' | 'apikey' | 'bearer';
    username?: string;
    password?: string;
    token?: string;
    apiKey?: string;
    secretExists?: {
      password?: boolean;
      token?: boolean;
      apiKey?: boolean;
    };
  };
  connection?: {
    ssl?: {
      enabled: boolean;
      mode?: 'REQUIRED' | 'PREFERRED' | 'DISABLED';
      certificateFile?: string;
      keyFile?: string;
      caCertificateFile?: string;
    };
    timeout?: number;
    maxConnections?: number;
  };
  properties?: Array<{
    key: string;
    value: string;
  }>;
  headers?: Array<{
    key: string;
    value: string;
  }>;
}

// 插件类型
export interface Plugin {
  id: string;
  name: string;
  displayName: string;
  version: string;
  category: 'database' | 'api' | 'cloud' | 'ai';
  status: 'active' | 'inactive' | 'deprecated';
  description: string;
  iconUrl: string;
  documentationUrl: string;
  supportedOperations: ('READ' | 'WRITE' | 'DELETE')[];
  templates: Record<string, string>;
  formConfig?: PluginFormConfig;
}

// 插件表单配置类型
export interface PluginFormConfig {
  sections: Array<{
    name: string;
    title: string;
    fields: Array<{
      name: string;
      label: string;
      type: 'INPUT_TEXT' | 'INPUT_NUMBER' | 'INPUT_PASSWORD' | 'SELECT' | 'SWITCH' | 'TEXTAREA';
      required: boolean;
      placeholder?: string;
      defaultValue?: any;
      options?: Array<{
        label: string;
        value: any;
      }>;
      validation?: {
        pattern?: string;
        message?: string;
        min?: number;
        max?: number;
      };
      dependencies?: string[];
      hidden?: boolean;
    }>;
  }>;
}

// 连接测试结果类型
export interface ConnectionTestResult {
  success: boolean;
  message: string;
  responseTime: number;
  details?: {
    serverVersion?: string;
    charset?: string;
    [key: string]: any;
  };
}

// 数据源结构类型
export interface DatasourceStructure {
  tables: Array<{
    name: string;
    type: 'TABLE' | 'VIEW' | 'COLLECTION';
    columns: Array<{
      name: string;
      type: string;
      isPrimaryKey?: boolean;
      isAutoIncrement?: boolean;
      isNullable?: boolean;
      maxLength?: number;
      defaultValue?: any;
    }>;
    keys?: Array<{
      name: string;
      type: 'PRIMARY' | 'FOREIGN' | 'UNIQUE' | 'INDEX';
      columnNames: string[];
    }>;
    templates?: Array<{
      title: string;
      body: string;
      suggested?: boolean;
    }>;
  }>;
}

// 查询执行结果类型
export interface QueryExecutionResult {
  isExecutionSuccess: boolean;
  body: any[];
  headers: Record<string, string>;
  statusCode: string;
  executionTime: number;
  rowsAffected: number;
  error?: {
    message: string;
    code?: string;
    line?: number;
    column?: number;
  };
}

// 查询类型
export interface Query {
  id: string;
  name: string;
  datasourceId: string;
  query: string;
  parameters: Array<{
    key: string;
    value: any;
    type?: 'string' | 'number' | 'boolean' | 'date';
  }>;
  tags: string[];
  createdAt: string;
  updatedAt: string;
  lastExecuted?: string;
}

// 请求参数类型
export interface CreateDatasourceRequest {
  name: string;
  pluginId: string;
  workspaceId: string;
  datasourceConfiguration: DatasourceConfiguration;
}

export interface UpdateDatasourceRequest {
  name?: string;
  datasourceConfiguration?: Partial<DatasourceConfiguration>;
}

export interface ExecuteQueryRequest {
  query: string;
  parameters?: Array<{
    key: string;
    value: any;
  }>;
}

export interface CreateQueryRequest {
  name: string;
  datasourceId: string;
  query: string;
  parameters: Array<{
    key: string;
    value: any;
    type?: string;
  }>;
  tags: string[];
}
