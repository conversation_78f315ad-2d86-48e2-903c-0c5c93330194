"use strict";(self.webpackChunkpageplug_datasource_frontend=self.webpackChunkpageplug_datasource_frontend||[]).push([[340],{3340:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval('{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   store: () => (/* binding */ store),\n/* harmony export */   useAppDispatch: () => (/* binding */ useAppDispatch),\n/* harmony export */   useAppSelector: () => (/* binding */ useAppSelector)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(9235);\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(8037);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_redux__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _slices_datasourceSlice__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(2719);\n/* harmony import */ var _slices_pluginSlice__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(4629);\n/* harmony import */ var _slices_querySlice__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(7166);\n/* harmony import */ var _slices_uiSlice__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(5478);\n\r\n\r\n\r\n\r\n\r\n\r\nconst store = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.configureStore)({\r\n    reducer: {\r\n        datasource: _slices_datasourceSlice__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Ay,\r\n        plugin: _slices_pluginSlice__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Ay,\r\n        query: _slices_querySlice__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Ay,\r\n        ui: _slices_uiSlice__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay\r\n    },\r\n    middleware: (getDefaultMiddleware) => getDefaultMiddleware({\r\n        serializableCheck: {\r\n            ignoredActions: [\'persist/PERSIST\', \'persist/REHYDRATE\']\r\n        }\r\n    }),\r\n    devTools: "development" !== \'production\'\r\n});\r\nconst useAppDispatch = () => (0,react_redux__WEBPACK_IMPORTED_MODULE_1__.useDispatch)();\r\nconst useAppSelector = react_redux__WEBPACK_IMPORTED_MODULE_1__.useSelector;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///3340\n\n}')}}]);