# PagePlug 数据集模块详细分析

## 1. 数据集模块概述

数据集模块是 PagePlug 的核心功能之一，负责管理API接口、数据库查询、JS对象等数据集合。该模块提供了统一的数据访问层，支持多种数据源的查询构建、执行和结果处理。

### 1.1 功能范围
- **API管理**: REST API、GraphQL API的创建和管理
- **数据库查询**: SQL查询、NoSQL查询的构建和执行
- **JS对象**: JavaScript代码的编写和执行
- **查询构建器**: 可视化查询构建工具
- **数据集合**: 多个查询的组织和管理
- **结果处理**: 查询结果的格式化和缓存

### 1.2 模块架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    数据集模块架构                            │
├─────────────────────────────────────────────────────────────┤
│  用户界面层                                                  │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │  API编辑器   │  │  查询编辑器  │  │  JS对象编辑器 │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
├─────────────────────────────────────────────────────────────┤
│  业务逻辑层                                                  │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │  查询构建器  │  │  执行引擎    │  │  结果处理器  │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
├─────────────────────────────────────────────────────────────┤
│  数据访问层                                                  │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │  Action服务  │  │ Collection服务│  │ JSAction服务 │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

## 2. 核心实体模型

### 2.1 Action (查询/API)
```typescript
interface Action {
  id: string;
  name: string;
  applicationId: string;
  workspaceId: string;
  pluginId: string;
  pluginType: PluginType; // DB, API, SAAS, JS
  datasource: {
    id: string;
    name: string;
    pluginId: string;
  };
  actionConfiguration: {
    timeoutInMillisecond: number;
    paginationType: PaginationType;
    headers: Property[];
    params: Property[];
    body?: string;
    httpMethod?: string;
    path?: string;
    // SQL查询配置
    query?: string;
    // GraphQL配置
    graphqlQuery?: string;
    variables?: string;
  };
  executeOnLoad: boolean;
  dynamicBindingPathList: DynamicBinding[];
  isValid: boolean;
  invalids: string[];
  messages: string[];
  userPermissions: string[];
}

enum PluginType {
  API = "API",
  DB = "DB",
  SAAS = "SAAS",
  JS = "JS",
  REMOTE = "REMOTE"
}

enum PaginationType {
  NONE = "NONE",
  PAGE_NO = "PAGE_NO",
  URL = "URL"
}
```

### 2.2 JSCollection (JS对象集合)
```typescript
interface JSCollection {
  id: string;
  name: string;
  applicationId: string;
  workspaceId: string;
  pluginId: string;
  pluginType: PluginType.JS;
  body: string; // JavaScript代码
  variables: JSVariable[];
  actions: JSAction[];
  isMainJSCollection: boolean;
  userPermissions: string[];
}

interface JSAction {
  id: string;
  name: string;
  actionConfiguration: {
    body: string;
    jsArguments: JSArgument[];
    isAsync: boolean;
    timeoutInMillisecond: number;
  };
  executeOnLoad: boolean;
  clientSideExecution: boolean;
  confirmBeforeExecute: boolean;
}

interface JSVariable {
  name: string;
  value: any;
  type: string;
}
```

### 2.3 Collection (数据集合)
```typescript
interface Collection {
  id: string;
  name: string;
  applicationId: string;
  workspaceId: string;
  shared: boolean;
  actions: NewAction[]; // 包含的Action列表
}

interface NewAction {
  id: string;
  name: string;
  pluginType: PluginType;
  datasource: {
    id: string;
    name: string;
  };
}
```

## 3. 前端模块分析

### 3.1 API编辑器模块
**位置**: `app/client/src/pages/Editor/APIEditor/`

#### 3.1.1 主要组件
```typescript
// API编辑器主组件
APIEditor/
├── index.tsx                    # API编辑器入口
├── Editor.tsx                   # 编辑器主体
├── Form.tsx                     # API表单
├── PostBodyData.tsx             # POST请求体编辑
├── CommonEditorForm.tsx         # 通用表单组件
├── Pagination.tsx               # 分页配置
├── GraphQL/                     # GraphQL编辑器
│   ├── QueryEditor.tsx          # GraphQL查询编辑器
│   ├── VariableEditor.tsx       # 变量编辑器
│   └── GraphQLEditorForm.tsx    # GraphQL表单
└── RestAPIForm.tsx              # REST API表单
```

#### 3.1.2 核心功能
- **HTTP方法配置**: GET, POST, PUT, DELETE, PATCH等
- **URL路径配置**: 支持动态参数绑定
- **请求头管理**: 自定义HTTP头部
- **请求体编辑**: JSON, Form Data, Raw等格式
- **认证配置**: Basic Auth, Bearer Token, API Key等
- **响应处理**: 响应格式化和错误处理

#### 3.1.3 状态管理
```typescript
// API编辑器状态
interface APIEditorState {
  currentAPI: Action | null;
  isRunning: boolean;
  isDeleting: boolean;
  response: ActionResponse | null;
  errors: ActionError[];
  settings: {
    timeout: number;
    pagination: PaginationConfig;
    headers: Property[];
    params: Property[];
  };
}
```

### 3.2 查询编辑器模块
**位置**: `app/client/src/pages/Editor/QueryEditor/`

#### 3.2.1 主要组件
```typescript
QueryEditor/
├── index.tsx                    # 查询编辑器入口
├── Editor.tsx                   # 编辑器主体
├── Form.tsx                     # 查询表单
├── FormRender.tsx               # 表单渲染器
├── DatasourceSelector.tsx       # 数据源选择器
├── QueryEditorHeader.tsx        # 编辑器头部
├── QueryResponseTab.tsx         # 响应结果标签页
├── QueryDebuggerTabs.tsx        # 调试器标签页
├── Table.tsx                    # 结果表格
├── JSONViewer.tsx               # JSON查看器
├── TemplateMenu.tsx             # 查询模板菜单
└── helpers.tsx                  # 辅助函数
```

#### 3.2.2 查询构建器功能
- **SQL查询构建**: 支持SELECT, INSERT, UPDATE, DELETE
- **NoSQL查询**: MongoDB查询语法支持
- **参数绑定**: 动态参数替换
- **查询模板**: 预定义查询模板
- **语法高亮**: SQL/NoSQL语法高亮
- **自动完成**: 表名、字段名自动补全

#### 3.2.3 查询执行流程
```typescript
// 查询执行流程
const executeQuery = async (action: Action) => {
  // 1. 参数验证
  const validationErrors = validateAction(action);
  if (validationErrors.length > 0) {
    throw new ValidationError(validationErrors);
  }

  // 2. 参数绑定
  const boundAction = bindActionParams(action, currentContext);

  // 3. 发送请求
  const response = await actionAPI.executeAction(boundAction);

  // 4. 结果处理
  const processedResponse = processActionResponse(response);

  // 5. 更新状态
  dispatch(updateActionResponse(action.id, processedResponse));

  return processedResponse;
};
```

### 3.3 JS对象编辑器模块
**位置**: `app/client/src/pages/Editor/JSEditor/`

#### 3.3.1 主要组件
```typescript
JSEditor/
├── index.tsx                    # JS编辑器入口
├── JSObjectNameEditor.tsx       # JS对象名称编辑
├── JSObjectHotKeys.tsx          # 快捷键处理
├── JSFunctionRun.tsx            # 函数执行
├── JSResponseView.tsx           # 响应查看
├── JSFunctionSettings.tsx       # 函数设置
└── stateHelpers.tsx             # 状态辅助函数
```

#### 3.3.2 JS对象功能
- **代码编辑**: JavaScript代码编辑器
- **函数定义**: 支持同步和异步函数
- **变量管理**: 全局变量定义和管理
- **API调用**: 在JS中调用其他API
- **数据处理**: 数据转换和处理逻辑
- **事件处理**: 组件事件处理函数

#### 3.3.3 JS对象示例
```javascript
// JS对象示例
export default {
  // 变量定义
  myVar1: "Hello World",
  myVar2: 123,

  // 异步函数
  myFun1: async () => {
    // 调用API
    const response = await Api1.run();
    return response.data;
  },

  // 同步函数
  myFun2: () => {
    // 数据处理
    return this.myVar1.toUpperCase();
  },

  // 数据转换函数
  transformData: (data) => {
    return data.map(item => ({
      id: item.id,
      name: item.name.toUpperCase(),
      status: item.active ? 'Active' : 'Inactive'
    }));
  }
}
```

## 4. 后端模块分析

### 4.1 Action服务层
**位置**: `app/server/appsmith-server/src/main/java/com/appsmith/server/services/`

#### 4.1.1 核心服务类
```java
// Action服务接口
public interface NewActionService extends CrudService<ActionDTO, String> {
    Mono<ActionDTO> createAction(ActionDTO action);
    Mono<ActionDTO> updateAction(String id, ActionDTO action);
    Mono<ActionExecutionResult> executeAction(ActionDTO action, ExecuteActionDTO executeActionDTO);
    Flux<ActionDTO> getActionsForApplication(String applicationId);
    Mono<ActionDTO> moveAction(ActionMoveDTO actionMoveDTO);
    Mono<ActionDTO> copyAction(ActionCopyDTO actionCopyDTO);
}

// Action执行服务
public interface ActionExecutionService {
    Mono<ActionExecutionResult> executeAction(ExecuteActionDTO executeActionDTO);
    Mono<ActionExecutionResult> executeActionWithCache(ExecuteActionDTO executeActionDTO);
    Flux<ActionExecutionResult> executeActionsInBatch(List<ExecuteActionDTO> executeActionDTOs);
}
```

#### 4.1.2 Action执行流程
```java
@Service
public class ActionExecutionServiceImpl implements ActionExecutionService {

    @Override
    public Mono<ActionExecutionResult> executeAction(ExecuteActionDTO executeActionDTO) {
        return validateExecuteActionDTO(executeActionDTO)
            .flatMap(this::enrichExecuteActionDTO)
            .flatMap(this::executeActionWithPlugin)
            .flatMap(this::processActionResult)
            .doOnSuccess(result -> cacheActionResult(executeActionDTO, result))
            .doOnError(error -> logActionError(executeActionDTO, error));
    }

    private Mono<ActionExecutionResult> executeActionWithPlugin(ExecuteActionDTO dto) {
        return pluginExecutorHelper.getPluginExecutor(dto.getAction().getPluginId())
            .flatMap(pluginExecutor -> {
                return pluginExecutor.execute(
                    dto.getAction().getDatasource(),
                    dto.getAction().getActionConfiguration(),
                    dto.getParams()
                );
            });
    }
}
```

### 4.2 Collection服务层
**位置**: `app/server/appsmith-server/src/main/java/com/appsmith/server/actioncollections/`

#### 4.2.1 Collection管理
```java
// Collection服务接口
public interface ActionCollectionService extends CrudService<ActionCollectionDTO, String> {
    Mono<ActionCollectionDTO> createCollection(ActionCollectionDTO collection, String branchName);
    Mono<ActionCollectionDTO> updateCollection(String id, ActionCollectionDTO collection);
    Flux<ActionCollectionDTO> getCollectionsForApplication(String applicationId);
    Mono<ActionCollectionDTO> addActionToCollection(String collectionId, String actionId);
    Mono<ActionCollectionDTO> removeActionFromCollection(String collectionId, String actionId);
}

// Collection布局服务
public interface LayoutCollectionService {
    Mono<ActionCollectionDTO> createCollection(ActionCollectionDTO collectionDTO, String branchName);
    Mono<ActionCollectionDTO> updateLayout(String collectionId, ActionCollectionDTO collectionDTO);
    Mono<ActionCollectionDTO> refactorCollectionName(RefactorNameDTO refactorNameDTO);
}
```

### 4.3 JS对象服务层
**位置**: `app/server/appsmith-server/src/main/java/com/appsmith/server/jslibs/`

#### 4.3.1 JS执行引擎
```java
// JS执行服务
public interface JSExecutionService {
    Mono<ActionExecutionResult> executeJSFunction(JSExecutionContext context);
    Mono<Object> evaluateJSExpression(String expression, Map<String, Object> context);
    Mono<Boolean> validateJSFunction(String functionBody);
}

// JS上下文管理
public class JSExecutionContext {
    private String functionBody;
    private Map<String, Object> variables;
    private Map<String, Object> globalContext;
    private List<String> arguments;
    private boolean isAsync;
    private int timeoutInMilliseconds;
}
```

## 5. 数据流和交互

### 5.1 查询执行数据流
```
用户操作 → 前端编辑器 → Action构建 → 参数绑定 → 后端服务 → 插件执行 → 数据源 → 结果返回 → 前端展示
```

### 5.2 JS对象执行流程
```
JS代码编辑 → 语法验证 → 函数解析 → 上下文构建 → JS引擎执行 → 结果处理 → 状态更新
```

### 5.3 Collection管理流程
```
创建Collection → 添加Actions → 配置关系 → 执行顺序 → 结果聚合 → 数据展示
```

## 6. 查询构建器详细分析

### 6.1 SQL查询构建器
**位置**: `app/client/src/WidgetQueryGenerators/`

#### 6.1.1 支持的数据库类型
```typescript
// PostgreSQL查询构建器
class PostgreSQLQueryGenerator {
  static buildSelect(config: WidgetQueryGenerationConfig): QueryPayload {
    const { tableName, columns, where, orderBy, limit, offset } = config;

    let query = `SELECT ${columns.join(', ')} FROM ${tableName}`;

    if (where) {
      query += ` WHERE ${where}`;
    }

    if (orderBy) {
      query += ` ORDER BY ${orderBy}`;
    }

    if (limit) {
      query += ` LIMIT ${limit}`;
    }

    if (offset) {
      query += ` OFFSET ${offset}`;
    }

    return {
      type: QUERY_TYPE.SELECT,
      name: `Find_${tableName}`,
      actionConfiguration: {
        query: query,
        pluginSpecifiedTemplates: [{ value: false }]
      }
    };
  }

  static buildInsert(config: WidgetQueryGenerationConfig): QueryPayload {
    const { tableName, data } = config;
    const columns = Object.keys(data);
    const values = columns.map(col => `{{${data[col]}}}`);

    const query = `INSERT INTO ${tableName} (${columns.join(', ')})
                   VALUES (${values.join(', ')})`;

    return {
      type: QUERY_TYPE.CREATE,
      name: `Insert_${tableName}`,
      actionConfiguration: {
        query: query,
        pluginSpecifiedTemplates: [{ value: false }]
      }
    };
  }

  static buildUpdate(config: WidgetQueryGenerationConfig): QueryPayload {
    const { tableName, data, primaryColumn } = config;
    const setClause = Object.keys(data)
      .filter(col => col !== primaryColumn)
      .map(col => `${col} = {{${data[col]}}}`)
      .join(', ');

    const query = `UPDATE ${tableName}
                   SET ${setClause}
                   WHERE ${primaryColumn} = {{${data[primaryColumn]}}}`;

    return {
      type: QUERY_TYPE.UPDATE,
      name: `Update_${tableName}`,
      actionConfiguration: {
        query: query,
        pluginSpecifiedTemplates: [{ value: false }]
      }
    };
  }
}

// MySQL查询构建器
class MySQLQueryGenerator extends PostgreSQLQueryGenerator {
  // 继承PostgreSQL的大部分功能，重写特定语法
  static buildSelect(config: WidgetQueryGenerationConfig): QueryPayload {
    const baseQuery = super.buildSelect(config);
    // MySQL特定的语法调整
    return baseQuery;
  }
}

// MSSQL查询构建器
class MSSQLQueryGenerator {
  static buildSelect(config: WidgetQueryGenerationConfig): QueryPayload {
    const { tableName, columns, where, orderBy, limit, offset } = config;

    let query = `SELECT ${columns.join(', ')} FROM ${tableName}`;

    if (where) {
      query += ` WHERE ${where}`;
    }

    if (orderBy) {
      query += ` ORDER BY ${orderBy}`;
    }

    // MSSQL使用OFFSET...FETCH语法
    if (offset || limit) {
      query += ` OFFSET ${offset || 0} ROWS`;
      if (limit) {
        query += ` FETCH NEXT ${limit} ROWS ONLY`;
      }
    }

    return {
      type: QUERY_TYPE.SELECT,
      name: `Find_${tableName}`,
      actionConfiguration: {
        query: query,
        pluginSpecifiedTemplates: [{ value: false }]
      }
    };
  }
}
```

#### 6.1.2 NoSQL查询构建器
```typescript
// MongoDB查询构建器
class MongoDBQueryGenerator {
  static buildFind(config: WidgetQueryGenerationConfig): QueryPayload {
    const { tableName, where, orderBy, limit, offset } = config;

    return {
      type: QUERY_TYPE.SELECT,
      name: `Find_${tableName}`,
      formData: {
        find: {
          query: { data: where || "" },
          sort: { data: orderBy || "" },
          limit: { data: limit || "" },
          skip: { data: offset || "" }
        },
        collection: { data: tableName },
        command: { data: "FIND" }
      },
      dynamicBindingPathList: [
        { key: "formData.find.query.data" },
        { key: "formData.find.sort.data" },
        { key: "formData.find.limit.data" },
        { key: "formData.find.skip.data" }
      ]
    };
  }

  static buildInsert(config: WidgetQueryGenerationConfig): QueryPayload {
    const { tableName, data } = config;

    return {
      type: QUERY_TYPE.CREATE,
      name: `Insert_${tableName}`,
      formData: {
        insert: {
          documents: { data: JSON.stringify([data]) }
        },
        collection: { data: tableName },
        command: { data: "INSERT" }
      },
      dynamicBindingPathList: [
        { key: "formData.insert.documents.data" }
      ]
    };
  }

  static buildUpdate(config: WidgetQueryGenerationConfig): QueryPayload {
    const { tableName, data, primaryColumn } = config;
    const filter = { [primaryColumn]: `{{${data[primaryColumn]}}}` };
    const update = { $set: data };

    return {
      type: QUERY_TYPE.UPDATE,
      name: `Update_${tableName}`,
      formData: {
        updateMany: {
          query: { data: JSON.stringify(filter) },
          update: { data: JSON.stringify(update) }
        },
        collection: { data: tableName },
        command: { data: "UPDATE_MANY" }
      },
      dynamicBindingPathList: [
        { key: "formData.updateMany.query.data" },
        { key: "formData.updateMany.update.data" }
      ]
    };
  }

  static buildAggregate(config: WidgetQueryGenerationConfig): QueryPayload {
    const { tableName, pipeline } = config;

    return {
      type: QUERY_TYPE.SELECT,
      name: `Aggregate_${tableName}`,
      formData: {
        aggregate: {
          arrayPipelines: { data: JSON.stringify(pipeline) }
        },
        collection: { data: tableName },
        command: { data: "AGGREGATE" }
      },
      dynamicBindingPathList: [
        { key: "formData.aggregate.arrayPipelines.data" }
      ]
    };
  }
}
```

### 6.2 API查询构建器
```typescript
// REST API查询构建器
class RestAPIQueryGenerator {
  static buildGetRequest(config: APIQueryGenerationConfig): QueryPayload {
    const { endpoint, headers, params } = config;

    return {
      type: QUERY_TYPE.SELECT,
      name: `Get_${endpoint.replace(/[^a-zA-Z0-9]/g, '_')}`,
      actionConfiguration: {
        httpMethod: "GET",
        path: endpoint,
        headers: headers || [],
        queryParameters: params || [],
        timeoutInMillisecond: 10000,
        paginationType: "NONE"
      },
      dynamicBindingPathList: []
    };
  }

  static buildPostRequest(config: APIQueryGenerationConfig): QueryPayload {
    const { endpoint, headers, body } = config;

    return {
      type: QUERY_TYPE.CREATE,
      name: `Post_${endpoint.replace(/[^a-zA-Z0-9]/g, '_')}`,
      actionConfiguration: {
        httpMethod: "POST",
        path: endpoint,
        headers: [
          { key: "Content-Type", value: "application/json" },
          ...(headers || [])
        ],
        body: JSON.stringify(body),
        timeoutInMillisecond: 10000,
        paginationType: "NONE"
      },
      dynamicBindingPathList: [
        { key: "actionConfiguration.body" }
      ]
    };
  }

  static buildPutRequest(config: APIQueryGenerationConfig): QueryPayload {
    const { endpoint, headers, body } = config;

    return {
      type: QUERY_TYPE.UPDATE,
      name: `Put_${endpoint.replace(/[^a-zA-Z0-9]/g, '_')}`,
      actionConfiguration: {
        httpMethod: "PUT",
        path: endpoint,
        headers: [
          { key: "Content-Type", value: "application/json" },
          ...(headers || [])
        ],
        body: JSON.stringify(body),
        timeoutInMillisecond: 10000,
        paginationType: "NONE"
      },
      dynamicBindingPathList: [
        { key: "actionConfiguration.body" }
      ]
    };
  }

  static buildDeleteRequest(config: APIQueryGenerationConfig): QueryPayload {
    const { endpoint, headers, params } = config;

    return {
      type: QUERY_TYPE.DELETE,
      name: `Delete_${endpoint.replace(/[^a-zA-Z0-9]/g, '_')}`,
      actionConfiguration: {
        httpMethod: "DELETE",
        path: endpoint,
        headers: headers || [],
        queryParameters: params || [],
        timeoutInMillisecond: 10000,
        paginationType: "NONE"
      },
      dynamicBindingPathList: []
    };
  }
}

// GraphQL查询构建器
class GraphQLQueryGenerator {
  static buildQuery(config: GraphQLQueryGenerationConfig): QueryPayload {
    const { query, variables } = config;

    return {
      type: QUERY_TYPE.SELECT,
      name: `GraphQL_Query`,
      actionConfiguration: {
        body: query,
        variables: JSON.stringify(variables || {}),
        timeoutInMillisecond: 10000,
        paginationType: "NONE"
      },
      dynamicBindingPathList: [
        { key: "actionConfiguration.body" },
        { key: "actionConfiguration.variables" }
      ]
    };
  }

  static buildMutation(config: GraphQLQueryGenerationConfig): QueryPayload {
    const { mutation, variables } = config;

    return {
      type: QUERY_TYPE.CREATE,
      name: `GraphQL_Mutation`,
      actionConfiguration: {
        body: mutation,
        variables: JSON.stringify(variables || {}),
        timeoutInMillisecond: 10000,
        paginationType: "NONE"
      },
      dynamicBindingPathList: [
        { key: "actionConfiguration.body" },
        { key: "actionConfiguration.variables" }
      ]
    };
  }
}
```

## 7. 数据绑定和参数系统

### 7.1 动态参数绑定
```typescript
// 参数绑定接口
interface DynamicBinding {
  key: string;           // 绑定路径，如 "actionConfiguration.body"
  value?: string;        // 绑定表达式，如 "{{Table1.selectedRow.id}}"
}

// 参数绑定处理器
class ParameterBindingProcessor {
  static processBindings(
    action: Action,
    context: EvaluationContext
  ): Action {
    const processedAction = cloneDeep(action);

    action.dynamicBindingPathList?.forEach(binding => {
      const currentValue = get(processedAction, binding.key);
      if (typeof currentValue === 'string' && currentValue.includes('{{')) {
        const evaluatedValue = this.evaluateExpression(currentValue, context);
        set(processedAction, binding.key, evaluatedValue);
      }
    });

    return processedAction;
  }

  static evaluateExpression(expression: string, context: EvaluationContext): any {
    // 提取绑定表达式
    const bindingRegex = /\{\{([^}]+)\}\}/g;
    let result = expression;
    let match;

    while ((match = bindingRegex.exec(expression)) !== null) {
      const bindingExpression = match[1].trim();
      const evaluatedValue = this.evaluateBinding(bindingExpression, context);
      result = result.replace(match[0], evaluatedValue);
    }

    return result;
  }

  private static evaluateBinding(expression: string, context: EvaluationContext): any {
    try {
      // 安全的表达式求值
      const func = new Function('context', `
        with(context) {
          return ${expression};
        }
      `);
      return func(context);
    } catch (error) {
      console.error(`Error evaluating binding: ${expression}`, error);
      return `{{${expression}}}`;
    }
  }
}

// 评估上下文
interface EvaluationContext {
  // 组件数据
  [widgetName: string]: {
    data?: any[];
    selectedRow?: any;
    selectedRows?: any[];
    searchText?: string;
    pageNo?: number;
    pageSize?: number;
  };

  // 全局变量
  appsmith: {
    user: User;
    store: Record<string, any>;
    URL: {
      queryParams: Record<string, string>;
      pathname: string;
    };
    theme: AppTheme;
  };

  // API响应数据
  [actionName: string]: {
    data?: any;
    isLoading?: boolean;
    error?: any;
    responseMeta?: {
      statusCode?: number;
      headers?: Record<string, string>;
    };
  };
}
```

### 7.2 查询模板系统
```typescript
// 查询模板接口
interface QueryTemplate {
  id: string;
  name: string;
  description: string;
  pluginType: PluginType;
  category: string;
  template: {
    actionConfiguration: any;
    dynamicBindingPathList: DynamicBinding[];
  };
  variables: TemplateVariable[];
}

interface TemplateVariable {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object';
  description: string;
  defaultValue?: any;
  required: boolean;
}

// 预定义查询模板
const QUERY_TEMPLATES: QueryTemplate[] = [
  {
    id: 'postgres_select_all',
    name: '查询所有记录',
    description: '从指定表中查询所有记录',
    pluginType: PluginType.DB,
    category: 'PostgreSQL',
    template: {
      actionConfiguration: {
        query: 'SELECT * FROM {{tableName}} LIMIT {{limit}};',
        pluginSpecifiedTemplates: [{ value: false }]
      },
      dynamicBindingPathList: [
        { key: 'actionConfiguration.query' }
      ]
    },
    variables: [
      {
        name: 'tableName',
        type: 'string',
        description: '表名',
        required: true
      },
      {
        name: 'limit',
        type: 'number',
        description: '限制记录数',
        defaultValue: 100,
        required: false
      }
    ]
  },
  {
    id: 'postgres_select_by_id',
    name: '根据ID查询',
    description: '根据主键ID查询单条记录',
    pluginType: PluginType.DB,
    category: 'PostgreSQL',
    template: {
      actionConfiguration: {
        query: 'SELECT * FROM {{tableName}} WHERE {{idColumn}} = {{idValue}};',
        pluginSpecifiedTemplates: [{ value: false }]
      },
      dynamicBindingPathList: [
        { key: 'actionConfiguration.query' }
      ]
    },
    variables: [
      {
        name: 'tableName',
        type: 'string',
        description: '表名',
        required: true
      },
      {
        name: 'idColumn',
        type: 'string',
        description: 'ID列名',
        defaultValue: 'id',
        required: true
      },
      {
        name: 'idValue',
        type: 'string',
        description: 'ID值',
        required: true
      }
    ]
  },
  {
    id: 'rest_api_get',
    name: 'GET请求',
    description: '发送GET请求获取数据',
    pluginType: PluginType.API,
    category: 'REST API',
    template: {
      actionConfiguration: {
        httpMethod: 'GET',
        path: '{{endpoint}}',
        headers: [],
        queryParameters: [],
        timeoutInMillisecond: 10000,
        paginationType: 'NONE'
      },
      dynamicBindingPathList: [
        { key: 'actionConfiguration.path' }
      ]
    },
    variables: [
      {
        name: 'endpoint',
        type: 'string',
        description: 'API端点路径',
        required: true
      }
    ]
  },
  {
    id: 'mongodb_find',
    name: 'MongoDB查询',
    description: '在MongoDB集合中查找文档',
    pluginType: PluginType.DB,
    category: 'MongoDB',
    template: {
      actionConfiguration: {
        formData: {
          find: {
            query: { data: '{{filter}}' },
            sort: { data: '{{sort}}' },
            limit: { data: '{{limit}}' }
          },
          collection: { data: '{{collection}}' },
          command: { data: 'FIND' }
        }
      },
      dynamicBindingPathList: [
        { key: 'actionConfiguration.formData.find.query.data' },
        { key: 'actionConfiguration.formData.find.sort.data' },
        { key: 'actionConfiguration.formData.find.limit.data' },
        { key: 'actionConfiguration.formData.collection.data' }
      ]
    },
    variables: [
      {
        name: 'collection',
        type: 'string',
        description: '集合名称',
        required: true
      },
      {
        name: 'filter',
        type: 'object',
        description: '查询过滤条件',
        defaultValue: '{}',
        required: false
      },
      {
        name: 'sort',
        type: 'object',
        description: '排序条件',
        defaultValue: '{}',
        required: false
      },
      {
        name: 'limit',
        type: 'number',
        description: '限制记录数',
        defaultValue: 100,
        required: false
      }
    ]
  }
];

// 模板应用器
class QueryTemplateApplier {
  static applyTemplate(
    template: QueryTemplate,
    variables: Record<string, any>
  ): Partial<Action> {
    // 验证必需变量
    const missingVariables = template.variables
      .filter(v => v.required && !variables.hasOwnProperty(v.name))
      .map(v => v.name);

    if (missingVariables.length > 0) {
      throw new Error(`Missing required variables: ${missingVariables.join(', ')}`);
    }

    // 应用默认值
    const finalVariables = { ...variables };
    template.variables.forEach(v => {
      if (!finalVariables.hasOwnProperty(v.name) && v.defaultValue !== undefined) {
        finalVariables[v.name] = v.defaultValue;
      }
    });

    // 替换模板中的变量
    const actionConfig = this.replaceTemplateVariables(
      template.template.actionConfiguration,
      finalVariables
    );

    return {
      name: template.name,
      pluginType: template.pluginType,
      actionConfiguration: actionConfig,
      dynamicBindingPathList: template.template.dynamicBindingPathList
    };
  }

  private static replaceTemplateVariables(
    config: any,
    variables: Record<string, any>
  ): any {
    const configStr = JSON.stringify(config);
    let result = configStr;

    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
      result = result.replace(regex, String(value));
    });

    return JSON.parse(result);
  }
}
