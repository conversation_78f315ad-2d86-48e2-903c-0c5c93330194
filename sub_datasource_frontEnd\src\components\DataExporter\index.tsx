import React from 'react';
import { Card, Empty } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';

interface DataExporterProps {
  data?: any[];
  format?: 'csv' | 'excel' | 'json';
}

const DataExporter: React.FC<DataExporterProps> = ({ data, format = 'csv' }) => {
  return (
    <Card title="数据导出" extra={<DownloadOutlined />}>
      <Empty
        description="数据导出组件开发中..."
        image={Empty.PRESENTED_IMAGE_SIMPLE}
      />
    </Card>
  );
};

export default DataExporter;
