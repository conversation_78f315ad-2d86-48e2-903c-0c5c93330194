import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Tag,
  Button,
  Space,
  Input,
  Select,
  Modal,
  Typography,
  Divider,
  Badge,
  Tooltip,
  message
} from 'antd';
import {
  AppstoreOutlined,
  SearchOutlined,
  InfoCircleOutlined,
  DownloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CodeOutlined,
  DatabaseOutlined,
  CloudOutlined,
  ApiOutlined,
  RobotOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

import { Plugin } from '../../types/api';
import { apiClient } from '../../services/apiClient';
import { LoadingSpinner } from '../../components/Common/LoadingSpinner';
import { useTheme } from '../../hooks/useTheme';

const { Search } = Input;
const { Option } = Select;
const { Text, Paragraph, Title } = Typography;

// 样式化组件
const PageContainer = styled.div`
  padding: 24px;
`;

const PageHeader = styled.div`
  margin-bottom: 24px;
`;

const PageTitle = styled.h1<{ $isDark: boolean }>`
  font-size: 24px;
  font-weight: 600;
  color: ${props => props.$isDark ? '#ffffff' : '#262626'};
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
`;

const FilterSection = styled.div`
  margin-bottom: 24px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
`;

const PluginCard = styled(Card)<{ $status: string }>`
  height: 100%;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    border-color: #1890ff;
  }
  
  .ant-card-head {
    border-bottom: 1px solid #f0f0f0;
    min-height: 60px;
  }
  
  .ant-card-body {
    height: 200px;
    display: flex;
    flex-direction: column;
  }
  
  ${props => props.$status === 'deprecated' && `
    opacity: 0.6;
    &:hover {
      transform: none;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      border-color: transparent;
    }
  `}
`;

const PluginIcon = styled.div<{ $category: string }>`
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  margin-bottom: 12px;
  
  ${props => {
    switch (props.$category) {
      case 'database':
        return 'background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);';
      case 'api':
        return 'background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);';
      case 'cloud':
        return 'background: linear-gradient(135deg, #722ed1 0%, #531dab 100%);';
      case 'ai':
        return 'background: linear-gradient(135deg, #fa8c16 0%, #d46b08 100%);';
      default:
        return 'background: linear-gradient(135deg, #8c8c8c 0%, #595959 100%);';
    }
  }}
`;

const PluginDescription = styled.div`
  flex: 1;
  margin-bottom: 16px;
`;

const PluginFooter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
`;

const StatusTag = styled(Tag)<{ $status: string }>`
  ${props => {
    switch (props.$status) {
      case 'active':
        return `
          background-color: #f6ffed;
          border-color: #b7eb8f;
          color: #52c41a;
        `;
      case 'inactive':
        return `
          background-color: #fff7e6;
          border-color: #ffd591;
          color: #fa8c16;
        `;
      case 'deprecated':
        return `
          background-color: #fff2f0;
          border-color: #ffccc7;
          color: #ff4d4f;
        `;
      default:
        return '';
    }
  }}
`;

interface PluginManagementProps {}

const PluginManagement: React.FC<PluginManagementProps> = () => {
  const { isDark } = useTheme();
  const [plugins, setPlugins] = useState<Plugin[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [selectedPlugin, setSelectedPlugin] = useState<Plugin | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);

  // 加载插件列表
  const loadPlugins = async () => {
    try {
      setLoading(true);
      const response = await apiClient.get('/plugins', {
        params: {
          category: selectedCategory || undefined,
          status: selectedStatus || undefined
        }
      });
      setPlugins(response.data);
    } catch (error) {
      message.error('加载插件列表失败');
      console.error('Load plugins error:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadPlugins();
  }, [selectedCategory, selectedStatus]);

  // 获取分类图标
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'database':
        return <DatabaseOutlined />;
      case 'api':
        return <ApiOutlined />;
      case 'cloud':
        return <CloudOutlined />;
      case 'ai':
        return <RobotOutlined />;
      default:
        return <AppstoreOutlined />;
    }
  };

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'inactive':
        return <ExclamationCircleOutlined style={{ color: '#fa8c16' }} />;
      case 'deprecated':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return null;
    }
  };

  // 过滤插件
  const filteredPlugins = plugins.filter(plugin => {
    const matchesSearch = !searchText || 
      plugin.name.toLowerCase().includes(searchText.toLowerCase()) ||
      plugin.displayName.toLowerCase().includes(searchText.toLowerCase()) ||
      plugin.description.toLowerCase().includes(searchText.toLowerCase());
    
    return matchesSearch;
  });

  // 统计数据
  const stats = {
    total: plugins.length,
    active: plugins.filter(p => p.status === 'active').length,
    inactive: plugins.filter(p => p.status === 'inactive').length,
    deprecated: plugins.filter(p => p.status === 'deprecated').length
  };

  // 分类统计
  const categoryStats = plugins.reduce((acc, plugin) => {
    acc[plugin.category] = (acc[plugin.category] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // 查看插件详情
  const viewPluginDetail = (plugin: Plugin) => {
    setSelectedPlugin(plugin);
    setDetailModalVisible(true);
  };

  if (loading) {
    return <LoadingSpinner text="加载插件列表中..." />;
  }

  return (
    <PageContainer>
      <PageHeader>
        <PageTitle $isDark={isDark}>
          <AppstoreOutlined />
          插件管理
        </PageTitle>
        <Text type="secondary">
          管理和配置数据源插件，支持多种数据库和API服务连接
        </Text>
      </PageHeader>

      {/* 统计信息 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 600, color: '#1890ff' }}>
                {stats.total}
              </div>
              <div style={{ color: '#8c8c8c' }}>总插件数</div>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 600, color: '#52c41a' }}>
                {stats.active}
              </div>
              <div style={{ color: '#8c8c8c' }}>活跃插件</div>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 600, color: '#fa8c16' }}>
                {stats.inactive}
              </div>
              <div style={{ color: '#8c8c8c' }}>未激活</div>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 600, color: '#ff4d4f' }}>
                {stats.deprecated}
              </div>
              <div style={{ color: '#8c8c8c' }}>已废弃</div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 过滤器 */}
      <FilterSection>
        <Row gutter={16} align="middle">
          <Col span={8}>
            <Search
              placeholder="搜索插件名称或描述"
              allowClear
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              prefix={<SearchOutlined />}
            />
          </Col>
          <Col span={6}>
            <Select
              placeholder="选择分类"
              allowClear
              style={{ width: '100%' }}
              value={selectedCategory}
              onChange={setSelectedCategory}
            >
              <Option value="database">数据库 ({categoryStats.database || 0})</Option>
              <Option value="api">API服务 ({categoryStats.api || 0})</Option>
              <Option value="cloud">云服务 ({categoryStats.cloud || 0})</Option>
              <Option value="ai">AI服务 ({categoryStats.ai || 0})</Option>
            </Select>
          </Col>
          <Col span={6}>
            <Select
              placeholder="选择状态"
              allowClear
              style={{ width: '100%' }}
              value={selectedStatus}
              onChange={setSelectedStatus}
            >
              <Option value="active">活跃 ({stats.active})</Option>
              <Option value="inactive">未激活 ({stats.inactive})</Option>
              <Option value="deprecated">已废弃 ({stats.deprecated})</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Text type="secondary">
              共找到 {filteredPlugins.length} 个插件
            </Text>
          </Col>
        </Row>
      </FilterSection>

      {/* 插件列表 */}
      <Row gutter={[16, 16]}>
        {filteredPlugins.map(plugin => (
          <Col key={plugin.id} xs={24} sm={12} md={8} lg={6}>
            <PluginCard
              $status={plugin.status}
              onClick={() => viewPluginDetail(plugin)}
              title={
                <Space>
                  <span>{plugin.displayName}</span>
                  <Badge
                    count={getStatusIcon(plugin.status)}
                    style={{ backgroundColor: 'transparent' }}
                  />
                </Space>
              }
              extra={
                <StatusTag $status={plugin.status}>
                  {plugin.status === 'active' ? '活跃' : 
                   plugin.status === 'inactive' ? '未激活' : '已废弃'}
                </StatusTag>
              }
            >
              <PluginIcon $category={plugin.category}>
                {getCategoryIcon(plugin.category)}
              </PluginIcon>
              
              <PluginDescription>
                <Paragraph
                  ellipsis={{ rows: 3, tooltip: plugin.description }}
                  style={{ margin: 0, fontSize: '14px' }}
                >
                  {plugin.description}
                </Paragraph>
              </PluginDescription>

              <PluginFooter>
                <Space>
                  <Tag color="blue">{plugin.category}</Tag>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    v{plugin.version}
                  </Text>
                </Space>
                <Tooltip title="查看详情">
                  <Button
                    type="text"
                    size="small"
                    icon={<InfoCircleOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      viewPluginDetail(plugin);
                    }}
                  />
                </Tooltip>
              </PluginFooter>
            </PluginCard>
          </Col>
        ))}
      </Row>

      {/* 插件详情对话框 */}
      <Modal
        title={
          <Space>
            {selectedPlugin && getCategoryIcon(selectedPlugin.category)}
            {selectedPlugin?.displayName}
            <StatusTag $status={selectedPlugin?.status || ''}>
              {selectedPlugin?.status === 'active' ? '活跃' : 
               selectedPlugin?.status === 'inactive' ? '未激活' : '已废弃'}
            </StatusTag>
          </Space>
        }
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>,
          <Button
            key="docs"
            type="primary"
            icon={<InfoCircleOutlined />}
            onClick={() => {
              if (selectedPlugin?.documentationUrl) {
                window.open(selectedPlugin.documentationUrl, '_blank');
              }
            }}
            disabled={!selectedPlugin?.documentationUrl}
          >
            查看文档
          </Button>
        ]}
        width={800}
      >
        {selectedPlugin && (
          <div>
            <Paragraph>{selectedPlugin.description}</Paragraph>
            
            <Divider orientation="left">基本信息</Divider>
            <Row gutter={16}>
              <Col span={12}>
                <Text strong>插件ID: </Text>
                <Text code>{selectedPlugin.id}</Text>
              </Col>
              <Col span={12}>
                <Text strong>版本: </Text>
                <Tag color="blue">v{selectedPlugin.version}</Tag>
              </Col>
              <Col span={12}>
                <Text strong>分类: </Text>
                <Tag color="green">{selectedPlugin.category}</Tag>
              </Col>
              <Col span={12}>
                <Text strong>支持操作: </Text>
                {selectedPlugin.supportedOperations.map(op => (
                  <Tag key={op} color="orange">{op}</Tag>
                ))}
              </Col>
            </Row>

            <Divider orientation="left">查询模板</Divider>
            <Row gutter={16}>
              {Object.entries(selectedPlugin.templates).map(([key, template]) => (
                <Col key={key} span={24} style={{ marginBottom: 8 }}>
                  <Card size="small">
                    <Text strong>{key}: </Text>
                    <Text code style={{ fontSize: '12px' }}>{template}</Text>
                  </Card>
                </Col>
              ))}
            </Row>
          </div>
        )}
      </Modal>
    </PageContainer>
  );
};

export default PluginManagement;
