# If someone with write access comments "/ok-to-test" on a pull request, emit a repository_dispatch event
name: Ok To Test

on:
  issue_comment:
    types: [created]

jobs:
  ok-to-test:
    runs-on: ubuntu-latest
    # Only run for PRs, not issue comments
    if: |
      github.event.issue.pull_request
    steps:
      - name: Generate token
        id: generate_token
        uses: tibdex/github-app-token@v1
        with:
          app_id: ${{ secrets.APPSMITH_INTEGRATION_TESTING_ID }}
          private_key: ${{ secrets.APPSMITH_INTEGRATION_TESTING_KEY }}

      - name: Slash Command Dispatch
        uses: peter-evans/slash-command-dispatch@v3
        env:
          TOKEN: ${{ steps.generate_token.outputs.token }}
        with:
          token: ${{ env.TOKEN }} # GitHub App installation access token
          reaction-token: ${{ secrets.GITHUB_TOKEN }}
          issue-type: pull-request
          commands: |
            ok-to-test
            perf-test
            ci-merge-check
            ci-test-limit
            ok-to-test-with-documentdb
            build-deploy-preview
          permission: write
