# 数据集模块微前端架构设计

## 1. 数据集微前端概述

数据集模块作为PagePlug的第二个微前端子应用，负责管理API接口、数据库查询、JS对象等数据集合功能。采用React18 + Module Federation架构，确保与主应用的技术栈一致性和样式隔离。

### 1.1 模块边界定义
```
数据集微前端边界:
├── API管理 (REST API, GraphQL)
├── 数据库查询 (SQL, NoSQL)
├── JS对象管理
├── 查询构建器
├── 数据集合管理
├── 查询执行和结果处理
└── 查询模板系统
```

### 1.2 与主应用的交互
```
主应用 ↔ 数据集微前端:
├── 用户认证状态共享
├── 工作空间上下文共享
├── 应用配置共享
├── 事件通信 (查询执行、结果更新)
├── 路由导航
└── 主题样式同步
```

## 2. React18微前端架构

### 2.1 Module Federation配置

#### 2.1.1 主应用配置
```javascript
// packages/shell-app/webpack.config.js
const ModuleFederationPlugin = require('@module-federation/webpack')

module.exports = {
  mode: 'development',
  devServer: {
    port: 3000,
  },
  plugins: [
    new ModuleFederationPlugin({
      name: 'shell',
      remotes: {
        datasourceApp: 'datasourceApp@http://localhost:3001/remoteEntry.js',
        datasetApp: 'datasetApp@http://localhost:3002/remoteEntry.js', // 数据集应用
        userApp: 'userApp@http://localhost:3003/remoteEntry.js'
      },
      shared: {
        react: { 
          singleton: true, 
          requiredVersion: '^18.2.0',
          eager: true 
        },
        'react-dom': { 
          singleton: true, 
          requiredVersion: '^18.2.0',
          eager: true 
        },
        '@reduxjs/toolkit': { 
          singleton: true,
          requiredVersion: '^1.9.0' 
        },
        'react-router-dom': { 
          singleton: true,
          requiredVersion: '^6.0.0' 
        },
        'antd': { 
          singleton: true,
          requiredVersion: '^5.0.0' 
        },
        'styled-components': { 
          singleton: true,
          requiredVersion: '^6.0.0' 
        }
      }
    })
  ]
}
```

#### 2.1.2 数据集微前端配置
```javascript
// packages/dataset-app/webpack.config.js
const ModuleFederationPlugin = require('@module-federation/webpack')

module.exports = {
  mode: 'development',
  devServer: {
    port: 3002,
  },
  plugins: [
    new ModuleFederationPlugin({
      name: 'datasetApp',
      filename: 'remoteEntry.js',
      exposes: {
        './DatasetApp': './src/App',
        './DatasetRoutes': './src/routes',
        './DatasetStore': './src/store',
        './APIEditor': './src/components/APIEditor',
        './QueryEditor': './src/components/QueryEditor',
        './JSEditor': './src/components/JSEditor',
        './QueryBuilder': './src/components/QueryBuilder',
        './CollectionManager': './src/components/CollectionManager'
      },
      shared: {
        react: { 
          singleton: true, 
          requiredVersion: '^18.2.0' 
        },
        'react-dom': { 
          singleton: true, 
          requiredVersion: '^18.2.0' 
        },
        '@reduxjs/toolkit': { 
          singleton: true,
          requiredVersion: '^1.9.0' 
        },
        'react-router-dom': { 
          singleton: true,
          requiredVersion: '^6.0.0' 
        },
        'antd': { 
          singleton: true,
          requiredVersion: '^5.0.0' 
        },
        'styled-components': { 
          singleton: true,
          requiredVersion: '^6.0.0' 
        }
      }
    })
  ]
}
```

### 2.2 项目结构设计

```
packages/dataset-app/
├── public/
│   ├── index.html
│   └── favicon.ico
├── src/
│   ├── components/                    # 组件库
│   │   ├── APIEditor/                # API编辑器组件
│   │   │   ├── index.tsx
│   │   │   ├── RestAPIForm.tsx
│   │   │   ├── GraphQLForm.tsx
│   │   │   ├── RequestBuilder.tsx
│   │   │   ├── ResponseViewer.tsx
│   │   │   └── styles.ts
│   │   ├── QueryEditor/              # 查询编辑器组件
│   │   │   ├── index.tsx
│   │   │   ├── SQLEditor.tsx
│   │   │   ├── NoSQLEditor.tsx
│   │   │   ├── QueryBuilder.tsx
│   │   │   ├── ResultViewer.tsx
│   │   │   └── styles.ts
│   │   ├── JSEditor/                 # JS对象编辑器
│   │   │   ├── index.tsx
│   │   │   ├── CodeEditor.tsx
│   │   │   ├── FunctionManager.tsx
│   │   │   ├── VariableManager.tsx
│   │   │   └── styles.ts
│   │   ├── CollectionManager/        # 数据集合管理
│   │   │   ├── index.tsx
│   │   │   ├── CollectionList.tsx
│   │   │   ├── CollectionForm.tsx
│   │   │   └── styles.ts
│   │   ├── QueryBuilder/             # 查询构建器
│   │   │   ├── index.tsx
│   │   │   ├── SQLBuilder.tsx
│   │   │   ├── NoSQLBuilder.tsx
│   │   │   ├── APIBuilder.tsx
│   │   │   └── styles.ts
│   │   └── shared/                   # 共享组件
│   │       ├── CodeEditor/
│   │       ├── DataViewer/
│   │       ├── ParameterInput/
│   │       └── ExecutionButton/
│   ├── pages/                        # 页面组件
│   │   ├── APIList/                  # API列表页
│   │   ├── APIDetail/                # API详情页
│   │   ├── QueryList/                # 查询列表页
│   │   ├── QueryDetail/              # 查询详情页
│   │   ├── JSObjectList/             # JS对象列表页
│   │   ├── JSObjectDetail/           # JS对象详情页
│   │   └── CollectionList/           # 集合列表页
│   ├── store/                        # 状态管理
│   │   ├── index.ts                  # Store配置
│   │   ├── slices/                   # Redux Slices
│   │   │   ├── apiSlice.ts
│   │   │   ├── querySlice.ts
│   │   │   ├── jsObjectSlice.ts
│   │   │   ├── collectionSlice.ts
│   │   │   └── executionSlice.ts
│   │   └── middleware/               # 中间件
│   │       ├── apiMiddleware.ts
│   │       └── executionMiddleware.ts
│   ├── services/                     # 服务层
│   │   ├── api/                      # API服务
│   │   │   ├── actionAPI.ts
│   │   │   ├── collectionAPI.ts
│   │   │   ├── executionAPI.ts
│   │   │   └── templateAPI.ts
│   │   ├── builders/                 # 查询构建器
│   │   │   ├── SQLBuilder.ts
│   │   │   ├── MongoDBBuilder.ts
│   │   │   ├── RestAPIBuilder.ts
│   │   │   └── GraphQLBuilder.ts
│   │   └── executors/                # 执行器
│   │       ├── QueryExecutor.ts
│   │       ├── APIExecutor.ts
│   │       └── JSExecutor.ts
│   ├── hooks/                        # 自定义Hooks
│   │   ├── useAction.ts
│   │   ├── useCollection.ts
│   │   ├── useExecution.ts
│   │   ├── useQueryBuilder.ts
│   │   └── useParameterBinding.ts
│   ├── utils/                        # 工具函数
│   │   ├── parameterBinding.ts
│   │   ├── queryValidation.ts
│   │   ├── responseFormatter.ts
│   │   └── templateProcessor.ts
│   ├── types/                        # 类型定义
│   │   ├── action.ts
│   │   ├── collection.ts
│   │   ├── execution.ts
│   │   └── template.ts
│   ├── styles/                       # 样式文件
│   │   ├── global.ts                 # 全局样式
│   │   ├── theme.ts                  # 主题配置
│   │   └── components.ts             # 组件样式
│   ├── routes/                       # 路由配置
│   │   ├── index.tsx
│   │   └── routes.tsx
│   ├── App.tsx                       # 应用入口
│   ├── index.tsx                     # 渲染入口
│   └── bootstrap.tsx                 # 启动文件
├── package.json
├── webpack.config.js
├── tsconfig.json
└── README.md
```

## 3. 样式隔离策略

### 3.1 CSS-in-JS方案 (推荐)
```typescript
// src/styles/global.ts
import styled, { createGlobalStyle } from 'styled-components'

// 全局样式重置，仅作用于数据集微应用
export const GlobalStyle = createGlobalStyle`
  .dataset-app {
    /* 样式重置，避免与主应用冲突 */
    * {
      box-sizing: border-box;
    }
    
    /* CSS变量定义 */
    --dataset-primary: #1890ff;
    --dataset-success: #52c41a;
    --dataset-warning: #faad14;
    --dataset-error: #ff4d4f;
    --dataset-text: #262626;
    --dataset-text-secondary: #8c8c8c;
    --dataset-bg: #ffffff;
    --dataset-bg-secondary: #fafafa;
    --dataset-border: #d9d9d9;
    --dataset-border-radius: 6px;
    --dataset-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    
    /* 字体设置 */
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 
                 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
    font-size: 14px;
    line-height: 1.5715;
    color: var(--dataset-text);
  }
`

// 应用容器样式
export const AppContainer = styled.div`
  min-height: 100vh;
  background: var(--dataset-bg);
  
  /* 确保样式隔离 */
  &.dataset-app {
    isolation: isolate;
  }
`

// 页面容器样式
export const PageContainer = styled.div`
  padding: 24px;
  background: var(--dataset-bg);
  min-height: calc(100vh - 64px);
`

// 卡片容器样式
export const CardContainer = styled.div`
  background: var(--dataset-bg);
  border: 1px solid var(--dataset-border);
  border-radius: var(--dataset-border-radius);
  box-shadow: var(--dataset-shadow);
  padding: 24px;
  margin-bottom: 16px;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
`
```

### 3.2 组件样式隔离
```typescript
// src/components/APIEditor/styles.ts
import styled from 'styled-components'

export const APIEditorContainer = styled.div`
  .api-editor {
    background: var(--dataset-bg);
    border-radius: var(--dataset-border-radius);
    overflow: hidden;
    
    .editor-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 24px;
      background: var(--dataset-bg-secondary);
      border-bottom: 1px solid var(--dataset-border);
      
      .title {
        font-size: 16px;
        font-weight: 600;
        color: var(--dataset-text);
        margin: 0;
      }
      
      .actions {
        display: flex;
        gap: 8px;
      }
    }
    
    .editor-content {
      padding: 24px;
      
      .form-section {
        margin-bottom: 24px;
        
        .section-title {
          font-size: 14px;
          font-weight: 600;
          color: var(--dataset-text);
          margin-bottom: 12px;
        }
        
        .form-row {
          display: flex;
          gap: 16px;
          margin-bottom: 16px;
          
          .form-item {
            flex: 1;
            
            label {
              display: block;
              font-size: 14px;
              color: var(--dataset-text);
              margin-bottom: 4px;
            }
            
            input, select, textarea {
              width: 100%;
              padding: 8px 12px;
              border: 1px solid var(--dataset-border);
              border-radius: var(--dataset-border-radius);
              font-size: 14px;
              
              &:focus {
                outline: none;
                border-color: var(--dataset-primary);
                box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
              }
            }
          }
        }
      }
    }
    
    .editor-footer {
      padding: 16px 24px;
      background: var(--dataset-bg-secondary);
      border-top: 1px solid var(--dataset-border);
      display: flex;
      justify-content: flex-end;
      gap: 8px;
    }
  }
`

export const QueryEditorContainer = styled.div`
  .query-editor {
    display: flex;
    flex-direction: column;
    height: 100%;
    
    .editor-toolbar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      background: var(--dataset-bg-secondary);
      border-bottom: 1px solid var(--dataset-border);
      
      .toolbar-left {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .datasource-selector {
          min-width: 200px;
        }
        
        .query-type-selector {
          min-width: 120px;
        }
      }
      
      .toolbar-right {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
    
    .editor-body {
      flex: 1;
      display: flex;
      
      .query-input {
        flex: 1;
        border-right: 1px solid var(--dataset-border);
        
        .code-editor {
          height: 100%;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 13px;
        }
      }
      
      .query-result {
        flex: 1;
        display: flex;
        flex-direction: column;
        
        .result-tabs {
          border-bottom: 1px solid var(--dataset-border);
        }
        
        .result-content {
          flex: 1;
          padding: 16px;
          overflow: auto;
        }
      }
    }
  }
`

export const JSEditorContainer = styled.div`
  .js-editor {
    height: 100%;
    display: flex;
    flex-direction: column;
    
    .editor-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 24px;
      background: var(--dataset-bg-secondary);
      border-bottom: 1px solid var(--dataset-border);
      
      .object-name {
        font-size: 18px;
        font-weight: 600;
        color: var(--dataset-text);
      }
      
      .editor-actions {
        display: flex;
        gap: 8px;
      }
    }
    
    .editor-content {
      flex: 1;
      display: flex;
      
      .code-panel {
        flex: 2;
        border-right: 1px solid var(--dataset-border);
        
        .code-editor {
          height: 100%;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 14px;
        }
      }
      
      .info-panel {
        flex: 1;
        display: flex;
        flex-direction: column;
        
        .panel-tabs {
          border-bottom: 1px solid var(--dataset-border);
        }
        
        .panel-content {
          flex: 1;
          padding: 16px;
          overflow: auto;
        }
      }
    }
  }
`
```

### 3.3 主题同步机制
```typescript
// src/hooks/useTheme.ts
import { useEffect, useState } from 'react'
import { eventBus } from '@pageplug/shared-utils'

interface ThemeConfig {
  primaryColor: string;
  successColor: string;
  warningColor: string;
  errorColor: string;
  textColor: string;
  backgroundColor: string;
  borderColor: string;
  borderRadius: string;
  fontFamily: string;
}

export const useTheme = () => {
  const [theme, setTheme] = useState<ThemeConfig>({
    primaryColor: '#1890ff',
    successColor: '#52c41a',
    warningColor: '#faad14',
    errorColor: '#ff4d4f',
    textColor: '#262626',
    backgroundColor: '#ffffff',
    borderColor: '#d9d9d9',
    borderRadius: '6px',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
  })
  
  useEffect(() => {
    // 监听主应用主题变化
    const handleThemeChange = (newTheme: ThemeConfig) => {
      setTheme(newTheme)
      updateCSSVariables(newTheme)
    }
    
    eventBus.on('theme:changed', handleThemeChange)
    
    // 获取初始主题
    eventBus.emit('theme:get', (initialTheme: ThemeConfig) => {
      setTheme(initialTheme)
      updateCSSVariables(initialTheme)
    })
    
    return () => {
      eventBus.off('theme:changed', handleThemeChange)
    }
  }, [])
  
  const updateCSSVariables = (themeConfig: ThemeConfig) => {
    const root = document.documentElement
    root.style.setProperty('--dataset-primary', themeConfig.primaryColor)
    root.style.setProperty('--dataset-success', themeConfig.successColor)
    root.style.setProperty('--dataset-warning', themeConfig.warningColor)
    root.style.setProperty('--dataset-error', themeConfig.errorColor)
    root.style.setProperty('--dataset-text', themeConfig.textColor)
    root.style.setProperty('--dataset-bg', themeConfig.backgroundColor)
    root.style.setProperty('--dataset-border', themeConfig.borderColor)
    root.style.setProperty('--dataset-border-radius', themeConfig.borderRadius)
  }
  
  return { theme, updateTheme: setTheme }
}
```

## 4. 状态管理架构

### 4.1 Redux Store配置
```typescript
// src/store/index.ts
import { configureStore } from '@reduxjs/toolkit'
import { apiSlice } from './slices/apiSlice'
import { querySlice } from './slices/querySlice'
import { jsObjectSlice } from './slices/jsObjectSlice'
import { collectionSlice } from './slices/collectionSlice'
import { executionSlice } from './slices/executionSlice'
import { apiMiddleware } from './middleware/apiMiddleware'
import { executionMiddleware } from './middleware/executionMiddleware'

export const store = configureStore({
  reducer: {
    api: apiSlice.reducer,
    query: querySlice.reducer,
    jsObject: jsObjectSlice.reducer,
    collection: collectionSlice.reducer,
    execution: executionSlice.reducer
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['execution/executeAction/pending']
      }
    }).concat(apiMiddleware, executionMiddleware)
})

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
```

### 4.2 Action管理Slice
```typescript
// src/store/slices/apiSlice.ts
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { actionAPI } from '../../services/api/actionAPI'
import type { Action, ActionCreateRequest, ActionUpdateRequest } from '../../types/action'

interface APIState {
  actions: Record<string, Action>;
  currentAction: Action | null;
  loading: boolean;
  error: string | null;
  filters: {
    pluginType?: string;
    datasourceId?: string;
    searchText?: string;
  };
}

const initialState: APIState = {
  actions: {},
  currentAction: null,
  loading: false,
  error: null,
  filters: {}
}

// 异步Actions
export const fetchActions = createAsyncThunk(
  'api/fetchActions',
  async (applicationId: string) => {
    const response = await actionAPI.getActions(applicationId)
    return response.data
  }
)

export const createAction = createAsyncThunk(
  'api/createAction',
  async (request: ActionCreateRequest) => {
    const response = await actionAPI.createAction(request)
    return response.data
  }
)

export const updateAction = createAsyncThunk(
  'api/updateAction',
  async ({ id, data }: { id: string; data: ActionUpdateRequest }) => {
    const response = await actionAPI.updateAction(id, data)
    return response.data
  }
)

export const deleteAction = createAsyncThunk(
  'api/deleteAction',
  async (id: string) => {
    await actionAPI.deleteAction(id)
    return id
  }
)

export const executeAction = createAsyncThunk(
  'api/executeAction',
  async ({ id, params }: { id: string; params?: Record<string, any> }) => {
    const response = await actionAPI.executeAction(id, params)
    return { actionId: id, result: response.data }
  }
)

const apiSlice = createSlice({
  name: 'api',
  initialState,
  reducers: {
    setCurrentAction: (state, action: PayloadAction<Action | null>) => {
      state.currentAction = action.payload
    },
    updateActionInPlace: (state, action: PayloadAction<Partial<Action> & { id: string }>) => {
      const { id, ...updates } = action.payload
      if (state.actions[id]) {
        state.actions[id] = { ...state.actions[id], ...updates }
      }
      if (state.currentAction?.id === id) {
        state.currentAction = { ...state.currentAction, ...updates }
      }
    },
    setFilters: (state, action: PayloadAction<Partial<APIState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload }
    },
    clearError: (state) => {
      state.error = null
    }
  },
  extraReducers: (builder) => {
    builder
      // fetchActions
      .addCase(fetchActions.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchActions.fulfilled, (state, action) => {
        state.loading = false
        state.actions = action.payload.reduce((acc, action) => {
          acc[action.id] = action
          return acc
        }, {} as Record<string, Action>)
      })
      .addCase(fetchActions.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || 'Failed to fetch actions'
      })
      // createAction
      .addCase(createAction.fulfilled, (state, action) => {
        state.actions[action.payload.id] = action.payload
        state.currentAction = action.payload
      })
      // updateAction
      .addCase(updateAction.fulfilled, (state, action) => {
        state.actions[action.payload.id] = action.payload
        if (state.currentAction?.id === action.payload.id) {
          state.currentAction = action.payload
        }
      })
      // deleteAction
      .addCase(deleteAction.fulfilled, (state, action) => {
        delete state.actions[action.payload]
        if (state.currentAction?.id === action.payload) {
          state.currentAction = null
        }
      })
  }
})

export const { 
  setCurrentAction, 
  updateActionInPlace, 
  setFilters, 
  clearError 
} = apiSlice.actions

export { apiSlice }
```
