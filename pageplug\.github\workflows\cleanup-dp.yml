name: Cleanup Deploy Previews

on:
  workflow_dispatch:
  schedule:
    - cron: 0 0 * * *

jobs:
  execute:
    runs-on: ubuntu-latest
    steps:
      # Checkout the code
      - uses: actions/checkout@v2

      - name: Install mongosh
        run: |
          sudo apt-get update
          sudo apt-get install -y wget gnupg
          wget -qO - https://www.mongodb.org/static/pgp/server-5.0.asc | sudo apt-key add -
          echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu jammy/mongodb-org/5.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list
          sudo apt-get update
          sudo apt-get install -y mongodb-mongosh

      - name: Install relevant packages
        run: |
          which aws
          sudo apt update -q
          sudo apt install -y curl unzip less jq
          curl -LO https://storage.googleapis.com/kubernetes-release/release/v1.23.6/bin/linux/amd64/kubectl
          sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl
          curl -fsSL -o get_helm.sh https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3
          chmod 700 get_helm.sh
          ./get_helm.sh

      - name: Delete Helm chart
        env:
          AWS_ROLE_ARN: ${{ secrets.APPSMITH_EKS_AWS_ROLE_ARN }}
          AWS_ACCESS_KEY_ID: ${{ secrets.APPSMITH_CI_AWS_SECRET_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.APPSMITH_CI_AWS_SECRET_ACCESS_KEY }}
          DB_USERNAME: ${{ secrets.DB_USERNAME }}
          DB_PASSWORD: ${{ secrets.DB_PASSWORD }}
          DB_URL: ${{ secrets.DB_URL }}
          GH_TOKEN: ${{ secrets.APPSMITH_DEPLOY_PREVIEW_PAT }}
          DP_EFS_ID: ${{ secrets.APPSMITH_DP_EFS_ID }}

        run: /bin/bash ./scripts/cleanup_dp.sh
