import React from 'react';
import { Spin, SpinProps } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import styled, { keyframes } from 'styled-components';

// 自定义旋转动画
const rotate = keyframes`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`;

// 脉冲动画
const pulse = keyframes`
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
`;

// 加载容器
const LoadingContainer = styled.div<{ $fullScreen?: boolean; $minHeight?: string }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
  min-height: ${props => props.$minHeight || '200px'};
  
  ${props => props.$fullScreen && `
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(4px);
    z-index: 9999;
    min-height: 100vh;
  `}
`;

// 自定义加载图标
const CustomLoadingIcon = styled(LoadingOutlined)<{ $size?: number; $color?: string }>`
  font-size: ${props => props.$size || 24}px !important;
  color: ${props => props.$color || props.theme.colors.primary} !important;
  animation: ${rotate} 1s linear infinite;
`;

// 加载文本
const LoadingText = styled.div<{ $color?: string }>`
  margin-top: 12px;
  color: ${props => props.$color || props.theme.colors.text.secondary};
  font-size: 14px;
  animation: ${pulse} 2s ease-in-out infinite;
`;

// 点状加载器
const DotLoader = styled.div`
  display: flex;
  gap: 4px;
  
  .dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: ${props => props.theme.colors.primary};
    animation: ${pulse} 1.4s ease-in-out infinite both;
    
    &:nth-child(1) { animation-delay: -0.32s; }
    &:nth-child(2) { animation-delay: -0.16s; }
    &:nth-child(3) { animation-delay: 0s; }
  }
`;

// 骨架屏加载器
const SkeletonLoader = styled.div`
  .skeleton-line {
    height: 16px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 4px;
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  @keyframes loading {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }
`;

// 组件属性接口
interface LoadingSpinnerProps extends Omit<SpinProps, 'indicator'> {
  text?: string;
  fullScreen?: boolean;
  minHeight?: string;
  variant?: 'spin' | 'dots' | 'skeleton';
  iconSize?: number;
  iconColor?: string;
  textColor?: string;
  skeletonLines?: number;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  text = '加载中...',
  fullScreen = false,
  minHeight,
  variant = 'spin',
  iconSize = 24,
  iconColor,
  textColor,
  skeletonLines = 3,
  ...spinProps
}) => {
  // 渲染不同类型的加载器
  const renderLoader = () => {
    switch (variant) {
      case 'dots':
        return (
          <>
            <DotLoader>
              <div className="dot" />
              <div className="dot" />
              <div className="dot" />
            </DotLoader>
            {text && <LoadingText $color={textColor}>{text}</LoadingText>}
          </>
        );
      
      case 'skeleton':
        return (
          <SkeletonLoader>
            {Array.from({ length: skeletonLines }, (_, index) => (
              <div
                key={index}
                className="skeleton-line"
                style={{
                  width: `${Math.random() * 40 + 60}%`
                }}
              />
            ))}
          </SkeletonLoader>
        );
      
      default:
        return (
          <Spin
            indicator={<CustomLoadingIcon $size={iconSize} $color={iconColor} />}
            tip={text}
            {...spinProps}
          />
        );
    }
  };

  return (
    <LoadingContainer $fullScreen={fullScreen} $minHeight={minHeight}>
      {renderLoader()}
    </LoadingContainer>
  );
};

// 页面级加载组件
export const PageLoading: React.FC<{ text?: string }> = ({ text = '页面加载中...' }) => (
  <LoadingSpinner fullScreen text={text} iconSize={32} />
);

// 内容区域加载组件
export const ContentLoading: React.FC<{ text?: string; minHeight?: string }> = ({ 
  text = '内容加载中...', 
  minHeight = '300px' 
}) => (
  <LoadingSpinner text={text} minHeight={minHeight} />
);

// 按钮加载组件
export const ButtonLoading: React.FC = () => (
  <LoadingSpinner variant="spin" iconSize={14} text="" />
);

// 表格加载组件
export const TableLoading: React.FC = () => (
  <LoadingSpinner variant="skeleton" skeletonLines={5} />
);
