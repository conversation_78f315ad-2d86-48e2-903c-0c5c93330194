import { Datasource, Plugin, Query, DatasourceStructure, ConnectionTestResult } from '../types/api';

// 模拟插件数据
export const mockPlugins: Plugin[] = [
  {
    id: 'mysql-plugin',
    name: 'mysql',
    displayName: 'MySQL Database',
    version: '1.0.0',
    category: 'database',
    status: 'active',
    description: '连接到 MySQL 数据库，支持 5.7+ 版本',
    iconUrl: '/icons/mysql.svg',
    documentationUrl: 'https://docs.example.com/mysql',
    supportedOperations: ['READ', 'WRITE', 'DELETE'],
    templates: {
      SELECT: 'SELECT * FROM table_name LIMIT 10;',
      INSERT: 'INSERT INTO table_name (column1, column2) VALUES (?, ?);',
      UPDATE: 'UPDATE table_name SET column1 = ? WHERE id = ?;',
      DELETE: 'DELETE FROM table_name WHERE id = ?;'
    }
  },
  {
    id: 'postgresql-plugin',
    name: 'postgresql',
    displayName: 'PostgreSQL Database',
    version: '1.0.0',
    category: 'database',
    status: 'active',
    description: '连接到 PostgreSQL 数据库，支持 12+ 版本',
    iconUrl: '/icons/postgresql.svg',
    documentationUrl: 'https://docs.example.com/postgresql',
    supportedOperations: ['READ', 'WRITE', 'DELETE'],
    templates: {
      SELECT: 'SELECT * FROM table_name LIMIT 10;',
      INSERT: 'INSERT INTO table_name (column1, column2) VALUES ($1, $2);',
      UPDATE: 'UPDATE table_name SET column1 = $1 WHERE id = $2;',
      DELETE: 'DELETE FROM table_name WHERE id = $1;'
    }
  },
  {
    id: 'mongodb-plugin',
    name: 'mongodb',
    displayName: 'MongoDB Database',
    version: '1.0.0',
    category: 'database',
    status: 'active',
    description: '连接到 MongoDB 数据库，支持 4.4+ 版本',
    iconUrl: '/icons/mongodb.svg',
    documentationUrl: 'https://docs.example.com/mongodb',
    supportedOperations: ['READ', 'WRITE', 'DELETE'],
    templates: {
      FIND: 'db.collection.find({});',
      INSERT: 'db.collection.insertOne({});',
      UPDATE: 'db.collection.updateOne({}, {$set: {}});',
      DELETE: 'db.collection.deleteOne({});'
    }
  },
  {
    id: 'redis-plugin',
    name: 'redis',
    displayName: 'Redis Cache',
    version: '1.0.0',
    category: 'database',
    status: 'active',
    description: '连接到 Redis 缓存数据库',
    iconUrl: '/icons/redis.svg',
    documentationUrl: 'https://docs.example.com/redis',
    supportedOperations: ['READ', 'WRITE', 'DELETE'],
    templates: {
      GET: 'GET key',
      SET: 'SET key value',
      DEL: 'DEL key',
      KEYS: 'KEYS pattern'
    }
  },
  {
    id: 'restapi-plugin',
    name: 'restapi',
    displayName: 'REST API',
    version: '1.0.0',
    category: 'api',
    status: 'active',
    description: '连接到 REST API 服务',
    iconUrl: '/icons/api.svg',
    documentationUrl: 'https://docs.example.com/restapi',
    supportedOperations: ['READ', 'WRITE'],
    templates: {
      GET: 'GET /api/endpoint',
      POST: 'POST /api/endpoint',
      PUT: 'PUT /api/endpoint',
      DELETE: 'DELETE /api/endpoint'
    }
  },
  {
    id: 'elasticsearch-plugin',
    name: 'elasticsearch',
    displayName: 'Elasticsearch',
    version: '1.0.0',
    category: 'database',
    status: 'active',
    description: '连接到 Elasticsearch 搜索引擎',
    iconUrl: '/icons/elasticsearch.svg',
    documentationUrl: 'https://docs.example.com/elasticsearch',
    supportedOperations: ['READ', 'WRITE'],
    templates: {
      SEARCH: 'GET /index/_search',
      INDEX: 'POST /index/_doc',
      UPDATE: 'POST /index/_update/id',
      DELETE: 'DELETE /index/_doc/id'
    }
  }
];

// 模拟数据源数据
export const mockDatasources: Datasource[] = [
  {
    id: 'ds-001',
    name: 'MySQL 生产环境',
    pluginId: 'mysql-plugin',
    pluginName: 'MySQL Database',
    workspaceId: 'workspace-123',
    isValid: true,
    isConfigured: true,
    lastUsed: '2024-01-15T09:45:00Z',
    createdAt: '2024-01-10T10:30:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
    datasourceConfiguration: {
      url: 'mysql://prod-mysql.example.com:3306',
      databaseName: 'production',
      authentication: {
        authType: 'basic',
        username: 'app_user',
        secretExists: {
          password: true
        }
      },
      connection: {
        ssl: {
          enabled: true,
          mode: 'REQUIRED'
        },
        timeout: 30000,
        maxConnections: 10
      }
    }
  },
  {
    id: 'ds-002',
    name: 'PostgreSQL 开发环境',
    pluginId: 'postgresql-plugin',
    pluginName: 'PostgreSQL Database',
    workspaceId: 'workspace-123',
    isValid: true,
    isConfigured: true,
    lastUsed: '2024-01-14T16:20:00Z',
    createdAt: '2024-01-08T14:15:00Z',
    updatedAt: '2024-01-14T16:20:00Z',
    datasourceConfiguration: {
      url: 'postgresql://dev-postgres.example.com:5432',
      databaseName: 'development',
      authentication: {
        authType: 'basic',
        username: 'dev_user',
        secretExists: {
          password: true
        }
      },
      connection: {
        ssl: {
          enabled: false
        },
        timeout: 15000,
        maxConnections: 5
      }
    }
  },
  {
    id: 'ds-003',
    name: 'MongoDB 用户数据',
    pluginId: 'mongodb-plugin',
    pluginName: 'MongoDB Database',
    workspaceId: 'workspace-123',
    isValid: false,
    isConfigured: true,
    lastUsed: '2024-01-12T11:30:00Z',
    createdAt: '2024-01-05T09:00:00Z',
    updatedAt: '2024-01-12T11:30:00Z',
    datasourceConfiguration: {
      url: 'mongodb://mongo.example.com:27017',
      databaseName: 'userdata',
      authentication: {
        authType: 'basic',
        username: 'mongo_user',
        secretExists: {
          password: true
        }
      }
    }
  },
  {
    id: 'ds-004',
    name: 'Redis 缓存',
    pluginId: 'redis-plugin',
    pluginName: 'Redis Cache',
    workspaceId: 'workspace-123',
    isValid: true,
    isConfigured: true,
    lastUsed: '2024-01-15T08:15:00Z',
    createdAt: '2024-01-03T16:45:00Z',
    updatedAt: '2024-01-15T08:15:00Z',
    datasourceConfiguration: {
      url: 'redis://cache.example.com:6379',
      authentication: {
        authType: 'basic',
        secretExists: {
          password: true
        }
      }
    }
  },
  {
    id: 'ds-005',
    name: 'API Gateway',
    pluginId: 'restapi-plugin',
    pluginName: 'REST API',
    workspaceId: 'workspace-123',
    isValid: true,
    isConfigured: true,
    lastUsed: '2024-01-15T10:00:00Z',
    createdAt: '2024-01-01T12:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    datasourceConfiguration: {
      url: 'https://api.example.com',
      authentication: {
        authType: 'bearer',
        secretExists: {
          token: true
        }
      },
      headers: [
        {
          key: 'Content-Type',
          value: 'application/json'
        }
      ]
    }
  }
];

// 模拟查询数据
export const mockQueries: Query[] = [
  {
    id: 'query-001',
    name: '获取活跃用户',
    datasourceId: 'ds-001',
    query: 'SELECT * FROM users WHERE status = ? AND last_login > ? LIMIT ?',
    parameters: [
      { key: 'status', value: 'active' },
      { key: 'last_login', value: '2024-01-01' },
      { key: 'limit', value: 100 }
    ],
    tags: ['users', 'active'],
    createdAt: '2024-01-10T10:30:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
    lastExecuted: '2024-01-15T09:45:00Z'
  },
  {
    id: 'query-002',
    name: '订单统计',
    datasourceId: 'ds-002',
    query: 'SELECT DATE(created_at) as date, COUNT(*) as count FROM orders WHERE created_at >= $1 GROUP BY DATE(created_at)',
    parameters: [
      { key: 'start_date', value: '2024-01-01' }
    ],
    tags: ['orders', 'statistics'],
    createdAt: '2024-01-08T14:15:00Z',
    updatedAt: '2024-01-14T16:20:00Z',
    lastExecuted: '2024-01-14T16:20:00Z'
  }
];

// 模拟数据源结构
export const mockDatasourceStructure: Record<string, DatasourceStructure> = {
  'ds-001': {
    tables: [
      {
        name: 'users',
        type: 'TABLE',
        columns: [
          {
            name: 'id',
            type: 'INT',
            isPrimaryKey: true,
            isAutoIncrement: true,
            isNullable: false
          },
          {
            name: 'username',
            type: 'VARCHAR',
            maxLength: 50,
            isNullable: false
          },
          {
            name: 'email',
            type: 'VARCHAR',
            maxLength: 100,
            isNullable: false
          },
          {
            name: 'status',
            type: 'ENUM',
            isNullable: false,
            defaultValue: 'active'
          },
          {
            name: 'created_at',
            type: 'TIMESTAMP',
            isNullable: false
          },
          {
            name: 'last_login',
            type: 'TIMESTAMP',
            isNullable: true
          }
        ],
        keys: [
          {
            name: 'PRIMARY',
            type: 'PRIMARY',
            columnNames: ['id']
          },
          {
            name: 'idx_email',
            type: 'UNIQUE',
            columnNames: ['email']
          }
        ],
        templates: [
          {
            title: '查询所有用户',
            body: 'SELECT * FROM users LIMIT 10;',
            suggested: true
          },
          {
            title: '查询活跃用户',
            body: "SELECT * FROM users WHERE status = 'active' LIMIT 10;"
          }
        ]
      },
      {
        name: 'orders',
        type: 'TABLE',
        columns: [
          {
            name: 'id',
            type: 'INT',
            isPrimaryKey: true,
            isAutoIncrement: true,
            isNullable: false
          },
          {
            name: 'user_id',
            type: 'INT',
            isNullable: false
          },
          {
            name: 'total_amount',
            type: 'DECIMAL',
            isNullable: false
          },
          {
            name: 'status',
            type: 'ENUM',
            isNullable: false
          },
          {
            name: 'created_at',
            type: 'TIMESTAMP',
            isNullable: false
          }
        ],
        keys: [
          {
            name: 'PRIMARY',
            type: 'PRIMARY',
            columnNames: ['id']
          },
          {
            name: 'fk_user_id',
            type: 'FOREIGN',
            columnNames: ['user_id']
          }
        ],
        templates: [
          {
            title: '查询订单',
            body: 'SELECT * FROM orders LIMIT 10;',
            suggested: true
          }
        ]
      }
    ]
  }
};

// 模拟连接测试结果
export const mockConnectionTestResults: Record<string, ConnectionTestResult> = {
  'ds-001': {
    success: true,
    message: '连接成功',
    responseTime: 150,
    details: {
      serverVersion: '8.0.28',
      charset: 'utf8mb4'
    }
  },
  'ds-002': {
    success: true,
    message: '连接成功',
    responseTime: 120,
    details: {
      serverVersion: '14.5',
      charset: 'UTF8'
    }
  },
  'ds-003': {
    success: false,
    message: '连接失败：认证错误',
    responseTime: 5000,
    details: {
      error: 'Authentication failed'
    }
  }
};
