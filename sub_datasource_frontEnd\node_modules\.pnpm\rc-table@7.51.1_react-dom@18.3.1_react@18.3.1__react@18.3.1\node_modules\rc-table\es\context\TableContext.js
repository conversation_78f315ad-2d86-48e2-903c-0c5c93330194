import { createContext, createImmutable } from '@rc-component/context';
var _createImmutable = createImmutable(),
  makeImmutable = _createImmutable.makeImmutable,
  responseImmutable = _createImmutable.responseImmutable,
  useImmutableMark = _createImmutable.useImmutableMark;
export { makeImmutable, responseImmutable, useImmutableMark };
var TableContext = createContext();
export default TableContext;