# 微前端架构设计

## 1. 微前端架构概述

### 1.1 架构目标
- **独立开发**: 数据源管理作为独立的前端应用
- **独立部署**: 可以独立发布和更新
- **技术栈自由**: 使用Vue3技术栈
- **运行时集成**: 通过模块联邦或iframe集成到主应用
- **状态隔离**: 独立的状态管理和路由

### 1.2 微前端方案选择
采用 **React18 + Module Federation + Styled Components + TailwindCSS** 方案，具有以下优势：
- 技术栈统一，降低维护成本
- 组件库可复用，提升开发效率
- 运行时动态加载，支持独立部署
- Styled Components + TailwindCSS双重样式隔离机制，避免样式冲突
- 类型安全支持，提升代码质量
- 原子化CSS，快速样式开发

### 1.3 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    微前端架构                                │
├─────────────────────────────────────────────────────────────┤
│  主应用 (Shell App)                                         │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              React 主框架                               │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    │ │
│  │  │  应用编辑器  │  │  用户管理    │  │  工作空间    │    │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘    │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  微前端应用 (Micro Frontend)                                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              数据源管理应用 (React18)                    │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    │ │
│  │  │  数据源列表  │  │  数据源配置  │  │  查询测试    │    │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘    │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  共享服务层 (Shared Services)                               │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │  认证服务    │  │  通信总线    │  │  主题系统    │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

## 2. 技术架构设计

### 2.1 技术栈选择
- **构建工具**: Webpack 5 + Module Federation
- **前端框架**: React 18.2+ (Hooks + Concurrent Features)
- **状态管理**: Redux Toolkit 1.9+ + React Query 4.0+
- **UI组件库**: Ant Design 5.0+ (统一设计语言)
- **路由管理**: React Router 6.0+
- **HTTP客户端**: Axios 1.6+ (统一请求拦截)
- **类型支持**: TypeScript 5.0+
- **样式方案**: Styled Components 6.0+ + TailwindCSS 3.0+ (双重样式隔离)

### 2.2 Module Federation配置
```javascript
// webpack.config.js - 数据源微前端应用
const ModuleFederationPlugin = require('@module-federation/webpack')
const path = require('path')

module.exports = {
  mode: 'development',
  entry: './src/index.tsx',
  devServer: {
    port: 3001,
    headers: {
      'Access-Control-Allow-Origin': '*',
    },
  },
  resolve: {
    extensions: ['.tsx', '.ts', '.js', '.jsx'],
  },
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: 'ts-loader',
        exclude: /node_modules/,
      },
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader', 'postcss-loader'],
      },
    ],
  },
  plugins: [
    new ModuleFederationPlugin({
      name: 'datasourceApp',
      filename: 'remoteEntry.js',
      exposes: {
        './DatasourceApp': './src/App',
        './DatasourceRoutes': './src/routes',
        './DatasourceStore': './src/store'
      },
      shared: {
        react: {
          singleton: true,
          requiredVersion: '^18.2.0'
        },
        'react-dom': {
          singleton: true,
          requiredVersion: '^18.2.0'
        },
        'antd': {
          singleton: true,
          requiredVersion: '^5.0.0'
        },
        'styled-components': {
          singleton: true,
          requiredVersion: '^6.0.0'
        }
      }
    })
  ]
}
```

```javascript
// webpack.config.js - 主应用
const ModuleFederationPlugin = require('@module-federation/webpack')

module.exports = {
  mode: 'development',
  entry: './src/index.tsx',
  devServer: {
    port: 3000,
  },
  resolve: {
    extensions: ['.tsx', '.ts', '.js', '.jsx'],
  },
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: 'ts-loader',
        exclude: /node_modules/,
      },
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader', 'postcss-loader'],
      },
    ],
  },
  plugins: [
    new ModuleFederationPlugin({
      name: 'shellApp',
      remotes: {
        datasourceApp: 'datasourceApp@http://localhost:3001/remoteEntry.js'
      },
      shared: {
        react: {
          singleton: true,
          requiredVersion: '^18.2.0'
        },
        'react-dom': {
          singleton: true,
          requiredVersion: '^18.2.0'
        },
        'antd': {
          singleton: true,
          requiredVersion: '^5.0.0'
        },
        'styled-components': {
          singleton: true,
          requiredVersion: '^6.0.0'
        }
      }
    })
  ]
}
```

## 3. 项目结构设计

### 3.1 数据源微前端应用结构
```
datasource-frontend/
├── public/
│   ├── index.html
│   └── favicon.ico
├── src/
│   ├── index.tsx               # 应用入口
│   ├── App.tsx                 # 根组件
│   ├── components/             # 组件库
│   │   ├── common/            # 通用组件
│   │   │   ├── LoadingSpinner.tsx
│   │   │   ├── ErrorMessage.tsx
│   │   │   └── ConfirmDialog.tsx
│   │   └── datasource/        # 数据源组件
│   │       ├── DatasourceCard.tsx
│   │       ├── DatasourceForm.tsx
│   │       ├── ConnectionTest.tsx
│   │       ├── QueryTester.tsx
│   │       └── StructureViewer.tsx
│   ├── pages/                 # 页面组件
│   │   ├── DatasourceList.tsx
│   │   ├── DatasourceDetail.tsx
│   │   ├── DatasourceCreate.tsx
│   │   └── PluginManage.tsx
│   ├── routes/                # 路由配置
│   │   └── index.tsx
│   ├── store/                 # 状态管理
│   │   ├── index.ts
│   │   ├── datasourceSlice.ts
│   │   ├── pluginSlice.ts
│   │   └── systemSlice.ts
│   ├── hooks/                 # 自定义Hooks
│   │   ├── useDatasource.ts
│   │   ├── useConnection.ts
│   │   ├── useValidation.ts
│   │   └── useNotification.ts
│   ├── api/                   # API接口
│   │   ├── index.ts
│   │   ├── datasource.ts
│   │   ├── plugin.ts
│   │   └── types.ts
│   ├── utils/                 # 工具函数
│   │   ├── request.ts
│   │   ├── helpers.ts
│   │   └── constants.ts
│   ├── styles/                # 样式文件
│   │   ├── global.ts          # Styled Components全局样式
│   │   ├── theme.ts           # 主题配置
│   │   └── tailwind.css       # TailwindCSS样式
│   └── types/                 # 类型定义
│       ├── datasource.ts
│       ├── plugin.ts
│       └── common.ts
├── tests/                     # 测试文件
│   ├── unit/
│   ├── integration/
│   └── e2e/
├── package.json
├── webpack.config.js
├── tsconfig.json
├── tailwind.config.js
├── postcss.config.js
└── README.md
```

### 3.2 应用入口设计
```typescript
// src/index.tsx
import React from 'react'
import { createRoot } from 'react-dom/client'
import { Provider } from 'react-redux'
import { BrowserRouter } from 'react-router-dom'
import { ConfigProvider } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import { ThemeProvider } from 'styled-components'
import App from './App'
import { store } from './store'
import { GlobalStyle } from './styles/global'
import { theme } from './styles/theme'
import './styles/tailwind.css'

// 创建根容器
const container = document.getElementById('datasource-app')
const root = createRoot(container!)

// 渲染应用
root.render(
  <React.StrictMode>
    <Provider store={store}>
      <BrowserRouter basename="/datasource">
        <ConfigProvider locale={zhCN}>
          <ThemeProvider theme={theme}>
            <GlobalStyle />
            <App />
          </ThemeProvider>
        </ConfigProvider>
      </BrowserRouter>
    </Provider>
  </React.StrictMode>
)

// 导出应用实例供主应用使用
export { store }
```

```tsx
// src/App.tsx
import React, { Suspense } from 'react'
import { Routes, Route } from 'react-router-dom'
import styled from 'styled-components'
import { Spin } from 'antd'
import { ErrorBoundary } from './components/common/ErrorBoundary'
import { useTheme } from './hooks/useTheme'

// 懒加载页面组件
const DatasourceList = React.lazy(() => import('./pages/DatasourceList'))
const DatasourceDetail = React.lazy(() => import('./pages/DatasourceDetail'))
const DatasourceCreate = React.lazy(() => import('./pages/DatasourceCreate'))
const PluginManage = React.lazy(() => import('./pages/PluginManage'))

const AppContainer = styled.div`
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
`

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
`

function App() {
  const { theme } = useTheme()

  return (
    <AppContainer className="datasource-app tw-min-h-screen">
      <ErrorBoundary>
        <Suspense
          fallback={
            <LoadingContainer>
              <Spin size="large" />
            </LoadingContainer>
          }
        >
          <Routes>
            <Route path="/" element={<DatasourceList />} />
            <Route path="/create" element={<DatasourceCreate />} />
            <Route path="/:id" element={<DatasourceDetail />} />
            <Route path="/plugins" element={<PluginManage />} />
          </Routes>
        </Suspense>
      </ErrorBoundary>
    </AppContainer>
  )
}

export default App
```

## 4. 路由设计

### 4.1 路由配置
```typescript
// src/routes/index.tsx
import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { useSelector } from 'react-redux'
import { RootState } from '../store'
import { ProtectedRoute } from '../components/common/ProtectedRoute'

// 懒加载页面组件
const DatasourceList = React.lazy(() => import('../pages/DatasourceList'))
const DatasourceDetail = React.lazy(() => import('../pages/DatasourceDetail'))
const DatasourceCreate = React.lazy(() => import('../pages/DatasourceCreate'))
const DatasourceEdit = React.lazy(() => import('../pages/DatasourceEdit'))
const PluginManage = React.lazy(() => import('../pages/PluginManage'))

export const AppRoutes: React.FC = () => {
  const isAuthenticated = useSelector((state: RootState) => state.auth.isAuthenticated)

  return (
    <Routes>
      <Route path="/" element={<Navigate to="/datasources" replace />} />

      <Route
        path="/datasources"
        element={
          <ProtectedRoute isAuthenticated={isAuthenticated}>
            <DatasourceList />
          </ProtectedRoute>
        }
      />

      <Route
        path="/datasources/create"
        element={
          <ProtectedRoute isAuthenticated={isAuthenticated}>
            <DatasourceCreate />
          </ProtectedRoute>
        }
      />

      <Route
        path="/datasources/:id"
        element={
          <ProtectedRoute isAuthenticated={isAuthenticated}>
            <DatasourceDetail />
          </ProtectedRoute>
        }
      />

      <Route
        path="/datasources/:id/edit"
        element={
          <ProtectedRoute isAuthenticated={isAuthenticated}>
            <DatasourceEdit />
          </ProtectedRoute>
        }
      />

      <Route
        path="/plugins"
        element={
          <ProtectedRoute isAuthenticated={isAuthenticated}>
            <PluginManage />
          </ProtectedRoute>
        }
      />
    </Routes>
  )
}

// 受保护的路由组件
// src/components/common/ProtectedRoute.tsx
import React, { useEffect } from 'react'

interface ProtectedRouteProps {
  isAuthenticated: boolean
  children: React.ReactNode
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  isAuthenticated,
  children
}) => {
  useEffect(() => {
    if (!isAuthenticated) {
      // 通知主应用需要登录
      window.parent.postMessage({
        type: 'REQUIRE_AUTH',
        payload: { redirectTo: window.location.pathname }
      }, '*')
    }
  }, [isAuthenticated])

  if (!isAuthenticated) {
    return (
      <div className="tw-flex tw-items-center tw-justify-center tw-h-64">
        <div className="tw-text-gray-500">请先登录...</div>
      </div>
    )
  }

  return <>{children}</>
}
```

## 5. 状态管理设计

### 5.1 Redux Toolkit Store设计
```typescript
// src/store/index.ts
import { configureStore } from '@reduxjs/toolkit'
import datasourceReducer from './datasourceSlice'
import pluginReducer from './pluginSlice'
import systemReducer from './systemSlice'
import authReducer from './authSlice'

export const store = configureStore({
  reducer: {
    datasource: datasourceReducer,
    plugin: pluginReducer,
    system: systemReducer,
    auth: authReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
})

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
```

```typescript
// src/store/datasourceSlice.ts
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { datasourceApi } from '../api/datasource'
import type { Datasource, DatasourceCreate, DatasourceUpdate } from '../types/datasource'

interface DatasourceState {
  datasources: Datasource[]
  currentDatasource: Datasource | null
  loading: boolean
  error: string | null
  filters: {
    type: string
    status: string
    search: string
  }
}

const initialState: DatasourceState = {
  datasources: [],
  currentDatasource: null,
  loading: false,
  error: null,
  filters: {
    type: '',
    status: '',
    search: ''
  }
}

// 异步操作
export const fetchDatasources = createAsyncThunk(
  'datasource/fetchDatasources',
  async (workspaceId: string) => {
    const response = await datasourceApi.list({ workspaceId })
    return response.data
  }
)

export const createDatasource = createAsyncThunk(
  'datasource/createDatasource',
  async (datasourceData: DatasourceCreate) => {
    const response = await datasourceApi.create(datasourceData)
    return response.data
  }
)

export const updateDatasource = createAsyncThunk(
  'datasource/updateDatasource',
  async ({ id, updateData }: { id: string; updateData: DatasourceUpdate }) => {
    const response = await datasourceApi.update(id, updateData)
    return response.data
  }
)

export const deleteDatasource = createAsyncThunk(
  'datasource/deleteDatasource',
  async (id: string) => {
    await datasourceApi.delete(id)
    return id
  }
)

export const testConnection = createAsyncThunk(
  'datasource/testConnection',
  async (id: string) => {
    const response = await datasourceApi.testConnection(id)
    return { id, result: response.data }
  }
)

// Slice定义
const datasourceSlice = createSlice({
  name: 'datasource',
  initialState,
  reducers: {
    setCurrentDatasource: (state, action: PayloadAction<Datasource | null>) => {
      state.currentDatasource = action.payload
    },
    updateFilters: (state, action: PayloadAction<Partial<DatasourceState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload }
    },
    clearError: (state) => {
      state.error = null
    },
    updateDatasourceStatus: (state, action: PayloadAction<{ id: string; isValid: boolean }>) => {
      const datasource = state.datasources.find(ds => ds.id === action.payload.id)
      if (datasource) {
        datasource.isValid = action.payload.isValid
      }
    }
  },
  extraReducers: (builder) => {
    builder
      // fetchDatasources
      .addCase(fetchDatasources.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchDatasources.fulfilled, (state, action) => {
        state.loading = false
        state.datasources = action.payload
      })
      .addCase(fetchDatasources.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || '获取数据源列表失败'
      })
      // createDatasource
      .addCase(createDatasource.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(createDatasource.fulfilled, (state, action) => {
        state.loading = false
        state.datasources.push(action.payload)
      })
      .addCase(createDatasource.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || '创建数据源失败'
      })
      // updateDatasource
      .addCase(updateDatasource.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(updateDatasource.fulfilled, (state, action) => {
        state.loading = false
        const index = state.datasources.findIndex(ds => ds.id === action.payload.id)
        if (index !== -1) {
          state.datasources[index] = action.payload
        }
      })
      .addCase(updateDatasource.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || '更新数据源失败'
      })
      // deleteDatasource
      .addCase(deleteDatasource.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(deleteDatasource.fulfilled, (state, action) => {
        state.loading = false
        state.datasources = state.datasources.filter(ds => ds.id !== action.payload)
      })
      .addCase(deleteDatasource.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || '删除数据源失败'
      })
      // testConnection
      .addCase(testConnection.fulfilled, (state, action) => {
        const { id, result } = action.payload
        const datasource = state.datasources.find(ds => ds.id === id)
        if (datasource) {
          datasource.isValid = result.success
        }
      })
  }
})

export const {
  setCurrentDatasource,
  updateFilters,
  clearError,
  updateDatasourceStatus
} = datasourceSlice.actions

export default datasourceSlice.reducer

// 选择器
export const selectDatasources = (state: { datasource: DatasourceState }) =>
  state.datasource.datasources

export const selectFilteredDatasources = (state: { datasource: DatasourceState }) => {
  const { datasources, filters } = state.datasource
  return datasources.filter(ds => {
    const matchesType = !filters.type || ds.pluginId === filters.type
    const matchesStatus = !filters.status ||
      (filters.status === 'valid' ? ds.isValid : !ds.isValid)
    const matchesSearch = !filters.search ||
      ds.name.toLowerCase().includes(filters.search.toLowerCase())

    return matchesType && matchesStatus && matchesSearch
  })
}

export const selectDatasourcesByType = (state: { datasource: DatasourceState }) => {
  return state.datasource.datasources.reduce((acc, ds) => {
    if (!acc[ds.pluginId]) acc[ds.pluginId] = []
    acc[ds.pluginId].push(ds)
    return acc
  }, {} as Record<string, Datasource[]>)
}
```

## 6. 组件间通信

### 6.1 与主应用通信
```typescript
// src/utils/communication.ts
interface MessagePayload {
  type: string
  payload?: any
}

class MicroFrontendCommunication {
  private listeners: Map<string, Function[]> = new Map()

  constructor() {
    // 监听来自主应用的消息
    window.addEventListener('message', this.handleMessage.bind(this))
  }

  // 发送消息到主应用
  sendToParent(type: string, payload?: any) {
    window.parent.postMessage({ type, payload }, '*')
  }

  // 监听特定类型的消息
  on(type: string, callback: Function) {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, [])
    }
    this.listeners.get(type)!.push(callback)
  }

  // 移除消息监听器
  off(type: string, callback: Function) {
    const callbacks = this.listeners.get(type)
    if (callbacks) {
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  // 处理接收到的消息
  private handleMessage(event: MessageEvent) {
    const { type, payload } = event.data as MessagePayload
    const callbacks = this.listeners.get(type)

    if (callbacks) {
      callbacks.forEach(callback => callback(payload))
    }
  }

  // 通知主应用路由变化
  notifyRouteChange(route: string) {
    this.sendToParent('ROUTE_CHANGE', { route })
  }

  // 通知主应用数据源变化
  notifyDatasourceChange(datasource: any) {
    this.sendToParent('DATASOURCE_CHANGE', { datasource })
  }

  // 请求主应用的用户信息
  requestUserInfo() {
    this.sendToParent('REQUEST_USER_INFO')
  }

  // 请求主应用的工作空间信息
  requestWorkspaceInfo() {
    this.sendToParent('REQUEST_WORKSPACE_INFO')
  }
}

export const communication = new MicroFrontendCommunication()
```

### 6.2 主应用集成代码
```typescript
// 主应用中的微前端集成
import { lazy, Suspense } from 'react'

// 动态导入微前端应用
const DatasourceApp = lazy(() => import('datasourceApp/DatasourceApp'))

function DatasourcePage() {
  useEffect(() => {
    // 监听来自微前端的消息
    const handleMessage = (event: MessageEvent) => {
      const { type, payload } = event.data

      switch (type) {
        case 'REQUIRE_AUTH':
          // 处理认证请求
          handleAuthRequest(payload)
          break
        case 'ROUTE_CHANGE':
          // 处理路由变化
          handleRouteChange(payload)
          break
        case 'DATASOURCE_CHANGE':
          // 处理数据源变化
          handleDatasourceChange(payload)
          break
        case 'REQUEST_USER_INFO':
          // 发送用户信息
          sendUserInfo()
          break
        case 'REQUEST_WORKSPACE_INFO':
          // 发送工作空间信息
          sendWorkspaceInfo()
          break
      }
    }

    window.addEventListener('message', handleMessage)

    return () => {
      window.removeEventListener('message', handleMessage)
    }
  }, [])

  return (
    <div className="datasource-container">
      <Suspense fallback={<div>Loading...</div>}>
        <DatasourceApp />
      </Suspense>
    </div>
  )
}
```

## 7. 样式隔离和主题系统

### 7.1 样式隔离策略

#### 方案一: Styled Components + TailwindCSS (推荐)
```typescript
// src/styles/global.ts
import styled, { createGlobalStyle } from 'styled-components'

// 全局样式重置，仅作用于当前微应用
export const GlobalStyle = createGlobalStyle`
  .datasource-app {
    /* 样式重置，避免与主应用冲突 */
    * {
      box-sizing: border-box;
    }

    /* CSS变量定义 */
    --ds-primary: ${props => props.theme.colors.primary};
    --ds-success: ${props => props.theme.colors.success};
    --ds-warning: ${props => props.theme.colors.warning};
    --ds-error: ${props => props.theme.colors.error};
    --ds-text: ${props => props.theme.colors.text};
    --ds-bg: ${props => props.theme.colors.background};

    /* TailwindCSS前缀，避免样式冲突 */
    .tw-p-4 { padding: 1rem !important; }
    .tw-mb-4 { margin-bottom: 1rem !important; }
    .tw-rounded { border-radius: 0.25rem !important; }
  }
`

// 组件样式，结合Styled Components和TailwindCSS
const DatasourceCard = styled.div`
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: 6px;
  background: ${props => props.theme.colors.background};
  transition: all 0.2s ease;

  &:hover {
    box-shadow: ${props => props.theme.shadows.card};
    transform: translateY(-1px);
  }

  /* 结合TailwindCSS类名 */
  &.tw-loading {
    opacity: 0.6;
    pointer-events: none;
  }
`

const FormSection = styled.div.attrs({
  className: 'tw-mb-6' // 使用TailwindCSS类名
})`
  .section-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    color: ${props => props.theme.colors.text};
  }

  /* 响应式设计，结合TailwindCSS */
  @media (max-width: 768px) {
    margin-bottom: 1rem;
  }
`
```

```typescript
// src/styles/theme.ts
export const theme = {
  colors: {
    primary: '#1890ff',
    success: '#52c41a',
    warning: '#faad14',
    error: '#ff4d4f',
    text: '#262626',
    textSecondary: '#8c8c8c',
    background: '#ffffff',
    backgroundSecondary: '#fafafa',
    border: '#d9d9d9',
  },
  shadows: {
    card: '0 2px 8px rgba(0, 0, 0, 0.15)',
    modal: '0 4px 12px rgba(0, 0, 0, 0.15)',
  },
  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
  }
}

export type Theme = typeof theme
```

```css
/* src/styles/tailwind.css */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义TailwindCSS组件 */
@layer components {
  .ds-card {
    @apply tw-bg-white tw-rounded-lg tw-shadow-sm tw-border tw-border-gray-200 tw-p-4;
  }

  .ds-button-primary {
    @apply tw-bg-blue-500 tw-text-white tw-px-4 tw-py-2 tw-rounded tw-hover:bg-blue-600 tw-transition-colors;
  }

  .ds-input {
    @apply tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded tw-focus:outline-none tw-focus:ring-2 tw-focus:ring-blue-500;
  }
}

/* 微前端样式隔离 */
.datasource-app {
  /* 确保样式只作用于当前应用 */
  isolation: isolate;
}
```

#### TailwindCSS配置
```javascript
// tailwind.config.js
module.exports = {
  content: [
    './src/**/*.{js,jsx,ts,tsx}',
  ],
  prefix: 'tw-', // 添加前缀避免样式冲突
  theme: {
    extend: {
      colors: {
        primary: '#1890ff',
        success: '#52c41a',
        warning: '#faad14',
        error: '#ff4d4f',
      },
      fontFamily: {
        sans: ['-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
      },
      boxShadow: {
        'card': '0 2px 8px rgba(0, 0, 0, 0.15)',
        'modal': '0 4px 12px rgba(0, 0, 0, 0.15)',
      }
    },
  },
  plugins: [],
  corePlugins: {
    preflight: false, // 禁用默认样式重置，避免影响主应用
  },
}
```

```javascript
// postcss.config.js
module.exports = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
}
```

#### 方案三: Shadow DOM (高级隔离)
```typescript
// 使用Shadow DOM实现完全样式隔离
import { createRoot } from 'react-dom/client'

class DatasourceMicroApp extends HTMLElement {
  connectedCallback() {
    // 创建Shadow DOM
    const shadow = this.attachShadow({ mode: 'open' })

    // 创建样式
    const style = document.createElement('style')
    style.textContent = `
      :host {
        display: block;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      .datasource-list {
        padding: 20px;
      }

      .datasource-card {
        margin-bottom: 16px;
        border: 1px solid #d9d9d9;
        border-radius: 6px;
      }
    `

    // 创建容器
    const container = document.createElement('div')

    // 添加到Shadow DOM
    shadow.appendChild(style)
    shadow.appendChild(container)

    // 渲染React应用
    const root = createRoot(container)
    root.render(<DatasourceApp />)
  }
}

customElements.define('datasource-micro-app', DatasourceMicroApp)
```
```

### 7.2 React18主题同步机制
```typescript
// src/hooks/useTheme.ts
import { useEffect, useState } from 'react'
import { eventBus } from '@pageplug/shared-utils'

interface ThemeConfig {
  mode: 'light' | 'dark';
  primary: string;
  success: string;
  warning: string;
  error: string;
  textPrimary: string;
  textSecondary: string;
  background: string;
  backgroundSecondary: string;
  border: string;
  borderRadius: string;
  shadow: string;
}

export const useTheme = () => {
  const [theme, setTheme] = useState<ThemeConfig>({
    mode: 'light',
    primary: '#1890ff',
    success: '#52c41a',
    warning: '#faad14',
    error: '#ff4d4f',
    textPrimary: '#262626',
    textSecondary: '#8c8c8c',
    background: '#ffffff',
    backgroundSecondary: '#fafafa',
    border: '#d9d9d9',
    borderRadius: '6px',
    shadow: '0 2px 8px rgba(0, 0, 0, 0.15)'
  })

  useEffect(() => {
    // 监听主应用主题变化
    const handleThemeChange = (newTheme: ThemeConfig) => {
      setTheme(newTheme)
      updateCSSVariables(newTheme)
    }

    eventBus.on('theme:changed', handleThemeChange)

    // 获取初始主题
    eventBus.emit('theme:get', (initialTheme: ThemeConfig) => {
      setTheme(initialTheme)
      updateCSSVariables(initialTheme)
    })

    return () => {
      eventBus.off('theme:changed', handleThemeChange)
    }
  }, [])

  const updateCSSVariables = (themeConfig: ThemeConfig) => {
    const root = document.documentElement

    // 更新CSS变量，使用dataset前缀避免冲突
    root.style.setProperty('--dataset-primary', themeConfig.primary)
    root.style.setProperty('--dataset-success', themeConfig.success)
    root.style.setProperty('--dataset-warning', themeConfig.warning)
    root.style.setProperty('--dataset-error', themeConfig.error)
    root.style.setProperty('--dataset-text-primary', themeConfig.textPrimary)
    root.style.setProperty('--dataset-text-secondary', themeConfig.textSecondary)
    root.style.setProperty('--dataset-background', themeConfig.background)
    root.style.setProperty('--dataset-background-secondary', themeConfig.backgroundSecondary)
    root.style.setProperty('--dataset-border', themeConfig.border)
    root.style.setProperty('--dataset-border-radius', themeConfig.borderRadius)
    root.style.setProperty('--dataset-shadow', themeConfig.shadow)
  }

  return { theme, updateTheme: setTheme }
}
```

### 7.3 样式隔离完整方案
```typescript
// src/components/StyleProvider.tsx
import React, { useEffect } from 'react';
import { ThemeProvider } from 'styled-components';
import { ConfigProvider } from 'antd';
import { GlobalStyle } from '../styles/global';
import { useTheme } from '../hooks/useTheme';

interface StyleProviderProps {
  children: React.ReactNode;
}

export const StyleProvider: React.FC<StyleProviderProps> = ({ children }) => {
  const { theme } = useTheme();

  // Ant Design主题配置
  const antdTheme = {
    token: {
      colorPrimary: theme.primary,
      colorSuccess: theme.success,
      colorWarning: theme.warning,
      colorError: theme.error,
      colorText: theme.textPrimary,
      colorTextSecondary: theme.textSecondary,
      colorBgContainer: theme.background,
      colorBgElevated: theme.backgroundSecondary,
      colorBorder: theme.border,
      borderRadius: parseInt(theme.borderRadius),
      boxShadow: theme.shadow,
    },
    components: {
      Button: {
        borderRadius: parseInt(theme.borderRadius),
      },
      Input: {
        borderRadius: parseInt(theme.borderRadius),
      },
      Card: {
        borderRadius: parseInt(theme.borderRadius) + 2,
      },
    },
  };

  return (
    <div className="dataset-app" data-theme={theme.mode}>
      <GlobalStyle />
      <ThemeProvider theme={theme}>
        <ConfigProvider theme={antdTheme}>
          {children}
        </ConfigProvider>
      </ThemeProvider>
    </div>
  );
};
```

## 8. 部署和构建

### 8.1 构建配置
```json
{
  "scripts": {
    "dev": "vite --port 3001",
    "build": "vue-tsc && vite build",
    "preview": "vite preview --port 3001",
    "build:analyze": "vite build --mode analyze",
    "test": "vitest",
    "test:ui": "vitest --ui",
    "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix",
    "type-check": "vue-tsc --noEmit"
  }
}
```

### 8.2 Docker部署
```dockerfile
# Dockerfile
FROM node:18-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

```nginx
# nginx.conf
server {
    listen 80;
    server_name localhost;

    location / {
        root /usr/share/nginx/html;
        index index.html;
        try_files $uri $uri/ /index.html;

        # 设置CORS头部
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```
