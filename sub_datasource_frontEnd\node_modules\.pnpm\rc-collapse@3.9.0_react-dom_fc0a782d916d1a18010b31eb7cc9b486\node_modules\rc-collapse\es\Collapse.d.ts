import React from 'react';
import type { CollapseProps } from './interface';
declare const _default: React.ForwardRefExoticComponent<CollapseProps & React.RefAttributes<HTMLDivElement>> & {
    /**
     * @deprecated use `items` instead, will be removed in `v4.0.0`
     */
    Panel: React.ForwardRefExoticComponent<import("./interface").CollapsePanelProps & React.RefAttributes<HTMLDivElement>>;
};
export default _default;
