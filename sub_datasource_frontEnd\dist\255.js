"use strict";(self.webpackChunkpageplug_datasource_frontend=self.webpackChunkpageplug_datasource_frontend||[]).push([[255],{2719:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval("{/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Ay: () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* unused harmony exports fetchDatasources, fetchDatasourceById, createDatasource, updateDatasource, deleteDatasource, testDatasourceConnection, fetchDatasourceStructure, setFilters, clearError, setCurrentDatasource, clearTestResult, clearStructure, selectDatasources, selectCurrentDatasource, selectDatasourceLoading, selectDatasourcePagination, selectDatasourceFilters, selectTestResult, selectStructure */\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(9235);\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(8076);\n/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(antd__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_apiClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(8876);\n\r\n\r\n\r\nconst fetchDatasources = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAsyncThunk)('datasource/fetchDatasources', async (params = {}) => {\r\n    const response = await _services_apiClient__WEBPACK_IMPORTED_MODULE_2__/* .apiClient */ .u.get('/datasources', { params });\r\n    return response.data;\r\n});\r\nconst fetchDatasourceById = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAsyncThunk)('datasource/fetchDatasourceById', async (id) => {\r\n    const response = await _services_apiClient__WEBPACK_IMPORTED_MODULE_2__/* .apiClient */ .u.get(`/datasources/${id}`);\r\n    return response.data;\r\n});\r\nconst createDatasource = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAsyncThunk)('datasource/createDatasource', async (request) => {\r\n    const response = await _services_apiClient__WEBPACK_IMPORTED_MODULE_2__/* .apiClient */ .u.post('/datasources', request);\r\n    antd__WEBPACK_IMPORTED_MODULE_1__.message.success('数据源创建成功');\r\n    return response.data;\r\n});\r\nconst updateDatasource = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAsyncThunk)('datasource/updateDatasource', async ({ id, request }) => {\r\n    const response = await _services_apiClient__WEBPACK_IMPORTED_MODULE_2__/* .apiClient */ .u.put(`/datasources/${id}`, request);\r\n    antd__WEBPACK_IMPORTED_MODULE_1__.message.success('数据源更新成功');\r\n    return response.data;\r\n});\r\nconst deleteDatasource = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAsyncThunk)('datasource/deleteDatasource', async (id) => {\r\n    await _services_apiClient__WEBPACK_IMPORTED_MODULE_2__/* .apiClient */ .u.delete(`/datasources/${id}`);\r\n    antd__WEBPACK_IMPORTED_MODULE_1__.message.success('数据源删除成功');\r\n    return id;\r\n});\r\nconst testDatasourceConnection = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAsyncThunk)('datasource/testConnection', async (id) => {\r\n    const response = await _services_apiClient__WEBPACK_IMPORTED_MODULE_2__/* .apiClient */ .u.post(`/datasources/${id}/test`);\r\n    return { id, result: response.data };\r\n});\r\nconst fetchDatasourceStructure = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAsyncThunk)('datasource/fetchStructure', async (id) => {\r\n    const response = await _services_apiClient__WEBPACK_IMPORTED_MODULE_2__/* .apiClient */ .u.get(`/datasources/${id}/structure`);\r\n    return { id, structure: response.data };\r\n});\r\nconst initialState = {\r\n    datasources: [],\r\n    pagination: {\r\n        current: 1,\r\n        pageSize: 10,\r\n        total: 0\r\n    },\r\n    currentDatasource: null,\r\n    structures: {},\r\n    testResults: {},\r\n    loading: {\r\n        list: false,\r\n        detail: false,\r\n        create: false,\r\n        update: false,\r\n        delete: false,\r\n        test: false,\r\n        structure: false\r\n    },\r\n    error: null,\r\n    filters: {}\r\n};\r\nconst datasourceSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({\r\n    name: 'datasource',\r\n    initialState,\r\n    reducers: {\r\n        setFilters: (state, action) => {\r\n            state.filters = { ...state.filters, ...action.payload };\r\n        },\r\n        clearError: (state) => {\r\n            state.error = null;\r\n        },\r\n        setCurrentDatasource: (state, action) => {\r\n            state.currentDatasource = action.payload;\r\n        },\r\n        clearTestResult: (state, action) => {\r\n            delete state.testResults[action.payload];\r\n        },\r\n        clearStructure: (state, action) => {\r\n            delete state.structures[action.payload];\r\n        }\r\n    },\r\n    extraReducers: (builder) => {\r\n        builder\r\n            .addCase(fetchDatasources.pending, (state) => {\r\n            state.loading.list = true;\r\n            state.error = null;\r\n        })\r\n            .addCase(fetchDatasources.fulfilled, (state, action) => {\r\n            state.loading.list = false;\r\n            state.datasources = action.payload.content;\r\n            state.pagination = {\r\n                current: action.payload.pagination.page,\r\n                pageSize: action.payload.pagination.size,\r\n                total: action.payload.pagination.total\r\n            };\r\n        })\r\n            .addCase(fetchDatasources.rejected, (state, action) => {\r\n            state.loading.list = false;\r\n            state.error = action.error.message || '获取数据源列表失败';\r\n        });\r\n        builder\r\n            .addCase(fetchDatasourceById.pending, (state) => {\r\n            state.loading.detail = true;\r\n            state.error = null;\r\n        })\r\n            .addCase(fetchDatasourceById.fulfilled, (state, action) => {\r\n            state.loading.detail = false;\r\n            state.currentDatasource = action.payload;\r\n            const index = state.datasources.findIndex(ds => ds.id === action.payload.id);\r\n            if (index !== -1) {\r\n                state.datasources[index] = action.payload;\r\n            }\r\n        })\r\n            .addCase(fetchDatasourceById.rejected, (state, action) => {\r\n            state.loading.detail = false;\r\n            state.error = action.error.message || '获取数据源详情失败';\r\n        });\r\n        builder\r\n            .addCase(createDatasource.pending, (state) => {\r\n            state.loading.create = true;\r\n            state.error = null;\r\n        })\r\n            .addCase(createDatasource.fulfilled, (state, action) => {\r\n            state.loading.create = false;\r\n            state.datasources.unshift(action.payload);\r\n            state.pagination.total += 1;\r\n        })\r\n            .addCase(createDatasource.rejected, (state, action) => {\r\n            state.loading.create = false;\r\n            state.error = action.error.message || '创建数据源失败';\r\n        });\r\n        builder\r\n            .addCase(updateDatasource.pending, (state) => {\r\n            state.loading.update = true;\r\n            state.error = null;\r\n        })\r\n            .addCase(updateDatasource.fulfilled, (state, action) => {\r\n            state.loading.update = false;\r\n            const index = state.datasources.findIndex(ds => ds.id === action.payload.id);\r\n            if (index !== -1) {\r\n                state.datasources[index] = action.payload;\r\n            }\r\n            if (state.currentDatasource?.id === action.payload.id) {\r\n                state.currentDatasource = action.payload;\r\n            }\r\n        })\r\n            .addCase(updateDatasource.rejected, (state, action) => {\r\n            state.loading.update = false;\r\n            state.error = action.error.message || '更新数据源失败';\r\n        });\r\n        builder\r\n            .addCase(deleteDatasource.pending, (state) => {\r\n            state.loading.delete = true;\r\n            state.error = null;\r\n        })\r\n            .addCase(deleteDatasource.fulfilled, (state, action) => {\r\n            state.loading.delete = false;\r\n            state.datasources = state.datasources.filter(ds => ds.id !== action.payload);\r\n            state.pagination.total -= 1;\r\n            if (state.currentDatasource?.id === action.payload) {\r\n                state.currentDatasource = null;\r\n            }\r\n            delete state.testResults[action.payload];\r\n            delete state.structures[action.payload];\r\n        })\r\n            .addCase(deleteDatasource.rejected, (state, action) => {\r\n            state.loading.delete = false;\r\n            state.error = action.error.message || '删除数据源失败';\r\n        });\r\n        builder\r\n            .addCase(testDatasourceConnection.pending, (state) => {\r\n            state.loading.test = true;\r\n            state.error = null;\r\n        })\r\n            .addCase(testDatasourceConnection.fulfilled, (state, action) => {\r\n            state.loading.test = false;\r\n            state.testResults[action.payload.id] = action.payload.result;\r\n        })\r\n            .addCase(testDatasourceConnection.rejected, (state, action) => {\r\n            state.loading.test = false;\r\n            state.error = action.error.message || '连接测试失败';\r\n        });\r\n        builder\r\n            .addCase(fetchDatasourceStructure.pending, (state) => {\r\n            state.loading.structure = true;\r\n            state.error = null;\r\n        })\r\n            .addCase(fetchDatasourceStructure.fulfilled, (state, action) => {\r\n            state.loading.structure = false;\r\n            state.structures[action.payload.id] = action.payload.structure;\r\n        })\r\n            .addCase(fetchDatasourceStructure.rejected, (state, action) => {\r\n            state.loading.structure = false;\r\n            state.error = action.error.message || '获取数据源结构失败';\r\n        });\r\n    }\r\n});\r\nconst { setFilters, clearError, setCurrentDatasource, clearTestResult, clearStructure } = datasourceSlice.actions;\r\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (datasourceSlice.reducer);\r\nconst selectDatasources = (state) => state.datasource.datasources;\r\nconst selectCurrentDatasource = (state) => state.datasource.currentDatasource;\r\nconst selectDatasourceLoading = (state) => state.datasource.loading;\r\nconst selectDatasourcePagination = (state) => state.datasource.pagination;\r\nconst selectDatasourceFilters = (state) => state.datasource.filters;\r\nconst selectTestResult = (id) => (state) => state.datasource.testResults[id];\r\nconst selectStructure = (id) => (state) => state.datasource.structures[id];\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///2719\n\n}")},4629:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval("{/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Ay: () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* unused harmony exports fetchPlugins, fetchPluginById, setFilters, clearError, setCurrentPlugin, resetFilters, selectPlugins, selectCurrentPlugin, selectPluginLoading, selectPluginFilters, selectPluginStats, selectPluginError, selectFilteredPlugins, selectPluginsByCategory, selectPluginsByStatus, selectActivePlugins */\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(9235);\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _services_apiClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(8876);\n\r\n\r\nconst fetchPlugins = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAsyncThunk)('plugin/fetchPlugins', async (params = {}) => {\r\n    const response = await _services_apiClient__WEBPACK_IMPORTED_MODULE_1__/* .apiClient */ .u.get('/plugins', { params });\r\n    return response.data;\r\n});\r\nconst fetchPluginById = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAsyncThunk)('plugin/fetchPluginById', async (id) => {\r\n    const response = await _services_apiClient__WEBPACK_IMPORTED_MODULE_1__/* .apiClient */ .u.get(`/plugins/${id}`);\r\n    return response.data;\r\n});\r\nconst initialState = {\r\n    plugins: [],\r\n    currentPlugin: null,\r\n    loading: {\r\n        list: false,\r\n        detail: false\r\n    },\r\n    error: null,\r\n    filters: {},\r\n    stats: {\r\n        total: 0,\r\n        byCategory: {},\r\n        byStatus: {}\r\n    }\r\n};\r\nconst calculateStats = (plugins) => {\r\n    const stats = {\r\n        total: plugins.length,\r\n        byCategory: {},\r\n        byStatus: {}\r\n    };\r\n    plugins.forEach(plugin => {\r\n        stats.byCategory[plugin.category] = (stats.byCategory[plugin.category] || 0) + 1;\r\n        stats.byStatus[plugin.status] = (stats.byStatus[plugin.status] || 0) + 1;\r\n    });\r\n    return stats;\r\n};\r\nconst pluginSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({\r\n    name: 'plugin',\r\n    initialState,\r\n    reducers: {\r\n        setFilters: (state, action) => {\r\n            state.filters = { ...state.filters, ...action.payload };\r\n        },\r\n        clearError: (state) => {\r\n            state.error = null;\r\n        },\r\n        setCurrentPlugin: (state, action) => {\r\n            state.currentPlugin = action.payload;\r\n        },\r\n        resetFilters: (state) => {\r\n            state.filters = {};\r\n        }\r\n    },\r\n    extraReducers: (builder) => {\r\n        builder\r\n            .addCase(fetchPlugins.pending, (state) => {\r\n            state.loading.list = true;\r\n            state.error = null;\r\n        })\r\n            .addCase(fetchPlugins.fulfilled, (state, action) => {\r\n            state.loading.list = false;\r\n            state.plugins = action.payload;\r\n            state.stats = calculateStats(action.payload);\r\n        })\r\n            .addCase(fetchPlugins.rejected, (state, action) => {\r\n            state.loading.list = false;\r\n            state.error = action.error.message || '获取插件列表失败';\r\n        });\r\n        builder\r\n            .addCase(fetchPluginById.pending, (state) => {\r\n            state.loading.detail = true;\r\n            state.error = null;\r\n        })\r\n            .addCase(fetchPluginById.fulfilled, (state, action) => {\r\n            state.loading.detail = false;\r\n            state.currentPlugin = action.payload;\r\n            const index = state.plugins.findIndex(plugin => plugin.id === action.payload.id);\r\n            if (index !== -1) {\r\n                state.plugins[index] = action.payload;\r\n            }\r\n            else {\r\n                state.plugins.push(action.payload);\r\n            }\r\n            state.stats = calculateStats(state.plugins);\r\n        })\r\n            .addCase(fetchPluginById.rejected, (state, action) => {\r\n            state.loading.detail = false;\r\n            state.error = action.error.message || '获取插件详情失败';\r\n        });\r\n    }\r\n});\r\nconst { setFilters, clearError, setCurrentPlugin, resetFilters } = pluginSlice.actions;\r\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (pluginSlice.reducer);\r\nconst selectPlugins = (state) => state.plugin.plugins;\r\nconst selectCurrentPlugin = (state) => state.plugin.currentPlugin;\r\nconst selectPluginLoading = (state) => state.plugin.loading;\r\nconst selectPluginFilters = (state) => state.plugin.filters;\r\nconst selectPluginStats = (state) => state.plugin.stats;\r\nconst selectPluginError = (state) => state.plugin.error;\r\nconst selectFilteredPlugins = (state) => {\r\n    const { plugins, filters } = state.plugin;\r\n    return plugins.filter(plugin => {\r\n        if (filters.category && plugin.category !== filters.category) {\r\n            return false;\r\n        }\r\n        if (filters.status && plugin.status !== filters.status) {\r\n            return false;\r\n        }\r\n        if (filters.searchText) {\r\n            const searchText = filters.searchText.toLowerCase();\r\n            return (plugin.name.toLowerCase().includes(searchText) ||\r\n                plugin.displayName.toLowerCase().includes(searchText) ||\r\n                plugin.description.toLowerCase().includes(searchText));\r\n        }\r\n        return true;\r\n    });\r\n};\r\nconst selectPluginsByCategory = (category) => (state) => {\r\n    return state.plugin.plugins.filter(plugin => plugin.category === category);\r\n};\r\nconst selectPluginsByStatus = (status) => (state) => {\r\n    return state.plugin.plugins.filter(plugin => plugin.status === status);\r\n};\r\nconst selectActivePlugins = (state) => {\r\n    return state.plugin.plugins.filter(plugin => plugin.status === 'active');\r\n};\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///4629\n\n}")},5478:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval("{/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Ay: () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* unused harmony exports toggleSidebar, setSidebarCollapsed, setSidebarSelectedKey, setThemeMode, setPrimaryColor, setPageLoading, addNotification, markNotificationAsRead, markAllNotificationsAsRead, removeNotification, clearAllNotifications, setModalVisible, closeAllModals, setTablePageSize, setTableSettings, setQueryEditorFontSize, setQueryEditorSettings, setBreadcrumb, addBreadcrumbItem, clearBreadcrumb, setGlobalSearchVisible, setGlobalSearchQuery, setGlobalSearchResults, setGlobalSearchLoading, selectSidebar, selectTheme, selectPageLoading, selectNotifications, selectUnreadNotifications, selectModals, selectTableSettings, selectQueryEditorSettings, selectBreadcrumb, selectGlobalSearch */\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(9235);\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__);\n\r\nconst initialState = {\r\n    sidebar: {\r\n        collapsed: false,\r\n        selectedKey: '/datasources'\r\n    },\r\n    theme: {\r\n        mode: 'light',\r\n        primaryColor: '#1890ff'\r\n    },\r\n    pageLoading: false,\r\n    notifications: [],\r\n    modals: {\r\n        datasourceCreate: false,\r\n        datasourceEdit: false,\r\n        queryEditor: false,\r\n        pluginDetail: false\r\n    },\r\n    tableSettings: {\r\n        pageSize: 10,\r\n        showSizeChanger: true,\r\n        showQuickJumper: true\r\n    },\r\n    queryEditor: {\r\n        fontSize: 14,\r\n        theme: 'light',\r\n        wordWrap: true,\r\n        showLineNumbers: true,\r\n        autoComplete: true\r\n    },\r\n    breadcrumb: [],\r\n    globalSearch: {\r\n        visible: false,\r\n        query: '',\r\n        results: [],\r\n        loading: false\r\n    }\r\n};\r\nconst uiSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({\r\n    name: 'ui',\r\n    initialState,\r\n    reducers: {\r\n        toggleSidebar: (state) => {\r\n            state.sidebar.collapsed = !state.sidebar.collapsed;\r\n        },\r\n        setSidebarCollapsed: (state, action) => {\r\n            state.sidebar.collapsed = action.payload;\r\n        },\r\n        setSidebarSelectedKey: (state, action) => {\r\n            state.sidebar.selectedKey = action.payload;\r\n        },\r\n        setThemeMode: (state, action) => {\r\n            state.theme.mode = action.payload;\r\n            state.queryEditor.theme = action.payload;\r\n        },\r\n        setPrimaryColor: (state, action) => {\r\n            state.theme.primaryColor = action.payload;\r\n        },\r\n        setPageLoading: (state, action) => {\r\n            state.pageLoading = action.payload;\r\n        },\r\n        addNotification: (state, action) => {\r\n            const notification = {\r\n                ...action.payload,\r\n                id: `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\r\n                timestamp: new Date().toISOString(),\r\n                read: false\r\n            };\r\n            state.notifications.unshift(notification);\r\n            if (state.notifications.length > 50) {\r\n                state.notifications = state.notifications.slice(0, 50);\r\n            }\r\n        },\r\n        markNotificationAsRead: (state, action) => {\r\n            const notification = state.notifications.find(n => n.id === action.payload);\r\n            if (notification) {\r\n                notification.read = true;\r\n            }\r\n        },\r\n        markAllNotificationsAsRead: (state) => {\r\n            state.notifications.forEach(notification => {\r\n                notification.read = true;\r\n            });\r\n        },\r\n        removeNotification: (state, action) => {\r\n            state.notifications = state.notifications.filter(n => n.id !== action.payload);\r\n        },\r\n        clearAllNotifications: (state) => {\r\n            state.notifications = [];\r\n        },\r\n        setModalVisible: (state, action) => {\r\n            const { modal, visible } = action.payload;\r\n            state.modals[modal] = visible;\r\n        },\r\n        closeAllModals: (state) => {\r\n            Object.keys(state.modals).forEach(key => {\r\n                state.modals[key] = false;\r\n            });\r\n        },\r\n        setTablePageSize: (state, action) => {\r\n            state.tableSettings.pageSize = action.payload;\r\n        },\r\n        setTableSettings: (state, action) => {\r\n            state.tableSettings = { ...state.tableSettings, ...action.payload };\r\n        },\r\n        setQueryEditorFontSize: (state, action) => {\r\n            state.queryEditor.fontSize = action.payload;\r\n        },\r\n        setQueryEditorSettings: (state, action) => {\r\n            state.queryEditor = { ...state.queryEditor, ...action.payload };\r\n        },\r\n        setBreadcrumb: (state, action) => {\r\n            state.breadcrumb = action.payload;\r\n        },\r\n        addBreadcrumbItem: (state, action) => {\r\n            state.breadcrumb.push(action.payload);\r\n        },\r\n        clearBreadcrumb: (state) => {\r\n            state.breadcrumb = [];\r\n        },\r\n        setGlobalSearchVisible: (state, action) => {\r\n            state.globalSearch.visible = action.payload;\r\n            if (!action.payload) {\r\n                state.globalSearch.query = '';\r\n                state.globalSearch.results = [];\r\n            }\r\n        },\r\n        setGlobalSearchQuery: (state, action) => {\r\n            state.globalSearch.query = action.payload;\r\n        },\r\n        setGlobalSearchResults: (state, action) => {\r\n            state.globalSearch.results = action.payload;\r\n        },\r\n        setGlobalSearchLoading: (state, action) => {\r\n            state.globalSearch.loading = action.payload;\r\n        }\r\n    }\r\n});\r\nconst { toggleSidebar, setSidebarCollapsed, setSidebarSelectedKey, setThemeMode, setPrimaryColor, setPageLoading, addNotification, markNotificationAsRead, markAllNotificationsAsRead, removeNotification, clearAllNotifications, setModalVisible, closeAllModals, setTablePageSize, setTableSettings, setQueryEditorFontSize, setQueryEditorSettings, setBreadcrumb, addBreadcrumbItem, clearBreadcrumb, setGlobalSearchVisible, setGlobalSearchQuery, setGlobalSearchResults, setGlobalSearchLoading } = uiSlice.actions;\r\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (uiSlice.reducer);\r\nconst selectSidebar = (state) => state.ui.sidebar;\r\nconst selectTheme = (state) => state.ui.theme;\r\nconst selectPageLoading = (state) => state.ui.pageLoading;\r\nconst selectNotifications = (state) => state.ui.notifications;\r\nconst selectUnreadNotifications = (state) => state.ui.notifications.filter(n => !n.read);\r\nconst selectModals = (state) => state.ui.modals;\r\nconst selectTableSettings = (state) => state.ui.tableSettings;\r\nconst selectQueryEditorSettings = (state) => state.ui.queryEditor;\r\nconst selectBreadcrumb = (state) => state.ui.breadcrumb;\r\nconst selectGlobalSearch = (state) => state.ui.globalSearch;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///5478\n\n}")},7166:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval("{/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Ay: () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* unused harmony exports fetchQueries, createQuery, executeQuery, executeSavedQuery, setCurrentQueryText, setCurrentDatasourceId, setQueryParameters, addQueryParameter, removeQueryParameter, updateQueryParameter, setFilters, clearError, clearExecutionResult, clearAllExecutionResults, loadQueryToEditor, clearQueryHistory, selectSavedQueries, selectCurrentQuery, selectQueryLoading, selectQueryError, selectQueryFilters, selectQueryHistory, selectExecutionResults, selectExecutionResult, selectFilteredQueries */\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(9235);\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(8076);\n/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(antd__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_apiClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(8876);\n\r\n\r\n\r\nconst fetchQueries = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAsyncThunk)('query/fetchQueries', async (params = {}) => {\r\n    const response = await _services_apiClient__WEBPACK_IMPORTED_MODULE_2__/* .apiClient */ .u.get('/queries', { params });\r\n    return response.data;\r\n});\r\nconst createQuery = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAsyncThunk)('query/createQuery', async (request) => {\r\n    const response = await _services_apiClient__WEBPACK_IMPORTED_MODULE_2__/* .apiClient */ .u.post('/queries', request);\r\n    antd__WEBPACK_IMPORTED_MODULE_1__.message.success('查询保存成功');\r\n    return response.data;\r\n});\r\nconst executeQuery = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAsyncThunk)('query/executeQuery', async ({ datasourceId, request }) => {\r\n    const response = await _services_apiClient__WEBPACK_IMPORTED_MODULE_2__/* .apiClient */ .u.post(`/datasources/${datasourceId}/execute`, request);\r\n    return { datasourceId, request, result: response.data };\r\n});\r\nconst executeSavedQuery = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAsyncThunk)('query/executeSavedQuery', async ({ queryId, parameters }) => {\r\n    const response = await _services_apiClient__WEBPACK_IMPORTED_MODULE_2__/* .apiClient */ .u.post(`/queries/${queryId}/execute`, { parameters });\r\n    return { queryId, result: response.data };\r\n});\r\nconst initialState = {\r\n    savedQueries: [],\r\n    currentQuery: {\r\n        text: '',\r\n        datasourceId: '',\r\n        parameters: []\r\n    },\r\n    executionResults: {},\r\n    queryHistory: [],\r\n    loading: {\r\n        list: false,\r\n        create: false,\r\n        execute: false\r\n    },\r\n    error: null,\r\n    filters: {}\r\n};\r\nconst querySlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({\r\n    name: 'query',\r\n    initialState,\r\n    reducers: {\r\n        setCurrentQueryText: (state, action) => {\r\n            state.currentQuery.text = action.payload;\r\n        },\r\n        setCurrentDatasourceId: (state, action) => {\r\n            state.currentQuery.datasourceId = action.payload;\r\n        },\r\n        setQueryParameters: (state, action) => {\r\n            state.currentQuery.parameters = action.payload;\r\n        },\r\n        addQueryParameter: (state, action) => {\r\n            state.currentQuery.parameters.push(action.payload);\r\n        },\r\n        removeQueryParameter: (state, action) => {\r\n            state.currentQuery.parameters.splice(action.payload, 1);\r\n        },\r\n        updateQueryParameter: (state, action) => {\r\n            const { index, parameter } = action.payload;\r\n            if (state.currentQuery.parameters[index]) {\r\n                state.currentQuery.parameters[index] = parameter;\r\n            }\r\n        },\r\n        setFilters: (state, action) => {\r\n            state.filters = { ...state.filters, ...action.payload };\r\n        },\r\n        clearError: (state) => {\r\n            state.error = null;\r\n        },\r\n        clearExecutionResult: (state, action) => {\r\n            delete state.executionResults[action.payload];\r\n        },\r\n        clearAllExecutionResults: (state) => {\r\n            state.executionResults = {};\r\n        },\r\n        loadQueryToEditor: (state, action) => {\r\n            const query = action.payload;\r\n            state.currentQuery = {\r\n                text: query.query,\r\n                datasourceId: query.datasourceId,\r\n                parameters: query.parameters\r\n            };\r\n        },\r\n        clearQueryHistory: (state) => {\r\n            state.queryHistory = [];\r\n        }\r\n    },\r\n    extraReducers: (builder) => {\r\n        builder\r\n            .addCase(fetchQueries.pending, (state) => {\r\n            state.loading.list = true;\r\n            state.error = null;\r\n        })\r\n            .addCase(fetchQueries.fulfilled, (state, action) => {\r\n            state.loading.list = false;\r\n            state.savedQueries = action.payload;\r\n        })\r\n            .addCase(fetchQueries.rejected, (state, action) => {\r\n            state.loading.list = false;\r\n            state.error = action.error.message || '获取查询列表失败';\r\n        });\r\n        builder\r\n            .addCase(createQuery.pending, (state) => {\r\n            state.loading.create = true;\r\n            state.error = null;\r\n        })\r\n            .addCase(createQuery.fulfilled, (state, action) => {\r\n            state.loading.create = false;\r\n            state.savedQueries.unshift(action.payload);\r\n        })\r\n            .addCase(createQuery.rejected, (state, action) => {\r\n            state.loading.create = false;\r\n            state.error = action.error.message || '保存查询失败';\r\n        });\r\n        builder\r\n            .addCase(executeQuery.pending, (state) => {\r\n            state.loading.execute = true;\r\n            state.error = null;\r\n        })\r\n            .addCase(executeQuery.fulfilled, (state, action) => {\r\n            state.loading.execute = false;\r\n            const { datasourceId, request, result } = action.payload;\r\n            const resultKey = `${datasourceId}_${Date.now()}`;\r\n            state.executionResults[resultKey] = result;\r\n            state.queryHistory.unshift({\r\n                id: resultKey,\r\n                query: request.query,\r\n                datasourceId,\r\n                executedAt: new Date().toISOString(),\r\n                success: result.isExecutionSuccess,\r\n                executionTime: result.executionTime\r\n            });\r\n            if (state.queryHistory.length > 50) {\r\n                state.queryHistory = state.queryHistory.slice(0, 50);\r\n            }\r\n        })\r\n            .addCase(executeQuery.rejected, (state, action) => {\r\n            state.loading.execute = false;\r\n            state.error = action.error.message || '查询执行失败';\r\n        });\r\n        builder\r\n            .addCase(executeSavedQuery.pending, (state) => {\r\n            state.loading.execute = true;\r\n            state.error = null;\r\n        })\r\n            .addCase(executeSavedQuery.fulfilled, (state, action) => {\r\n            state.loading.execute = false;\r\n            const { queryId, result } = action.payload;\r\n            state.executionResults[queryId] = result;\r\n            const query = state.savedQueries.find(q => q.id === queryId);\r\n            if (query) {\r\n                query.lastExecuted = new Date().toISOString();\r\n                state.queryHistory.unshift({\r\n                    id: `${queryId}_${Date.now()}`,\r\n                    query: query.query,\r\n                    datasourceId: query.datasourceId,\r\n                    executedAt: new Date().toISOString(),\r\n                    success: result.isExecutionSuccess,\r\n                    executionTime: result.executionTime\r\n                });\r\n            }\r\n        })\r\n            .addCase(executeSavedQuery.rejected, (state, action) => {\r\n            state.loading.execute = false;\r\n            state.error = action.error.message || '执行保存的查询失败';\r\n        });\r\n    }\r\n});\r\nconst { setCurrentQueryText, setCurrentDatasourceId, setQueryParameters, addQueryParameter, removeQueryParameter, updateQueryParameter, setFilters, clearError, clearExecutionResult, clearAllExecutionResults, loadQueryToEditor, clearQueryHistory } = querySlice.actions;\r\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (querySlice.reducer);\r\nconst selectSavedQueries = (state) => state.query.savedQueries;\r\nconst selectCurrentQuery = (state) => state.query.currentQuery;\r\nconst selectQueryLoading = (state) => state.query.loading;\r\nconst selectQueryError = (state) => state.query.error;\r\nconst selectQueryFilters = (state) => state.query.filters;\r\nconst selectQueryHistory = (state) => state.query.queryHistory;\r\nconst selectExecutionResults = (state) => state.query.executionResults;\r\nconst selectExecutionResult = (key) => (state) => state.query.executionResults[key];\r\nconst selectFilteredQueries = (state) => {\r\n    const { savedQueries, filters } = state.query;\r\n    return savedQueries.filter(query => {\r\n        if (filters.datasourceId && query.datasourceId !== filters.datasourceId) {\r\n            return false;\r\n        }\r\n        if (filters.tag && !query.tags.includes(filters.tag)) {\r\n            return false;\r\n        }\r\n        return true;\r\n    });\r\n};\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNzE2Ni5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBZ0Y7QUFDakQ7QUFHc0I7QUFHOUMsTUFBTSxZQUFZLEdBQUcsa0VBQWdCLENBQzFDLG9CQUFvQixFQUNwQixLQUFLLEVBQUUsU0FHSCxFQUFFLEVBQUUsRUFBRTtJQUNSLE1BQU0sUUFBUSxHQUFHLE1BQU0sbUVBQVMsQ0FBQyxHQUFHLENBQUMsVUFBVSxFQUFFLEVBQUUsTUFBTSxFQUFFLENBQUMsQ0FBQztJQUM3RCxPQUFPLFFBQVEsQ0FBQyxJQUFJLENBQUM7QUFDdkIsQ0FBQyxDQUNGLENBQUM7QUFFSyxNQUFNLFdBQVcsR0FBRyxrRUFBZ0IsQ0FDekMsbUJBQW1CLEVBQ25CLEtBQUssRUFBRSxPQUEyQixFQUFFLEVBQUU7SUFDcEMsTUFBTSxRQUFRLEdBQUcsTUFBTSxtRUFBUyxDQUFDLElBQUksQ0FBQyxVQUFVLEVBQUUsT0FBTyxDQUFDLENBQUM7SUFDM0QseUNBQU8sQ0FBQyxPQUFPLENBQUMsUUFBUSxDQUFDLENBQUM7SUFDMUIsT0FBTyxRQUFRLENBQUMsSUFBSSxDQUFDO0FBQ3ZCLENBQUMsQ0FDRixDQUFDO0FBRUssTUFBTSxZQUFZLEdBQUcsa0VBQWdCLENBQzFDLG9CQUFvQixFQUNwQixLQUFLLEVBQUUsRUFBRSxZQUFZLEVBQUUsT0FBTyxFQUEwRCxFQUFFLEVBQUU7SUFDMUYsTUFBTSxRQUFRLEdBQUcsTUFBTSxtRUFBUyxDQUFDLElBQUksQ0FBQyxnQkFBZ0IsWUFBWSxVQUFVLEVBQUUsT0FBTyxDQUFDLENBQUM7SUFDdkYsT0FBTyxFQUFFLFlBQVksRUFBRSxPQUFPLEVBQUUsTUFBTSxFQUFFLFFBQVEsQ0FBQyxJQUFJLEVBQUUsQ0FBQztBQUMxRCxDQUFDLENBQ0YsQ0FBQztBQUVLLE1BQU0saUJBQWlCLEdBQUcsa0VBQWdCLENBQy9DLHlCQUF5QixFQUN6QixLQUFLLEVBQUUsRUFBRSxPQUFPLEVBQUUsVUFBVSxFQUF3RSxFQUFFLEVBQUU7SUFDdEcsTUFBTSxRQUFRLEdBQUcsTUFBTSxtRUFBUyxDQUFDLElBQUksQ0FBQyxZQUFZLE9BQU8sVUFBVSxFQUFFLEVBQUUsVUFBVSxFQUFFLENBQUMsQ0FBQztJQUNyRixPQUFPLEVBQUUsT0FBTyxFQUFFLE1BQU0sRUFBRSxRQUFRLENBQUMsSUFBSSxFQUFFLENBQUM7QUFDNUMsQ0FBQyxDQUNGLENBQUM7QUE2Q0YsTUFBTSxZQUFZLEdBQWU7SUFDL0IsWUFBWSxFQUFFLEVBQUU7SUFDaEIsWUFBWSxFQUFFO1FBQ1osSUFBSSxFQUFFLEVBQUU7UUFDUixZQUFZLEVBQUUsRUFBRTtRQUNoQixVQUFVLEVBQUUsRUFBRTtLQUNmO0lBQ0QsZ0JBQWdCLEVBQUUsRUFBRTtJQUNwQixZQUFZLEVBQUUsRUFBRTtJQUNoQixPQUFPLEVBQUU7UUFDUCxJQUFJLEVBQUUsS0FBSztRQUNYLE1BQU0sRUFBRSxLQUFLO1FBQ2IsT0FBTyxFQUFFLEtBQUs7S0FDZjtJQUNELEtBQUssRUFBRSxJQUFJO0lBQ1gsT0FBTyxFQUFFLEVBQUU7Q0FDWixDQUFDO0FBR0YsTUFBTSxVQUFVLEdBQUcsNkRBQVcsQ0FBQztJQUM3QixJQUFJLEVBQUUsT0FBTztJQUNiLFlBQVk7SUFDWixRQUFRLEVBQUU7UUFFUixtQkFBbUIsRUFBRSxDQUFDLEtBQUssRUFBRSxNQUE2QixFQUFFLEVBQUU7WUFDNUQsS0FBSyxDQUFDLFlBQVksQ0FBQyxJQUFJLEdBQUcsTUFBTSxDQUFDLE9BQU8sQ0FBQztRQUMzQyxDQUFDO1FBR0Qsc0JBQXNCLEVBQUUsQ0FBQyxLQUFLLEVBQUUsTUFBNkIsRUFBRSxFQUFFO1lBQy9ELEtBQUssQ0FBQyxZQUFZLENBQUMsWUFBWSxHQUFHLE1BQU0sQ0FBQyxPQUFPLENBQUM7UUFDbkQsQ0FBQztRQUdELGtCQUFrQixFQUFFLENBQUMsS0FBSyxFQUFFLE1BQXdFLEVBQUUsRUFBRTtZQUN0RyxLQUFLLENBQUMsWUFBWSxDQUFDLFVBQVUsR0FBRyxNQUFNLENBQUMsT0FBTyxDQUFDO1FBQ2pELENBQUM7UUFHRCxpQkFBaUIsRUFBRSxDQUFDLEtBQUssRUFBRSxNQUFpRSxFQUFFLEVBQUU7WUFDOUYsS0FBSyxDQUFDLFlBQVksQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQztRQUNyRCxDQUFDO1FBR0Qsb0JBQW9CLEVBQUUsQ0FBQyxLQUFLLEVBQUUsTUFBNkIsRUFBRSxFQUFFO1lBQzdELEtBQUssQ0FBQyxZQUFZLENBQUMsVUFBVSxDQUFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsT0FBTyxFQUFFLENBQUMsQ0FBQyxDQUFDO1FBQzFELENBQUM7UUFHRCxvQkFBb0IsRUFBRSxDQUFDLEtBQUssRUFBRSxNQUErRixFQUFFLEVBQUU7WUFDL0gsTUFBTSxFQUFFLEtBQUssRUFBRSxTQUFTLEVBQUUsR0FBRyxNQUFNLENBQUMsT0FBTyxDQUFDO1lBQzVDLElBQUksS0FBSyxDQUFDLFlBQVksQ0FBQyxVQUFVLENBQUMsS0FBSyxDQUFDLEVBQUU7Z0JBQ3hDLEtBQUssQ0FBQyxZQUFZLENBQUMsVUFBVSxDQUFDLEtBQUssQ0FBQyxHQUFHLFNBQVMsQ0FBQzthQUNsRDtRQUNILENBQUM7UUFHRCxVQUFVLEVBQUUsQ0FBQyxLQUFLLEVBQUUsTUFBcUQsRUFBRSxFQUFFO1lBQzNFLEtBQUssQ0FBQyxPQUFPLEdBQUcsRUFBRSxHQUFHLEtBQUssQ0FBQyxPQUFPLEVBQUUsR0FBRyxNQUFNLENBQUMsT0FBTyxFQUFFLENBQUM7UUFDMUQsQ0FBQztRQUdELFVBQVUsRUFBRSxDQUFDLEtBQUssRUFBRSxFQUFFO1lBQ3BCLEtBQUssQ0FBQyxLQUFLLEdBQUcsSUFBSSxDQUFDO1FBQ3JCLENBQUM7UUFHRCxvQkFBb0IsRUFBRSxDQUFDLEtBQUssRUFBRSxNQUE2QixFQUFFLEVBQUU7WUFDN0QsT0FBTyxLQUFLLENBQUMsZ0JBQWdCLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDO1FBQ2hELENBQUM7UUFHRCx3QkFBd0IsRUFBRSxDQUFDLEtBQUssRUFBRSxFQUFFO1lBQ2xDLEtBQUssQ0FBQyxnQkFBZ0IsR0FBRyxFQUFFLENBQUM7UUFDOUIsQ0FBQztRQUdELGlCQUFpQixFQUFFLENBQUMsS0FBSyxFQUFFLE1BQTRCLEVBQUUsRUFBRTtZQUN6RCxNQUFNLEtBQUssR0FBRyxNQUFNLENBQUMsT0FBTyxDQUFDO1lBQzdCLEtBQUssQ0FBQyxZQUFZLEdBQUc7Z0JBQ25CLElBQUksRUFBRSxLQUFLLENBQUMsS0FBSztnQkFDakIsWUFBWSxFQUFFLEtBQUssQ0FBQyxZQUFZO2dCQUNoQyxVQUFVLEVBQUUsS0FBSyxDQUFDLFVBQVU7YUFDN0IsQ0FBQztRQUNKLENBQUM7UUFHRCxpQkFBaUIsRUFBRSxDQUFDLEtBQUssRUFBRSxFQUFFO1lBQzNCLEtBQUssQ0FBQyxZQUFZLEdBQUcsRUFBRSxDQUFDO1FBQzFCLENBQUM7S0FDRjtJQUNELGFBQWEsRUFBRSxDQUFDLE9BQU8sRUFBRSxFQUFFO1FBRXpCLE9BQU87YUFDSixPQUFPLENBQUMsWUFBWSxDQUFDLE9BQU8sRUFBRSxDQUFDLEtBQUssRUFBRSxFQUFFO1lBQ3ZDLEtBQUssQ0FBQyxPQUFPLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQztZQUMxQixLQUFLLENBQUMsS0FBSyxHQUFHLElBQUksQ0FBQztRQUNyQixDQUFDLENBQUM7YUFDRCxPQUFPLENBQUMsWUFBWSxDQUFDLFNBQVMsRUFBRSxDQUFDLEtBQUssRUFBRSxNQUE4QixFQUFFLEVBQUU7WUFDekUsS0FBSyxDQUFDLE9BQU8sQ0FBQyxJQUFJLEdBQUcsS0FBSyxDQUFDO1lBQzNCLEtBQUssQ0FBQyxZQUFZLEdBQUcsTUFBTSxDQUFDLE9BQU8sQ0FBQztRQUN0QyxDQUFDLENBQUM7YUFDRCxPQUFPLENBQUMsWUFBWSxDQUFDLFFBQVEsRUFBRSxDQUFDLEtBQUssRUFBRSxNQUFNLEVBQUUsRUFBRTtZQUNoRCxLQUFLLENBQUMsT0FBTyxDQUFDLElBQUksR0FBRyxLQUFLLENBQUM7WUFDM0IsS0FBSyxDQUFDLEtBQUssR0FBRyxNQUFNLENBQUMsS0FBSyxDQUFDLE9BQU8sSUFBSSxVQUFVLENBQUM7UUFDbkQsQ0FBQyxDQUFDLENBQUM7UUFHTCxPQUFPO2FBQ0osT0FBTyxDQUFDLFdBQVcsQ0FBQyxPQUFPLEVBQUUsQ0FBQyxLQUFLLEVBQUUsRUFBRTtZQUN0QyxLQUFLLENBQUMsT0FBTyxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUM7WUFDNUIsS0FBSyxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUM7UUFDckIsQ0FBQyxDQUFDO2FBQ0QsT0FBTyxDQUFDLFdBQVcsQ0FBQyxTQUFTLEVBQUUsQ0FBQyxLQUFLLEVBQUUsTUFBNEIsRUFBRSxFQUFFO1lBQ3RFLEtBQUssQ0FBQyxPQUFPLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQztZQUM3QixLQUFLLENBQUMsWUFBWSxDQUFDLE9BQU8sQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLENBQUM7UUFDN0MsQ0FBQyxDQUFDO2FBQ0QsT0FBTyxDQUFDLFdBQVcsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxLQUFLLEVBQUUsTUFBTSxFQUFFLEVBQUU7WUFDL0MsS0FBSyxDQUFDLE9BQU8sQ0FBQyxNQUFNLEdBQUcsS0FBSyxDQUFDO1lBQzdCLEtBQUssQ0FBQyxLQUFLLEdBQUcsTUFBTSxDQUFDLEtBQUssQ0FBQyxPQUFPLElBQUksUUFBUSxDQUFDO1FBQ2pELENBQUMsQ0FBQyxDQUFDO1FBR0wsT0FBTzthQUNKLE9BQU8sQ0FBQyxZQUFZLENBQUMsT0FBTyxFQUFFLENBQUMsS0FBSyxFQUFFLEVBQUU7WUFDdkMsS0FBSyxDQUFDLE9BQU8sQ0FBQyxPQUFPLEdBQUcsSUFBSSxDQUFDO1lBQzdCLEtBQUssQ0FBQyxLQUFLLEdBQUcsSUFBSSxDQUFDO1FBQ3JCLENBQUMsQ0FBQzthQUNELE9BQU8sQ0FBQyxZQUFZLENBQUMsU0FBUyxFQUFFLENBQUMsS0FBSyxFQUFFLE1BQU0sRUFBRSxFQUFFO1lBQ2pELEtBQUssQ0FBQyxPQUFPLENBQUMsT0FBTyxHQUFHLEtBQUssQ0FBQztZQUM5QixNQUFNLEVBQUUsWUFBWSxFQUFFLE9BQU8sRUFBRSxNQUFNLEVBQUUsR0FBRyxNQUFNLENBQUMsT0FBTyxDQUFDO1lBR3pELE1BQU0sU0FBUyxHQUFHLEdBQUcsWUFBWSxJQUFJLElBQUksQ0FBQyxHQUFHLEVBQUUsRUFBRSxDQUFDO1lBQ2xELEtBQUssQ0FBQyxnQkFBZ0IsQ0FBQyxTQUFTLENBQUMsR0FBRyxNQUFNLENBQUM7WUFHM0MsS0FBSyxDQUFDLFlBQVksQ0FBQyxPQUFPLENBQUM7Z0JBQ3pCLEVBQUUsRUFBRSxTQUFTO2dCQUNiLEtBQUssRUFBRSxPQUFPLENBQUMsS0FBSztnQkFDcEIsWUFBWTtnQkFDWixVQUFVLEVBQUUsSUFBSSxJQUFJLEVBQUUsQ0FBQyxXQUFXLEVBQUU7Z0JBQ3BDLE9BQU8sRUFBRSxNQUFNLENBQUMsa0JBQWtCO2dCQUNsQyxhQUFhLEVBQUUsTUFBTSxDQUFDLGFBQWE7YUFDcEMsQ0FBQyxDQUFDO1lBR0gsSUFBSSxLQUFLLENBQUMsWUFBWSxDQUFDLE1BQU0sR0FBRyxFQUFFLEVBQUU7Z0JBQ2xDLEtBQUssQ0FBQyxZQUFZLEdBQUcsS0FBSyxDQUFDLFlBQVksQ0FBQyxLQUFLLENBQUMsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxDQUFDO2FBQ3REO1FBQ0gsQ0FBQyxDQUFDO2FBQ0QsT0FBTyxDQUFDLFlBQVksQ0FBQyxRQUFRLEVBQUUsQ0FBQyxLQUFLLEVBQUUsTUFBTSxFQUFFLEVBQUU7WUFDaEQsS0FBSyxDQUFDLE9BQU8sQ0FBQyxPQUFPLEdBQUcsS0FBSyxDQUFDO1lBQzlCLEtBQUssQ0FBQyxLQUFLLEdBQUcsTUFBTSxDQUFDLEtBQUssQ0FBQyxPQUFPLElBQUksUUFBUSxDQUFDO1FBQ2pELENBQUMsQ0FBQyxDQUFDO1FBR0wsT0FBTzthQUNKLE9BQU8sQ0FBQyxpQkFBaUIsQ0FBQyxPQUFPLEVBQUUsQ0FBQyxLQUFLLEVBQUUsRUFBRTtZQUM1QyxLQUFLLENBQUMsT0FBTyxDQUFDLE9BQU8sR0FBRyxJQUFJLENBQUM7WUFDN0IsS0FBSyxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUM7UUFDckIsQ0FBQyxDQUFDO2FBQ0QsT0FBTyxDQUFDLGlCQUFpQixDQUFDLFNBQVMsRUFBRSxDQUFDLEtBQUssRUFBRSxNQUFNLEVBQUUsRUFBRTtZQUN0RCxLQUFLLENBQUMsT0FBTyxDQUFDLE9BQU8sR0FBRyxLQUFLLENBQUM7WUFDOUIsTUFBTSxFQUFFLE9BQU8sRUFBRSxNQUFNLEVBQUUsR0FBRyxNQUFNLENBQUMsT0FBTyxDQUFDO1lBRzNDLEtBQUssQ0FBQyxnQkFBZ0IsQ0FBQyxPQUFPLENBQUMsR0FBRyxNQUFNLENBQUM7WUFHekMsTUFBTSxLQUFLLEdBQUcsS0FBSyxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsRUFBRSxLQUFLLE9BQU8sQ0FBQyxDQUFDO1lBQzdELElBQUksS0FBSyxFQUFFO2dCQUNULEtBQUssQ0FBQyxZQUFZLEdBQUcsSUFBSSxJQUFJLEVBQUUsQ0FBQyxXQUFXLEVBQUUsQ0FBQztnQkFHOUMsS0FBSyxDQUFDLFlBQVksQ0FBQyxPQUFPLENBQUM7b0JBQ3pCLEVBQUUsRUFBRSxHQUFHLE9BQU8sSUFBSSxJQUFJLENBQUMsR0FBRyxFQUFFLEVBQUU7b0JBQzlCLEtBQUssRUFBRSxLQUFLLENBQUMsS0FBSztvQkFDbEIsWUFBWSxFQUFFLEtBQUssQ0FBQyxZQUFZO29CQUNoQyxVQUFVLEVBQUUsSUFBSSxJQUFJLEVBQUUsQ0FBQyxXQUFXLEVBQUU7b0JBQ3BDLE9BQU8sRUFBRSxNQUFNLENBQUMsa0JBQWtCO29CQUNsQyxhQUFhLEVBQUUsTUFBTSxDQUFDLGFBQWE7aUJBQ3BDLENBQUMsQ0FBQzthQUNKO1FBQ0gsQ0FBQyxDQUFDO2FBQ0QsT0FBTyxDQUFDLGlCQUFpQixDQUFDLFFBQVEsRUFBRSxDQUFDLEtBQUssRUFBRSxNQUFNLEVBQUUsRUFBRTtZQUNyRCxLQUFLLENBQUMsT0FBTyxDQUFDLE9BQU8sR0FBRyxLQUFLLENBQUM7WUFDOUIsS0FBSyxDQUFDLEtBQUssR0FBRyxNQUFNLENBQUMsS0FBSyxDQUFDLE9BQU8sSUFBSSxXQUFXLENBQUM7UUFDcEQsQ0FBQyxDQUFDLENBQUM7SUFDUCxDQUFDO0NBQ0YsQ0FBQyxDQUFDO0FBR0ksTUFBTSxFQUNYLG1CQUFtQixFQUNuQixzQkFBc0IsRUFDdEIsa0JBQWtCLEVBQ2xCLGlCQUFpQixFQUNqQixvQkFBb0IsRUFDcEIsb0JBQW9CLEVBQ3BCLFVBQVUsRUFDVixVQUFVLEVBQ1Ysb0JBQW9CLEVBQ3BCLHdCQUF3QixFQUN4QixpQkFBaUIsRUFDakIsaUJBQWlCLEVBQ2xCLEdBQUcsVUFBVSxDQUFDLE9BQU8sQ0FBQztBQUd2QixpRUFBZSxVQUFVLENBQUMsT0FBTyxFQUFDO0FBRzNCLE1BQU0sa0JBQWtCLEdBQUcsQ0FBQyxLQUE0QixFQUFFLEVBQUUsQ0FBQyxLQUFLLENBQUMsS0FBSyxDQUFDLFlBQVksQ0FBQztBQUN0RixNQUFNLGtCQUFrQixHQUFHLENBQUMsS0FBNEIsRUFBRSxFQUFFLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxZQUFZLENBQUM7QUFDdEYsTUFBTSxrQkFBa0IsR0FBRyxDQUFDLEtBQTRCLEVBQUUsRUFBRSxDQUFDLEtBQUssQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDO0FBQ2pGLE1BQU0sZ0JBQWdCLEdBQUcsQ0FBQyxLQUE0QixFQUFFLEVBQUUsQ0FBQyxLQUFLLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQztBQUM3RSxNQUFNLGtCQUFrQixHQUFHLENBQUMsS0FBNEIsRUFBRSxFQUFFLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUM7QUFDakYsTUFBTSxrQkFBa0IsR0FBRyxDQUFDLEtBQTRCLEVBQUUsRUFBRSxDQUFDLEtBQUssQ0FBQyxLQUFLLENBQUMsWUFBWSxDQUFDO0FBQ3RGLE1BQU0sc0JBQXNCLEdBQUcsQ0FBQyxLQUE0QixFQUFFLEVBQUUsQ0FBQyxLQUFLLENBQUMsS0FBSyxDQUFDLGdCQUFnQixDQUFDO0FBQzlGLE1BQU0scUJBQXFCLEdBQUcsQ0FBQyxHQUFXLEVBQUUsRUFBRSxDQUFDLENBQUMsS0FBNEIsRUFBRSxFQUFFLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxnQkFBZ0IsQ0FBQyxHQUFHLENBQUMsQ0FBQztBQUduSCxNQUFNLHFCQUFxQixHQUFHLENBQUMsS0FBNEIsRUFBRSxFQUFFO0lBQ3BFLE1BQU0sRUFBRSxZQUFZLEVBQUUsT0FBTyxFQUFFLEdBQUcsS0FBSyxDQUFDLEtBQUssQ0FBQztJQUU5QyxPQUFPLFlBQVksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLEVBQUU7UUFFakMsSUFBSSxPQUFPLENBQUMsWUFBWSxJQUFJLEtBQUssQ0FBQyxZQUFZLEtBQUssT0FBTyxDQUFDLFlBQVksRUFBRTtZQUN2RSxPQUFPLEtBQUssQ0FBQztTQUNkO1FBR0QsSUFBSSxPQUFPLENBQUMsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxFQUFFO1lBQ3BELE9BQU8sS0FBSyxDQUFDO1NBQ2Q7UUFFRCxPQUFPLElBQUksQ0FBQztJQUNkLENBQUMsQ0FBQyxDQUFDO0FBQ0wsQ0FBQyxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGFnZXBsdWctZGF0YXNvdXJjZS1mcm9udGVuZC8uL3NyYy9zdG9yZS9zbGljZXMvcXVlcnlTbGljZS50cz9mMjgyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZVNsaWNlLCBjcmVhdGVBc3luY1RodW5rLCBQYXlsb2FkQWN0aW9uIH0gZnJvbSAnQHJlZHV4anMvdG9vbGtpdCc7XG5pbXBvcnQgeyBtZXNzYWdlIH0gZnJvbSAnYW50ZCc7XG5cbmltcG9ydCB7IFF1ZXJ5LCBDcmVhdGVRdWVyeVJlcXVlc3QsIFF1ZXJ5RXhlY3V0aW9uUmVzdWx0LCBFeGVjdXRlUXVlcnlSZXF1ZXN0IH0gZnJvbSAnLi4vLi4vdHlwZXMvYXBpJztcbmltcG9ydCB7IGFwaUNsaWVudCB9IGZyb20gJy4uLy4uL3NlcnZpY2VzL2FwaUNsaWVudCc7XG5cbi8vIOW8guatpXRodW5rIGFjdGlvbnNcbmV4cG9ydCBjb25zdCBmZXRjaFF1ZXJpZXMgPSBjcmVhdGVBc3luY1RodW5rKFxuICAncXVlcnkvZmV0Y2hRdWVyaWVzJyxcbiAgYXN5bmMgKHBhcmFtczoge1xuICAgIGRhdGFzb3VyY2VJZD86IHN0cmluZztcbiAgICB0YWc/OiBzdHJpbmc7XG4gIH0gPSB7fSkgPT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LmdldCgnL3F1ZXJpZXMnLCB7IHBhcmFtcyB9KTtcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgfVxuKTtcblxuZXhwb3J0IGNvbnN0IGNyZWF0ZVF1ZXJ5ID0gY3JlYXRlQXN5bmNUaHVuayhcbiAgJ3F1ZXJ5L2NyZWF0ZVF1ZXJ5JyxcbiAgYXN5bmMgKHJlcXVlc3Q6IENyZWF0ZVF1ZXJ5UmVxdWVzdCkgPT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LnBvc3QoJy9xdWVyaWVzJywgcmVxdWVzdCk7XG4gICAgbWVzc2FnZS5zdWNjZXNzKCfmn6Xor6Lkv53lrZjmiJDlip8nKTtcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgfVxuKTtcblxuZXhwb3J0IGNvbnN0IGV4ZWN1dGVRdWVyeSA9IGNyZWF0ZUFzeW5jVGh1bmsoXG4gICdxdWVyeS9leGVjdXRlUXVlcnknLFxuICBhc3luYyAoeyBkYXRhc291cmNlSWQsIHJlcXVlc3QgfTogeyBkYXRhc291cmNlSWQ6IHN0cmluZzsgcmVxdWVzdDogRXhlY3V0ZVF1ZXJ5UmVxdWVzdCB9KSA9PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQucG9zdChgL2RhdGFzb3VyY2VzLyR7ZGF0YXNvdXJjZUlkfS9leGVjdXRlYCwgcmVxdWVzdCk7XG4gICAgcmV0dXJuIHsgZGF0YXNvdXJjZUlkLCByZXF1ZXN0LCByZXN1bHQ6IHJlc3BvbnNlLmRhdGEgfTtcbiAgfVxuKTtcblxuZXhwb3J0IGNvbnN0IGV4ZWN1dGVTYXZlZFF1ZXJ5ID0gY3JlYXRlQXN5bmNUaHVuayhcbiAgJ3F1ZXJ5L2V4ZWN1dGVTYXZlZFF1ZXJ5JyxcbiAgYXN5bmMgKHsgcXVlcnlJZCwgcGFyYW1ldGVycyB9OiB7IHF1ZXJ5SWQ6IHN0cmluZzsgcGFyYW1ldGVycz86IEFycmF5PHsga2V5OiBzdHJpbmc7IHZhbHVlOiBhbnkgfT4gfSkgPT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LnBvc3QoYC9xdWVyaWVzLyR7cXVlcnlJZH0vZXhlY3V0ZWAsIHsgcGFyYW1ldGVycyB9KTtcbiAgICByZXR1cm4geyBxdWVyeUlkLCByZXN1bHQ6IHJlc3BvbnNlLmRhdGEgfTtcbiAgfVxuKTtcblxuLy8g54q25oCB5o6l5Y+jXG5pbnRlcmZhY2UgUXVlcnlTdGF0ZSB7XG4gIC8vIOS/neWtmOeahOafpeivouWIl+ihqFxuICBzYXZlZFF1ZXJpZXM6IFF1ZXJ5W107XG4gIFxuICAvLyDlvZPliY3mn6Xor6JcbiAgY3VycmVudFF1ZXJ5OiB7XG4gICAgdGV4dDogc3RyaW5nO1xuICAgIGRhdGFzb3VyY2VJZDogc3RyaW5nO1xuICAgIHBhcmFtZXRlcnM6IEFycmF5PHsga2V5OiBzdHJpbmc7IHZhbHVlOiBhbnk7IHR5cGU/OiBzdHJpbmcgfT47XG4gIH07XG4gIFxuICAvLyDmiafooYznu5PmnpxcbiAgZXhlY3V0aW9uUmVzdWx0czogUmVjb3JkPHN0cmluZywgUXVlcnlFeGVjdXRpb25SZXN1bHQ+O1xuICBcbiAgLy8g5p+l6K+i5Y6G5Y+yXG4gIHF1ZXJ5SGlzdG9yeTogQXJyYXk8e1xuICAgIGlkOiBzdHJpbmc7XG4gICAgcXVlcnk6IHN0cmluZztcbiAgICBkYXRhc291cmNlSWQ6IHN0cmluZztcbiAgICBleGVjdXRlZEF0OiBzdHJpbmc7XG4gICAgc3VjY2VzczogYm9vbGVhbjtcbiAgICBleGVjdXRpb25UaW1lOiBudW1iZXI7XG4gIH0+O1xuICBcbiAgLy8g5Yqg6L2954q25oCBXG4gIGxvYWRpbmc6IHtcbiAgICBsaXN0OiBib29sZWFuO1xuICAgIGNyZWF0ZTogYm9vbGVhbjtcbiAgICBleGVjdXRlOiBib29sZWFuO1xuICB9O1xuICBcbiAgLy8g6ZSZ6K+v5L+h5oGvXG4gIGVycm9yOiBzdHJpbmcgfCBudWxsO1xuICBcbiAgLy8g6L+H5ruk5p2h5Lu2XG4gIGZpbHRlcnM6IHtcbiAgICBkYXRhc291cmNlSWQ/OiBzdHJpbmc7XG4gICAgdGFnPzogc3RyaW5nO1xuICB9O1xufVxuXG4vLyDliJ3lp4vnirbmgIFcbmNvbnN0IGluaXRpYWxTdGF0ZTogUXVlcnlTdGF0ZSA9IHtcbiAgc2F2ZWRRdWVyaWVzOiBbXSxcbiAgY3VycmVudFF1ZXJ5OiB7XG4gICAgdGV4dDogJycsXG4gICAgZGF0YXNvdXJjZUlkOiAnJyxcbiAgICBwYXJhbWV0ZXJzOiBbXVxuICB9LFxuICBleGVjdXRpb25SZXN1bHRzOiB7fSxcbiAgcXVlcnlIaXN0b3J5OiBbXSxcbiAgbG9hZGluZzoge1xuICAgIGxpc3Q6IGZhbHNlLFxuICAgIGNyZWF0ZTogZmFsc2UsXG4gICAgZXhlY3V0ZTogZmFsc2VcbiAgfSxcbiAgZXJyb3I6IG51bGwsXG4gIGZpbHRlcnM6IHt9XG59O1xuXG4vLyDliJvlu7pzbGljZVxuY29uc3QgcXVlcnlTbGljZSA9IGNyZWF0ZVNsaWNlKHtcbiAgbmFtZTogJ3F1ZXJ5JyxcbiAgaW5pdGlhbFN0YXRlLFxuICByZWR1Y2Vyczoge1xuICAgIC8vIOiuvue9ruW9k+WJjeafpeivouaWh+acrFxuICAgIHNldEN1cnJlbnRRdWVyeVRleHQ6IChzdGF0ZSwgYWN0aW9uOiBQYXlsb2FkQWN0aW9uPHN0cmluZz4pID0+IHtcbiAgICAgIHN0YXRlLmN1cnJlbnRRdWVyeS50ZXh0ID0gYWN0aW9uLnBheWxvYWQ7XG4gICAgfSxcbiAgICBcbiAgICAvLyDorr7nva7lvZPliY3mlbDmja7mupBcbiAgICBzZXRDdXJyZW50RGF0YXNvdXJjZUlkOiAoc3RhdGUsIGFjdGlvbjogUGF5bG9hZEFjdGlvbjxzdHJpbmc+KSA9PiB7XG4gICAgICBzdGF0ZS5jdXJyZW50UXVlcnkuZGF0YXNvdXJjZUlkID0gYWN0aW9uLnBheWxvYWQ7XG4gICAgfSxcbiAgICBcbiAgICAvLyDorr7nva7mn6Xor6Llj4LmlbBcbiAgICBzZXRRdWVyeVBhcmFtZXRlcnM6IChzdGF0ZSwgYWN0aW9uOiBQYXlsb2FkQWN0aW9uPEFycmF5PHsga2V5OiBzdHJpbmc7IHZhbHVlOiBhbnk7IHR5cGU/OiBzdHJpbmcgfT4+KSA9PiB7XG4gICAgICBzdGF0ZS5jdXJyZW50UXVlcnkucGFyYW1ldGVycyA9IGFjdGlvbi5wYXlsb2FkO1xuICAgIH0sXG4gICAgXG4gICAgLy8g5re75Yqg5p+l6K+i5Y+C5pWwXG4gICAgYWRkUXVlcnlQYXJhbWV0ZXI6IChzdGF0ZSwgYWN0aW9uOiBQYXlsb2FkQWN0aW9uPHsga2V5OiBzdHJpbmc7IHZhbHVlOiBhbnk7IHR5cGU/OiBzdHJpbmcgfT4pID0+IHtcbiAgICAgIHN0YXRlLmN1cnJlbnRRdWVyeS5wYXJhbWV0ZXJzLnB1c2goYWN0aW9uLnBheWxvYWQpO1xuICAgIH0sXG4gICAgXG4gICAgLy8g56e76Zmk5p+l6K+i5Y+C5pWwXG4gICAgcmVtb3ZlUXVlcnlQYXJhbWV0ZXI6IChzdGF0ZSwgYWN0aW9uOiBQYXlsb2FkQWN0aW9uPG51bWJlcj4pID0+IHtcbiAgICAgIHN0YXRlLmN1cnJlbnRRdWVyeS5wYXJhbWV0ZXJzLnNwbGljZShhY3Rpb24ucGF5bG9hZCwgMSk7XG4gICAgfSxcbiAgICBcbiAgICAvLyDmm7TmlrDmn6Xor6Llj4LmlbBcbiAgICB1cGRhdGVRdWVyeVBhcmFtZXRlcjogKHN0YXRlLCBhY3Rpb246IFBheWxvYWRBY3Rpb248eyBpbmRleDogbnVtYmVyOyBwYXJhbWV0ZXI6IHsga2V5OiBzdHJpbmc7IHZhbHVlOiBhbnk7IHR5cGU/OiBzdHJpbmcgfSB9PikgPT4ge1xuICAgICAgY29uc3QgeyBpbmRleCwgcGFyYW1ldGVyIH0gPSBhY3Rpb24ucGF5bG9hZDtcbiAgICAgIGlmIChzdGF0ZS5jdXJyZW50UXVlcnkucGFyYW1ldGVyc1tpbmRleF0pIHtcbiAgICAgICAgc3RhdGUuY3VycmVudFF1ZXJ5LnBhcmFtZXRlcnNbaW5kZXhdID0gcGFyYW1ldGVyO1xuICAgICAgfVxuICAgIH0sXG4gICAgXG4gICAgLy8g6K6+572u6L+H5ruk5p2h5Lu2XG4gICAgc2V0RmlsdGVyczogKHN0YXRlLCBhY3Rpb246IFBheWxvYWRBY3Rpb248UGFydGlhbDxRdWVyeVN0YXRlWydmaWx0ZXJzJ10+PikgPT4ge1xuICAgICAgc3RhdGUuZmlsdGVycyA9IHsgLi4uc3RhdGUuZmlsdGVycywgLi4uYWN0aW9uLnBheWxvYWQgfTtcbiAgICB9LFxuICAgIFxuICAgIC8vIOa4hemZpOmUmeivr1xuICAgIGNsZWFyRXJyb3I6IChzdGF0ZSkgPT4ge1xuICAgICAgc3RhdGUuZXJyb3IgPSBudWxsO1xuICAgIH0sXG4gICAgXG4gICAgLy8g5riF6Zmk5omn6KGM57uT5p6cXG4gICAgY2xlYXJFeGVjdXRpb25SZXN1bHQ6IChzdGF0ZSwgYWN0aW9uOiBQYXlsb2FkQWN0aW9uPHN0cmluZz4pID0+IHtcbiAgICAgIGRlbGV0ZSBzdGF0ZS5leGVjdXRpb25SZXN1bHRzW2FjdGlvbi5wYXlsb2FkXTtcbiAgICB9LFxuICAgIFxuICAgIC8vIOa4hemZpOaJgOacieaJp+ihjOe7k+aenFxuICAgIGNsZWFyQWxsRXhlY3V0aW9uUmVzdWx0czogKHN0YXRlKSA9PiB7XG4gICAgICBzdGF0ZS5leGVjdXRpb25SZXN1bHRzID0ge307XG4gICAgfSxcbiAgICBcbiAgICAvLyDliqDovb3mn6Xor6LliLDnvJbovpHlmahcbiAgICBsb2FkUXVlcnlUb0VkaXRvcjogKHN0YXRlLCBhY3Rpb246IFBheWxvYWRBY3Rpb248UXVlcnk+KSA9PiB7XG4gICAgICBjb25zdCBxdWVyeSA9IGFjdGlvbi5wYXlsb2FkO1xuICAgICAgc3RhdGUuY3VycmVudFF1ZXJ5ID0ge1xuICAgICAgICB0ZXh0OiBxdWVyeS5xdWVyeSxcbiAgICAgICAgZGF0YXNvdXJjZUlkOiBxdWVyeS5kYXRhc291cmNlSWQsXG4gICAgICAgIHBhcmFtZXRlcnM6IHF1ZXJ5LnBhcmFtZXRlcnNcbiAgICAgIH07XG4gICAgfSxcbiAgICBcbiAgICAvLyDmuIXpmaTmn6Xor6Lljoblj7JcbiAgICBjbGVhclF1ZXJ5SGlzdG9yeTogKHN0YXRlKSA9PiB7XG4gICAgICBzdGF0ZS5xdWVyeUhpc3RvcnkgPSBbXTtcbiAgICB9XG4gIH0sXG4gIGV4dHJhUmVkdWNlcnM6IChidWlsZGVyKSA9PiB7XG4gICAgLy8g6I635Y+W5p+l6K+i5YiX6KGoXG4gICAgYnVpbGRlclxuICAgICAgLmFkZENhc2UoZmV0Y2hRdWVyaWVzLnBlbmRpbmcsIChzdGF0ZSkgPT4ge1xuICAgICAgICBzdGF0ZS5sb2FkaW5nLmxpc3QgPSB0cnVlO1xuICAgICAgICBzdGF0ZS5lcnJvciA9IG51bGw7XG4gICAgICB9KVxuICAgICAgLmFkZENhc2UoZmV0Y2hRdWVyaWVzLmZ1bGZpbGxlZCwgKHN0YXRlLCBhY3Rpb246IFBheWxvYWRBY3Rpb248UXVlcnlbXT4pID0+IHtcbiAgICAgICAgc3RhdGUubG9hZGluZy5saXN0ID0gZmFsc2U7XG4gICAgICAgIHN0YXRlLnNhdmVkUXVlcmllcyA9IGFjdGlvbi5wYXlsb2FkO1xuICAgICAgfSlcbiAgICAgIC5hZGRDYXNlKGZldGNoUXVlcmllcy5yZWplY3RlZCwgKHN0YXRlLCBhY3Rpb24pID0+IHtcbiAgICAgICAgc3RhdGUubG9hZGluZy5saXN0ID0gZmFsc2U7XG4gICAgICAgIHN0YXRlLmVycm9yID0gYWN0aW9uLmVycm9yLm1lc3NhZ2UgfHwgJ+iOt+WPluafpeivouWIl+ihqOWksei0pSc7XG4gICAgICB9KTtcblxuICAgIC8vIOWIm+W7uuafpeivolxuICAgIGJ1aWxkZXJcbiAgICAgIC5hZGRDYXNlKGNyZWF0ZVF1ZXJ5LnBlbmRpbmcsIChzdGF0ZSkgPT4ge1xuICAgICAgICBzdGF0ZS5sb2FkaW5nLmNyZWF0ZSA9IHRydWU7XG4gICAgICAgIHN0YXRlLmVycm9yID0gbnVsbDtcbiAgICAgIH0pXG4gICAgICAuYWRkQ2FzZShjcmVhdGVRdWVyeS5mdWxmaWxsZWQsIChzdGF0ZSwgYWN0aW9uOiBQYXlsb2FkQWN0aW9uPFF1ZXJ5PikgPT4ge1xuICAgICAgICBzdGF0ZS5sb2FkaW5nLmNyZWF0ZSA9IGZhbHNlO1xuICAgICAgICBzdGF0ZS5zYXZlZFF1ZXJpZXMudW5zaGlmdChhY3Rpb24ucGF5bG9hZCk7XG4gICAgICB9KVxuICAgICAgLmFkZENhc2UoY3JlYXRlUXVlcnkucmVqZWN0ZWQsIChzdGF0ZSwgYWN0aW9uKSA9PiB7XG4gICAgICAgIHN0YXRlLmxvYWRpbmcuY3JlYXRlID0gZmFsc2U7XG4gICAgICAgIHN0YXRlLmVycm9yID0gYWN0aW9uLmVycm9yLm1lc3NhZ2UgfHwgJ+S/neWtmOafpeivouWksei0pSc7XG4gICAgICB9KTtcblxuICAgIC8vIOaJp+ihjOafpeivolxuICAgIGJ1aWxkZXJcbiAgICAgIC5hZGRDYXNlKGV4ZWN1dGVRdWVyeS5wZW5kaW5nLCAoc3RhdGUpID0+IHtcbiAgICAgICAgc3RhdGUubG9hZGluZy5leGVjdXRlID0gdHJ1ZTtcbiAgICAgICAgc3RhdGUuZXJyb3IgPSBudWxsO1xuICAgICAgfSlcbiAgICAgIC5hZGRDYXNlKGV4ZWN1dGVRdWVyeS5mdWxmaWxsZWQsIChzdGF0ZSwgYWN0aW9uKSA9PiB7XG4gICAgICAgIHN0YXRlLmxvYWRpbmcuZXhlY3V0ZSA9IGZhbHNlO1xuICAgICAgICBjb25zdCB7IGRhdGFzb3VyY2VJZCwgcmVxdWVzdCwgcmVzdWx0IH0gPSBhY3Rpb24ucGF5bG9hZDtcbiAgICAgICAgXG4gICAgICAgIC8vIOS/neWtmOaJp+ihjOe7k+aenFxuICAgICAgICBjb25zdCByZXN1bHRLZXkgPSBgJHtkYXRhc291cmNlSWR9XyR7RGF0ZS5ub3coKX1gO1xuICAgICAgICBzdGF0ZS5leGVjdXRpb25SZXN1bHRzW3Jlc3VsdEtleV0gPSByZXN1bHQ7XG4gICAgICAgIFxuICAgICAgICAvLyDmt7vliqDliLDmn6Xor6Lljoblj7JcbiAgICAgICAgc3RhdGUucXVlcnlIaXN0b3J5LnVuc2hpZnQoe1xuICAgICAgICAgIGlkOiByZXN1bHRLZXksXG4gICAgICAgICAgcXVlcnk6IHJlcXVlc3QucXVlcnksXG4gICAgICAgICAgZGF0YXNvdXJjZUlkLFxuICAgICAgICAgIGV4ZWN1dGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICBzdWNjZXNzOiByZXN1bHQuaXNFeGVjdXRpb25TdWNjZXNzLFxuICAgICAgICAgIGV4ZWN1dGlvblRpbWU6IHJlc3VsdC5leGVjdXRpb25UaW1lXG4gICAgICAgIH0pO1xuICAgICAgICBcbiAgICAgICAgLy8g6ZmQ5Yi25Y6G5Y+y6K6w5b2V5pWw6YePXG4gICAgICAgIGlmIChzdGF0ZS5xdWVyeUhpc3RvcnkubGVuZ3RoID4gNTApIHtcbiAgICAgICAgICBzdGF0ZS5xdWVyeUhpc3RvcnkgPSBzdGF0ZS5xdWVyeUhpc3Rvcnkuc2xpY2UoMCwgNTApO1xuICAgICAgICB9XG4gICAgICB9KVxuICAgICAgLmFkZENhc2UoZXhlY3V0ZVF1ZXJ5LnJlamVjdGVkLCAoc3RhdGUsIGFjdGlvbikgPT4ge1xuICAgICAgICBzdGF0ZS5sb2FkaW5nLmV4ZWN1dGUgPSBmYWxzZTtcbiAgICAgICAgc3RhdGUuZXJyb3IgPSBhY3Rpb24uZXJyb3IubWVzc2FnZSB8fCAn5p+l6K+i5omn6KGM5aSx6LSlJztcbiAgICAgIH0pO1xuXG4gICAgLy8g5omn6KGM5L+d5a2Y55qE5p+l6K+iXG4gICAgYnVpbGRlclxuICAgICAgLmFkZENhc2UoZXhlY3V0ZVNhdmVkUXVlcnkucGVuZGluZywgKHN0YXRlKSA9PiB7XG4gICAgICAgIHN0YXRlLmxvYWRpbmcuZXhlY3V0ZSA9IHRydWU7XG4gICAgICAgIHN0YXRlLmVycm9yID0gbnVsbDtcbiAgICAgIH0pXG4gICAgICAuYWRkQ2FzZShleGVjdXRlU2F2ZWRRdWVyeS5mdWxmaWxsZWQsIChzdGF0ZSwgYWN0aW9uKSA9PiB7XG4gICAgICAgIHN0YXRlLmxvYWRpbmcuZXhlY3V0ZSA9IGZhbHNlO1xuICAgICAgICBjb25zdCB7IHF1ZXJ5SWQsIHJlc3VsdCB9ID0gYWN0aW9uLnBheWxvYWQ7XG4gICAgICAgIFxuICAgICAgICAvLyDkv53lrZjmiafooYznu5PmnpxcbiAgICAgICAgc3RhdGUuZXhlY3V0aW9uUmVzdWx0c1txdWVyeUlkXSA9IHJlc3VsdDtcbiAgICAgICAgXG4gICAgICAgIC8vIOabtOaWsOafpeivoueahOacgOWQjuaJp+ihjOaXtumXtFxuICAgICAgICBjb25zdCBxdWVyeSA9IHN0YXRlLnNhdmVkUXVlcmllcy5maW5kKHEgPT4gcS5pZCA9PT0gcXVlcnlJZCk7XG4gICAgICAgIGlmIChxdWVyeSkge1xuICAgICAgICAgIHF1ZXJ5Lmxhc3RFeGVjdXRlZCA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKTtcbiAgICAgICAgICBcbiAgICAgICAgICAvLyDmt7vliqDliLDmn6Xor6Lljoblj7JcbiAgICAgICAgICBzdGF0ZS5xdWVyeUhpc3RvcnkudW5zaGlmdCh7XG4gICAgICAgICAgICBpZDogYCR7cXVlcnlJZH1fJHtEYXRlLm5vdygpfWAsXG4gICAgICAgICAgICBxdWVyeTogcXVlcnkucXVlcnksXG4gICAgICAgICAgICBkYXRhc291cmNlSWQ6IHF1ZXJ5LmRhdGFzb3VyY2VJZCxcbiAgICAgICAgICAgIGV4ZWN1dGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICAgIHN1Y2Nlc3M6IHJlc3VsdC5pc0V4ZWN1dGlvblN1Y2Nlc3MsXG4gICAgICAgICAgICBleGVjdXRpb25UaW1lOiByZXN1bHQuZXhlY3V0aW9uVGltZVxuICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICB9KVxuICAgICAgLmFkZENhc2UoZXhlY3V0ZVNhdmVkUXVlcnkucmVqZWN0ZWQsIChzdGF0ZSwgYWN0aW9uKSA9PiB7XG4gICAgICAgIHN0YXRlLmxvYWRpbmcuZXhlY3V0ZSA9IGZhbHNlO1xuICAgICAgICBzdGF0ZS5lcnJvciA9IGFjdGlvbi5lcnJvci5tZXNzYWdlIHx8ICfmiafooYzkv53lrZjnmoTmn6Xor6LlpLHotKUnO1xuICAgICAgfSk7XG4gIH1cbn0pO1xuXG4vLyDlr7zlh7phY3Rpb25zXG5leHBvcnQgY29uc3Qge1xuICBzZXRDdXJyZW50UXVlcnlUZXh0LFxuICBzZXRDdXJyZW50RGF0YXNvdXJjZUlkLFxuICBzZXRRdWVyeVBhcmFtZXRlcnMsXG4gIGFkZFF1ZXJ5UGFyYW1ldGVyLFxuICByZW1vdmVRdWVyeVBhcmFtZXRlcixcbiAgdXBkYXRlUXVlcnlQYXJhbWV0ZXIsXG4gIHNldEZpbHRlcnMsXG4gIGNsZWFyRXJyb3IsXG4gIGNsZWFyRXhlY3V0aW9uUmVzdWx0LFxuICBjbGVhckFsbEV4ZWN1dGlvblJlc3VsdHMsXG4gIGxvYWRRdWVyeVRvRWRpdG9yLFxuICBjbGVhclF1ZXJ5SGlzdG9yeVxufSA9IHF1ZXJ5U2xpY2UuYWN0aW9ucztcblxuLy8g5a+85Ye6cmVkdWNlclxuZXhwb3J0IGRlZmF1bHQgcXVlcnlTbGljZS5yZWR1Y2VyO1xuXG4vLyDpgInmi6nlmahcbmV4cG9ydCBjb25zdCBzZWxlY3RTYXZlZFF1ZXJpZXMgPSAoc3RhdGU6IHsgcXVlcnk6IFF1ZXJ5U3RhdGUgfSkgPT4gc3RhdGUucXVlcnkuc2F2ZWRRdWVyaWVzO1xuZXhwb3J0IGNvbnN0IHNlbGVjdEN1cnJlbnRRdWVyeSA9IChzdGF0ZTogeyBxdWVyeTogUXVlcnlTdGF0ZSB9KSA9PiBzdGF0ZS5xdWVyeS5jdXJyZW50UXVlcnk7XG5leHBvcnQgY29uc3Qgc2VsZWN0UXVlcnlMb2FkaW5nID0gKHN0YXRlOiB7IHF1ZXJ5OiBRdWVyeVN0YXRlIH0pID0+IHN0YXRlLnF1ZXJ5LmxvYWRpbmc7XG5leHBvcnQgY29uc3Qgc2VsZWN0UXVlcnlFcnJvciA9IChzdGF0ZTogeyBxdWVyeTogUXVlcnlTdGF0ZSB9KSA9PiBzdGF0ZS5xdWVyeS5lcnJvcjtcbmV4cG9ydCBjb25zdCBzZWxlY3RRdWVyeUZpbHRlcnMgPSAoc3RhdGU6IHsgcXVlcnk6IFF1ZXJ5U3RhdGUgfSkgPT4gc3RhdGUucXVlcnkuZmlsdGVycztcbmV4cG9ydCBjb25zdCBzZWxlY3RRdWVyeUhpc3RvcnkgPSAoc3RhdGU6IHsgcXVlcnk6IFF1ZXJ5U3RhdGUgfSkgPT4gc3RhdGUucXVlcnkucXVlcnlIaXN0b3J5O1xuZXhwb3J0IGNvbnN0IHNlbGVjdEV4ZWN1dGlvblJlc3VsdHMgPSAoc3RhdGU6IHsgcXVlcnk6IFF1ZXJ5U3RhdGUgfSkgPT4gc3RhdGUucXVlcnkuZXhlY3V0aW9uUmVzdWx0cztcbmV4cG9ydCBjb25zdCBzZWxlY3RFeGVjdXRpb25SZXN1bHQgPSAoa2V5OiBzdHJpbmcpID0+IChzdGF0ZTogeyBxdWVyeTogUXVlcnlTdGF0ZSB9KSA9PiBzdGF0ZS5xdWVyeS5leGVjdXRpb25SZXN1bHRzW2tleV07XG5cbi8vIOi/h+a7pOWQjueahOafpeivouWIl+ihqOmAieaLqeWZqFxuZXhwb3J0IGNvbnN0IHNlbGVjdEZpbHRlcmVkUXVlcmllcyA9IChzdGF0ZTogeyBxdWVyeTogUXVlcnlTdGF0ZSB9KSA9PiB7XG4gIGNvbnN0IHsgc2F2ZWRRdWVyaWVzLCBmaWx0ZXJzIH0gPSBzdGF0ZS5xdWVyeTtcbiAgXG4gIHJldHVybiBzYXZlZFF1ZXJpZXMuZmlsdGVyKHF1ZXJ5ID0+IHtcbiAgICAvLyDmlbDmja7mupDov4fmu6RcbiAgICBpZiAoZmlsdGVycy5kYXRhc291cmNlSWQgJiYgcXVlcnkuZGF0YXNvdXJjZUlkICE9PSBmaWx0ZXJzLmRhdGFzb3VyY2VJZCkge1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBcbiAgICAvLyDmoIfnrb7ov4fmu6RcbiAgICBpZiAoZmlsdGVycy50YWcgJiYgIXF1ZXJ5LnRhZ3MuaW5jbHVkZXMoZmlsdGVycy50YWcpKSB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIFxuICAgIHJldHVybiB0cnVlO1xuICB9KTtcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///7166\n\n}")}}]);