// 性能监控和优化工具

/**
 * 性能指标收集器
 */
export class PerformanceCollector {
  private static instance: PerformanceCollector;
  private metrics: Map<string, number> = new Map();
  private observers: PerformanceObserver[] = [];

  static getInstance(): PerformanceCollector {
    if (!PerformanceCollector.instance) {
      PerformanceCollector.instance = new PerformanceCollector();
    }
    return PerformanceCollector.instance;
  }

  constructor() {
    this.initObservers();
  }

  private initObservers() {
    // 监控导航性能
    if ('PerformanceObserver' in window) {
      try {
        const navigationObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            if (entry.entryType === 'navigation') {
              const navEntry = entry as PerformanceNavigationTiming;
              this.recordMetric('navigation.domContentLoaded', navEntry.domContentLoadedEventEnd - navEntry.domContentLoadedEventStart);
              this.recordMetric('navigation.loadComplete', navEntry.loadEventEnd - navEntry.loadEventStart);
              this.recordMetric('navigation.firstPaint', navEntry.responseEnd - navEntry.requestStart);
            }
          });
        });
        navigationObserver.observe({ entryTypes: ['navigation'] });
        this.observers.push(navigationObserver);
      } catch (error) {
        console.warn('Navigation observer not supported:', error);
      }

      // 监控资源加载性能
      try {
        const resourceObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            if (entry.entryType === 'resource') {
              const resourceEntry = entry as PerformanceResourceTiming;
              const duration = resourceEntry.responseEnd - resourceEntry.requestStart;
              
              if (resourceEntry.name.includes('.js')) {
                this.recordMetric('resource.js.loadTime', duration);
              } else if (resourceEntry.name.includes('.css')) {
                this.recordMetric('resource.css.loadTime', duration);
              }
            }
          });
        });
        resourceObserver.observe({ entryTypes: ['resource'] });
        this.observers.push(resourceObserver);
      } catch (error) {
        console.warn('Resource observer not supported:', error);
      }

      // 监控用户交互性能
      try {
        const measureObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            if (entry.entryType === 'measure') {
              this.recordMetric(`custom.${entry.name}`, entry.duration);
            }
          });
        });
        measureObserver.observe({ entryTypes: ['measure'] });
        this.observers.push(measureObserver);
      } catch (error) {
        console.warn('Measure observer not supported:', error);
      }
    }
  }

  recordMetric(name: string, value: number) {
    this.metrics.set(name, value);
    
    // 在开发环境下输出性能指标
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Performance] ${name}: ${value.toFixed(2)}ms`);
    }
  }

  getMetric(name: string): number | undefined {
    return this.metrics.get(name);
  }

  getAllMetrics(): Record<string, number> {
    return Object.fromEntries(this.metrics);
  }

  // 测量函数执行时间
  measureFunction<T>(name: string, fn: () => T): T {
    const start = performance.now();
    const result = fn();
    const end = performance.now();
    this.recordMetric(`function.${name}`, end - start);
    return result;
  }

  // 测量异步函数执行时间
  async measureAsyncFunction<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const start = performance.now();
    const result = await fn();
    const end = performance.now();
    this.recordMetric(`async.${name}`, end - start);
    return result;
  }

  // 清理观察者
  cleanup() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.metrics.clear();
  }
}

/**
 * 组件性能监控装饰器
 */
export function withPerformanceMonitoring<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  componentName: string
) {
  return function PerformanceMonitoredComponent(props: P) {
    const collector = PerformanceCollector.getInstance();
    
    React.useEffect(() => {
      const mountStart = performance.now();
      
      return () => {
        const mountEnd = performance.now();
        collector.recordMetric(`component.${componentName}.mountTime`, mountEnd - mountStart);
      };
    }, []);

    return React.createElement(WrappedComponent, props);
  };
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate = false
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    
    const callNow = immediate && !timeout;
    
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func(...args);
  };
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * 内存使用监控
 */
export class MemoryMonitor {
  private static instance: MemoryMonitor;
  private intervalId: NodeJS.Timeout | null = null;

  static getInstance(): MemoryMonitor {
    if (!MemoryMonitor.instance) {
      MemoryMonitor.instance = new MemoryMonitor();
    }
    return MemoryMonitor.instance;
  }

  startMonitoring(interval = 30000) { // 默认30秒检查一次
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }

    this.intervalId = setInterval(() => {
      this.checkMemoryUsage();
    }, interval);
  }

  stopMonitoring() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  private checkMemoryUsage() {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      const collector = PerformanceCollector.getInstance();
      
      collector.recordMetric('memory.usedJSHeapSize', memory.usedJSHeapSize / 1024 / 1024); // MB
      collector.recordMetric('memory.totalJSHeapSize', memory.totalJSHeapSize / 1024 / 1024); // MB
      collector.recordMetric('memory.jsHeapSizeLimit', memory.jsHeapSizeLimit / 1024 / 1024); // MB
      
      // 内存使用率超过80%时发出警告
      const usageRatio = memory.usedJSHeapSize / memory.jsHeapSizeLimit;
      if (usageRatio > 0.8) {
        console.warn(`[Memory Warning] Memory usage is ${(usageRatio * 100).toFixed(1)}%`);
      }
    }
  }
}

/**
 * 代码分割和懒加载工具
 */
export const lazyWithRetry = (importFunc: () => Promise<any>, retries = 3) => {
  return React.lazy(async () => {
    let attempt = 0;
    
    while (attempt < retries) {
      try {
        return await importFunc();
      } catch (error) {
        attempt++;
        console.warn(`[Lazy Load] Attempt ${attempt} failed:`, error);
        
        if (attempt >= retries) {
          throw error;
        }
        
        // 等待一段时间后重试
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
      }
    }
    
    throw new Error('Max retries reached');
  });
};

/**
 * 初始化性能监控
 */
export function initPerformanceMonitoring() {
  const collector = PerformanceCollector.getInstance();
  const memoryMonitor = MemoryMonitor.getInstance();
  
  // 开始内存监控
  memoryMonitor.startMonitoring();
  
  // 监控页面可见性变化
  document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible') {
      collector.recordMetric('page.becameVisible', performance.now());
    } else {
      collector.recordMetric('page.becameHidden', performance.now());
    }
  });
  
  // 页面卸载时清理
  window.addEventListener('beforeunload', () => {
    collector.cleanup();
    memoryMonitor.stopMonitoring();
  });
  
  console.log('[Performance] Monitoring initialized');
}

// 导出单例实例
export const performanceCollector = PerformanceCollector.getInstance();
export const memoryMonitor = MemoryMonitor.getInstance();
