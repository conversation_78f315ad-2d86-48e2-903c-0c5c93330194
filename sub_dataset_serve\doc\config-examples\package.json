{"name": "pageplug-datasource-app", "version": "1.0.0", "description": "PagePlug数据源管理微前端应用", "main": "src/index.tsx", "scripts": {"dev": "webpack serve --mode development", "build": "webpack --mode production", "build:analyze": "webpack --mode production --analyze", "preview": "serve dist -s -l 3001", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "lint:check": "eslint src --ext .js,.jsx,.ts,.tsx", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "clean": "<PERSON><PERSON><PERSON> dist", "prepare": "husky install"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "@reduxjs/toolkit": "^1.9.0", "react-redux": "^8.0.5", "@tanstack/react-query": "^4.24.0", "antd": "^5.2.0", "styled-components": "^6.0.0", "@ant-design/icons": "^5.0.0", "axios": "^1.3.0", "dayjs": "^1.11.0", "lodash-es": "^4.17.21", "react-i18next": "^12.1.0", "i18next": "^22.4.0"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/lodash-es": "^4.17.0", "@types/styled-components": "^5.1.0", "typescript": "^4.9.0", "webpack": "^5.75.0", "webpack-cli": "^5.0.0", "webpack-dev-server": "^4.11.0", "@module-federation/webpack": "^0.0.5", "html-webpack-plugin": "^5.5.0", "ts-loader": "^9.4.0", "style-loader": "^3.3.0", "css-loader": "^6.7.0", "postcss": "^8.4.0", "postcss-loader": "^7.0.0", "tailwindcss": "^3.2.0", "@tailwindcss/forms": "^0.5.0", "@tailwindcss/typography": "^0.5.0", "autoprefixer": "^10.4.0", "less": "^4.1.0", "less-loader": "^11.1.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^5.16.0", "@testing-library/user-event": "^14.4.0", "jest": "^29.3.0", "jest-environment-jsdom": "^29.3.0", "@types/jest": "^29.2.0", "eslint": "^8.32.0", "@typescript-eslint/eslint-plugin": "^5.48.0", "@typescript-eslint/parser": "^5.48.0", "eslint-plugin-react": "^7.32.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-import": "^2.27.0", "eslint-config-prettier": "^8.6.0", "prettier": "^2.8.0", "husky": "^8.0.0", "lint-staged": "^13.1.0", "rimraf": "^4.1.0", "serve": "^14.2.0"}, "peerDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "src/**/*.{json,css,md}": ["prettier --write"]}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/src/setupTests.ts"], "moduleNameMapping": {"^@/(.*)$": "<rootDir>/src/$1"}, "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts", "!src/index.tsx", "!src/setupTests.ts"], "coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/pageplug-datasource-app.git"}, "keywords": ["react", "typescript", "microfrontend", "module-federation", "styled-components", "tailwindcss", "antd", "datasource", "pageplug"], "author": "PagePlug Team", "license": "MIT"}