name: <PERSON><PERSON>mith CI Test Sanity Workflow

on:
  # This line enables manual triggering of this workflow.
  workflow_dispatch:
  workflow_call:
    inputs:
      pr:
        description: "This is the PR number in case the workflow is being called in a pull request"
        required: false
        type: number

jobs:
  ci-test:
    runs-on: ubuntu-latest
    if: |
      github.event.pull_request.head.repo.full_name == github.repository ||
      github.event_name == 'push' ||
      github.event_name == 'workflow_dispatch' ||
      github.event_name == 'repository_dispatch'
    defaults:
      run:
        shell: bash

    # Service containers to run with this job. Required for running tests
    services:
      # Label used to access the service container
      redis:
        # Docker Hub image for Redis
        image: redis
        ports:
          # Opens tcp port 6379 on the host and service container
          - 6379:6379
      mongo:
        image: mongo
        ports:
          - 27017:27017

    steps:
      - name: Set up Depot CLI
        uses: depot/setup-action@v1

      # Check out merge commit
      - name: Fork based /ok-to-test checkout
        if: inputs.pr != 0
        uses: actions/checkout@v4
        with:
          ref: "refs/pull/${{ inputs.pr }}/merge"

      # Checkout the code in the current branch in case the workflow is called because of a branch push event
      - name: Checkout the head commit of the branch
        if: inputs.pr == 0
        uses: actions/checkout@v4

      # Timestamp will be used to create cache key
      - id: timestamp
        run: echo "timestamp=$(date +'%Y-%m-%dT%H:%M:%S')" >> $GITHUB_OUTPUT

      # In case this is second attempt try restoring status of the prior attempt from cache
      - name: Restore the previous run result
        id: cache-appsmith
        uses: martijnhols/actions-cache@v3.0.2
        with:
          path: |
            ~/run_result_sanity
          key: ${{ github.run_id }}-sanity-${{ github.job }}
          restore-keys: |
            ${{ github.run_id }}-sanity-${{ github.job }}

      - name: Get the previous run result
        if: steps.cache-appsmith.outputs.cache-hit == 'true'
        id: run_result
        run: |
          run_result_env=$(cat ~/run_result_sanity)
          echo "run_result=$run_result_env" >> $GITHUB_OUTPUT

      - name: Dump steps context
        env:
          STEPS_CONTEXT: ${{ toJson(steps) }}
        run: echo "$STEPS_CONTEXT"

      # In case this is second attempt try restoring failed tests
      - name: Restore the previous failed combine result
        if: steps.run_result.outputs.run_result == 'failedtest'
        uses: martijnhols/actions-cache/restore@v3
        with:
          path: |
            ~/failed_spec_ci_sanity
          key: ${{ github.run_id }}-failed_spec_ci_sanity-${{ github.job }}
          restore-keys: |
            ${{ github.run_id }}-failed_spec_ci_sanity-${{ github.job }}

      # failed_spec_env will contain list of all failed specs
      # We are using environment variable instead of regular to support multiline
      - name: Get failed_spec
        if: steps.run_result.outputs.run_result == 'failedtest'
        run: |
          failed_spec_env=$(cat ~/failed_spec_ci_sanity)
          echo "failed_spec_env<<EOF" >> $GITHUB_ENV
          echo "$failed_spec_env" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV

      - if: steps.run_result.outputs.run_result != 'success' && steps.run_result.outputs.run_result != 'failedtest'
        run: echo "Starting full run" && exit 0

      - if: steps.run_result.outputs.run_result == 'failedtest'
        run: echo "Rerunning failed tests" && exit 0

      - name: cat run_result
        run: echo ${{ steps.run_result.outputs.run_result }}

      - name: Download Docker image artifact
        uses: actions/download-artifact@v3
        with:
          name: cicontainer

      - name: Load Docker image from tar file
        run: |
          gunzip cicontainer.tar.gz
          docker load -i cicontainer.tar

      - name: Create folder
        if: steps.run_result.outputs.run_result != 'success'
        env:
          APPSMITH_LICENSE_KEY: ${{ secrets.APPSMITH_LICENSE_KEY }}
        working-directory: "."
        run: |
          mkdir -p cicontainerlocal/stacks/configuration/

      - name: Run Appsmith & TED docker image
        if: steps.run_result.outputs.run_result != 'success'
        env:
          APPSMITH_LICENSE_KEY: ${{ secrets.APPSMITH_LICENSE_KEY }}
        working-directory: "."
        run: |
          sudo /etc/init.d/ssh stop ;
          mkdir -p ~/git-server/keys
          mkdir -p ~/git-server/repos
          docker run --name test-event-driver -d -p 22:22 -p 5001:5001 -p 3306:3306 \
          -p 5432:5432 -p 28017:27017 -p 25:25 -p 5000:5000 -p 3001:3000 -p 6001:6001 --privileged --pid=host --ipc=host --volume /:/host -v ~/git-server/keys:/git-server/keys \
          -v ~/git-server/repos:/git-server/repos  appsmith/test-event-driver:latest
          cd cicontainerlocal
          docker run -d --name appsmith -p 80:80 \
            -v "$PWD/stacks:/appsmith-stacks" -e APPSMITH_LICENSE_KEY=$APPSMITH_LICENSE_KEY \
            -e APPSMITH_DISABLE_TELEMETRY=true \
            -e APPSMITH_CLOUD_SERVICES_BASE_URL=http://host.docker.internal:5001 \
            --add-host=host.docker.internal:host-gateway --add-host=api.segment.io:host-gateway --add-host=t.appsmith.com:host-gateway \
            cicontainer

      - name: Setup MSSQL & Arango docker containers
        working-directory: app/client/cypress
        run: |
          docker run --name=mssqldb -e "ACCEPT_EULA=Y" -e "SA_PASSWORD=Root@123" -p 1433:1433 -d mcr.microsoft.com/azure-sql-edge
          docker run --name arangodb -e ARANGO_USERNAME=root -e ARANGO_ROOT_PASSWORD=Arango -p 8529:8529 -d arangodb
        # docker exec -i mssqldb /bin/bash -c "echo -e '[mysqld]\ntcpport=1433\ntcpnodelay=1' >> /var/opt/mssql/mssql.conf"
        # docker restart mssqldb
        # sudo ufw allow 1433/tcp
        # docker cp init-mssql-dump-for-test.sql mssqldb:var/init-mssql-dump-for-test.sql
        # docker exec -i mssqldb /opt/mssql-tools/bin/sqlcmd -S localhost -U SA -P Root@123 -i /var/init-mssql-dump-for-test.sql

      - name: Use Node.js
        if: steps.run_result.outputs.run_result != 'success'
        uses: actions/setup-node@v3
        with:
          node-version-file: app/client/package.json

      # Install all the dependencies
      - name: Install dependencies
        if: steps.run_result.outputs.run_result != 'success'
        run: |
          cd app/client
          yarn install \
          --cwd cypress \
          --modules-folder ../node_modules

      - name: Setting up the cypress tests
        if: steps.run_result.outputs.run_result != 'success'
        shell: bash
        run: |
          cd app/client
          chmod a+x ./cypress/setup-test-ci.sh
          ./cypress/setup-test-ci.sh

      - uses: browser-actions/setup-chrome@latest
        with:
          chrome-version: stable
      - run: |
          echo "BROWSER_PATH=$(which chrome)" >> $GITHUB_ENV

      - name: Run the cypress test
        if: steps.run_result.outputs.run_result != 'success' && steps.run_result.outputs.run_result != 'failedtest'
        uses: cypress-io/github-action@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}
          CYPRESS_PROJECT_ID: ${{ secrets.CYPRESS_PROJECT_ID }}
          CYPRESS_USERNAME: ${{ secrets.CYPRESS_USERNAME }}
          CYPRESS_PASSWORD: ${{ secrets.CYPRESS_PASSWORD }}
          CYPRESS_TESTUSERNAME1: ${{ secrets.CYPRESS_TESTUSERNAME1 }}
          CYPRESS_TESTPASSWORD1: ${{ secrets.CYPRESS_TESTPASSWORD1 }}
          CYPRESS_TESTUSERNAME2: ${{ secrets.CYPRESS_TESTUSERNAME2 }}
          CYPRESS_TESTPASSWORD2: ${{ secrets.CYPRESS_TESTPASSWORD1 }}
          CYPRESS_TESTUSERNAME3: ${{ secrets.CYPRESS_TESTUSERNAME3 }}
          CYPRESS_TESTPASSWORD3: ${{ secrets.CYPRESS_TESTPASSWORD3 }}
          CYPRESS_TESTUSERNAME4: ${{ secrets.CYPRESS_TESTUSERNAME4 }}
          CYPRESS_TESTPASSWORD4: ${{ secrets.CYPRESS_TESTPASSWORD4 }}
          CYPRESS_S3_ACCESS_KEY: ${{ secrets.CYPRESS_S3_ACCESS_KEY }}
          CYPRESS_S3_SECRET_KEY: ${{ secrets.CYPRESS_S3_SECRET_KEY }}
          CYPRESS_GITEA_TOKEN: ${{ secrets.GITEA_TOKEN }}
          CYPRESS_AIRTABLE_BEARER: ${{ secrets.AIRTABLE_BEARER }}
          CYPRESS_ORACLE_HOST: ${{ secrets.ORACLE_HOST }}
          CYPRESS_ORACLE_SERVICE: ${{ secrets.ORACLE_SERVICE }}
          CYPRESS_ORACLE_USERNAME: ${{ secrets.ORACLE_USERNAME }}
          CYPRESS_ORACLE_PASSWORD: ${{ secrets.ORACLE_PASSWORD }}
          CYPRESS_GITHUB_PERSONAL_ACCESS_TOKEN: ${{ secrets.CYPRESS_GITHUB_PERSONAL_ACCESS_TOKEN }}
          CYPRESS_TEST_GITHUB_USER_NAME: ${{ secrets.CYPRESS_TEST_GITHUB_USER_NAME }}
          CYPRESS_APPSMITH_OAUTH2_GOOGLE_CLIENT_ID: ${{ secrets.CYPRESS_APPSMITH_OAUTH2_GOOGLE_CLIENT_ID }}
          CYPRESS_APPSMITH_OAUTH2_GOOGLE_CLIENT_SECRET: ${{ secrets.CYPRESS_APPSMITH_OAUTH2_GOOGLE_CLIENT_SECRET }}
          CYPRESS_APPSMITH_OAUTH2_GITHUB_CLIENT_ID: ${{ secrets.CYPRESS_APPSMITH_OAUTH2_GITHUB_CLIENT_ID }}
          CYPRESS_APPSMITH_OAUTH2_GITHUB_CLIENT_SECRET: ${{ secrets.CYPRESS_APPSMITH_OAUTH2_GITHUB_CLIENT_SECRET }}
          CYPRESS_OAUTH_SAML_EMAIL: ${{ secrets.CYPRESS_OAUTH_SAML_EMAIL }}
          CYPRESS_OAUTH_SAML_ENTITY_ID: ${{ secrets.CYPRESS_OAUTH_SAML_ENTITY_ID }}
          CYPRESS_OAUTH_SAML_METADATA_URL: ${{ secrets.CYPRESS_OAUTH_SAML_METADATA_URL }}
          CYPRESS_OAUTH_SAML_METADATA_XML: ${{ secrets.CYPRESS_OAUTH_SAML_METADATA_XML }}
          CYPRESS_OAUTH_SAML_PUB_CERT: ${{ secrets.CYPRESS_OAUTH_SAML_PUB_CERT }}
          CYPRESS_OAUTH_SAML_SSO_URL: ${{ secrets.CYPRESS_OAUTH_SAML_SSO_URL }}
          CYPRESS_OAUTH_SAML_REDIRECT_URL: ${{ secrets.CYPRESS_OAUTH_SAML_REDIRECT_URL }}
          CYPRESS_APPSMITH_OAUTH2_OIDC_CLIENT_ID: ${{ secrets.CYPRESS_APPSMITH_OAUTH2_OIDC_CLIENT_ID }}
          CYPRESS_APPSMITH_OAUTH2_OIDC_CLIENT_SECRET: ${{ secrets.CYPRESS_APPSMITH_OAUTH2_OIDC_CLIENT_SECRET }}
          CYPRESS_APPSMITH_OAUTH2_OIDC_AUTH_URL: ${{ secrets.CYPRESS_APPSMITH_OAUTH2_OIDC_AUTH_URL }}
          CYPRESS_APPSMITH_OAUTH2_OIDC_TOKEN_URL: ${{ secrets.CYPRESS_APPSMITH_OAUTH2_OIDC_TOKEN_URL }}
          CYPRESS_APPSMITH_OAUTH2_OIDC_USER_INFO: ${{ secrets.CYPRESS_APPSMITH_OAUTH2_OIDC_USER_INFO }}
          CYPRESS_APPSMITH_OAUTH2_OIDC_JWKS_URL: ${{ secrets.CYPRESS_APPSMITH_OAUTH2_OIDC_JWKS_URL }}
          CYPRESS_APPSMITH_OAUTH2_OIDC_OKTA_PASSWORD: ${{ secrets.CYPRESS_APPSMITH_OAUTH2_OIDC_OKTA_PASSWORD }}
          CYPRESS_APPSMITH_OAUTH2_OIDC_DIRECT_URL: ${{ secrets.CYPRESS_APPSMITH_OAUTH2_OIDC_DIRECT_URL }}
          CYPRESS_EXCLUDE_TAGS: "airgap"
          CYPRESS_AIRGAPPED: false
          APPSMITH_DISABLE_TELEMETRY: true
          APPSMITH_GOOGLE_MAPS_API_KEY: ${{ secrets.APPSMITH_GOOGLE_MAPS_API_KEY }}
          COMMIT_INFO_MESSAGE: ${{ github.event.pull_request.title }}
        with:
          browser: ${{ env.BROWSER_PATH }}
          headless: true
          record: true
          install: false
          parallel: true
          config-file: cypress_ci.json
          group: "Chrome-Fat Container tests"
          spec: "cypress/integration/SanitySuite/**/*"
          working-directory: app/client
          # tag will be either "push" or "pull_request"
          tag: ${{ github.event_name }}
          env: "NODE_ENV=development"

      # In case of second attempt only run failed specs
      - name: Run the cypress test with failed tests
        if: steps.run_result.outputs.run_result == 'failedtest'
        uses: cypress-io/github-action@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}
          CYPRESS_PROJECT_ID: ${{ secrets.CYPRESS_PROJECT_ID }}
          CYPRESS_USERNAME: ${{ secrets.CYPRESS_USERNAME }}
          CYPRESS_PASSWORD: ${{ secrets.CYPRESS_PASSWORD }}
          CYPRESS_TESTUSERNAME1: ${{ secrets.CYPRESS_TESTUSERNAME1 }}
          CYPRESS_TESTPASSWORD1: ${{ secrets.CYPRESS_TESTPASSWORD1 }}
          CYPRESS_TESTUSERNAME2: ${{ secrets.CYPRESS_TESTUSERNAME2 }}
          CYPRESS_TESTPASSWORD2: ${{ secrets.CYPRESS_TESTPASSWORD1 }}
          CYPRESS_TESTUSERNAME3: ${{ secrets.CYPRESS_TESTUSERNAME3 }}
          CYPRESS_TESTPASSWORD3: ${{ secrets.CYPRESS_TESTPASSWORD3 }}
          CYPRESS_TESTUSERNAME4: ${{ secrets.CYPRESS_TESTUSERNAME4 }}
          CYPRESS_TESTPASSWORD4: ${{ secrets.CYPRESS_TESTPASSWORD4 }}
          CYPRESS_S3_ACCESS_KEY: ${{ secrets.CYPRESS_S3_ACCESS_KEY }}
          CYPRESS_S3_SECRET_KEY: ${{ secrets.CYPRESS_S3_SECRET_KEY }}
          CYPRESS_GITEA_TOKEN: ${{ secrets.GITEA_TOKEN }}
          CYPRESS_AIRTABLE_BEARER: ${{ secrets.AIRTABLE_BEARER }}
          CYPRESS_ORACLE_HOST: ${{ secrets.ORACLE_HOST }}
          CYPRESS_ORACLE_SERVICE: ${{ secrets.ORACLE_SERVICE }}
          CYPRESS_ORACLE_USERNAME: ${{ secrets.ORACLE_USERNAME }}
          CYPRESS_ORACLE_PASSWORD: ${{ secrets.ORACLE_PASSWORD }}
          CYPRESS_GITHUB_PERSONAL_ACCESS_TOKEN: ${{ secrets.CYPRESS_GITHUB_PERSONAL_ACCESS_TOKEN }}
          CYPRESS_TEST_GITHUB_USER_NAME: ${{ secrets.CYPRESS_TEST_GITHUB_USER_NAME }}
          CYPRESS_APPSMITH_OAUTH2_GOOGLE_CLIENT_ID: ${{ secrets.CYPRESS_APPSMITH_OAUTH2_GOOGLE_CLIENT_ID }}
          CYPRESS_APPSMITH_OAUTH2_GOOGLE_CLIENT_SECRET: ${{ secrets.CYPRESS_APPSMITH_OAUTH2_GOOGLE_CLIENT_SECRET }}
          CYPRESS_APPSMITH_OAUTH2_GITHUB_CLIENT_ID: ${{ secrets.CYPRESS_APPSMITH_OAUTH2_GITHUB_CLIENT_ID }}
          CYPRESS_APPSMITH_OAUTH2_GITHUB_CLIENT_SECRET: ${{ secrets.CYPRESS_APPSMITH_OAUTH2_GITHUB_CLIENT_SECRET }}
          CYPRESS_OAUTH_SAML_EMAIL: ${{ secrets.CYPRESS_OAUTH_SAML_EMAIL }}
          CYPRESS_OAUTH_SAML_ENTITY_ID: ${{ secrets.CYPRESS_OAUTH_SAML_ENTITY_ID }}
          CYPRESS_OAUTH_SAML_METADATA_URL: ${{ secrets.CYPRESS_OAUTH_SAML_METADATA_URL }}
          CYPRESS_OAUTH_SAML_METADATA_XML: ${{ secrets.CYPRESS_OAUTH_SAML_METADATA_XML }}
          CYPRESS_OAUTH_SAML_PUB_CERT: ${{ secrets.CYPRESS_OAUTH_SAML_PUB_CERT }}
          CYPRESS_OAUTH_SAML_SSO_URL: ${{ secrets.CYPRESS_OAUTH_SAML_SSO_URL }}
          CYPRESS_OAUTH_SAML_REDIRECT_URL: ${{ secrets.CYPRESS_OAUTH_SAML_REDIRECT_URL }}
          CYPRESS_APPSMITH_OAUTH2_OIDC_CLIENT_ID: ${{ secrets.CYPRESS_APPSMITH_OAUTH2_OIDC_CLIENT_ID }}
          CYPRESS_APPSMITH_OAUTH2_OIDC_CLIENT_SECRET: ${{ secrets.CYPRESS_APPSMITH_OAUTH2_OIDC_CLIENT_SECRET }}
          CYPRESS_APPSMITH_OAUTH2_OIDC_AUTH_URL: ${{ secrets.CYPRESS_APPSMITH_OAUTH2_OIDC_AUTH_URL }}
          CYPRESS_APPSMITH_OAUTH2_OIDC_TOKEN_URL: ${{ secrets.CYPRESS_APPSMITH_OAUTH2_OIDC_TOKEN_URL }}
          CYPRESS_APPSMITH_OAUTH2_OIDC_USER_INFO: ${{ secrets.CYPRESS_APPSMITH_OAUTH2_OIDC_USER_INFO }}
          CYPRESS_APPSMITH_OAUTH2_OIDC_JWKS_URL: ${{ secrets.CYPRESS_APPSMITH_OAUTH2_OIDC_JWKS_URL }}
          CYPRESS_APPSMITH_OAUTH2_OIDC_OKTA_PASSWORD: ${{ secrets.CYPRESS_APPSMITH_OAUTH2_OIDC_OKTA_PASSWORD }}
          CYPRESS_APPSMITH_OAUTH2_OIDC_DIRECT_URL: ${{ secrets.CYPRESS_APPSMITH_OAUTH2_OIDC_DIRECT_URL }}
          CYPRESS_EXCLUDE_TAGS: "airgap"
          CYPRESS_AIRGAPPED: false
          APPSMITH_DISABLE_TELEMETRY: true
          APPSMITH_GOOGLE_MAPS_API_KEY: ${{ secrets.APPSMITH_GOOGLE_MAPS_API_KEY }}
          COMMIT_INFO_MESSAGE: ${{ github.event.pull_request.title }}
        with:
          browser: ${{ env.BROWSER_PATH }}
          headless: true
          record: true
          install: false
          parallel: true
          config-file: cypress_ci.json
          group: "Chrome-Fat Container tests"
          spec: ${{ env.failed_spec_env }}
          working-directory: app/client
          # tag will be either "push" or "pull_request"
          tag: ${{ github.event_name }}
          env: "NODE_ENV=development"

      - name: Collect CI container logs
        if: failure()
        working-directory: "."
        run: |
          docker logs appsmith 2>&1 > ~/dockerlogs-sanity.txt

      # Upload docker logs
      - name: Upload failed test list artifact
        if: failure()
        uses: actions/upload-artifact@v3
        with:
          name: dockerlogs-sanity-${{ matrix.job }}
          path: ~/dockerlogs-sanity.txt

      # Set status = failedtest
      - name: Set fail if there are test failures
        if: failure()
        run: echo "failedtest" >> $GITHUB_OUTPUT > ~/run_result

      # add list failed tests to a file
      - name: In case of test failures copy them to a file
        if: failure()
        run: |
          cd ${{ github.workspace }}/app/client/cypress/
          find screenshots -type f \( -iname "*\(attempt 2\).png" -o -iname "*before all hook*" -o -iname "*after all hook*" \) | sed 's/screenshots/cypress\/integration/g'| sed 's:/[^/]*$::' | sort -u > ~/failed_spec_ci_sanity

      # Upload failed test list using common path for all matrix job
      - name: Upload failed test list artifact
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: failed_spec_ci_sanity
          path: ~/failed_spec_ci_sanity

      # Force store previous run result to cache
      - name: Store the previous run result
        if: failure()
        uses: martijnhols/actions-cache/save@v3
        with:
          path: |
            ~/run_result_sanity
          key: ${{ github.run_id }}-sanity-${{ github.job }}
          restore-keys: |
            ${{ github.run_id }}-sanity-${{ github.job }}

      # Force store previous failed test list to cache
      - name: Store the previous failed test result
        if: failure()
        uses: martijnhols/actions-cache/save@v3
        with:
          path: |
            ~/failed_spec_ci_sanity
          key: ${{ github.run_id }}-failed_spec_ci_sanity-${{ github.job }}
          restore-keys: |
            ${{ github.run_id }}-failed_spec_ci_sanity-${{ github.job }}

      # Upload the log artifact so that it can be used by the test & deploy job in the workflow
      - name: Upload server logs bundle on failure
        uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: server-logs-${{ matrix.job }}
          path: app/server/server-logs.log

      # Set status = success
      - name: Save the status of the run
        run: echo "run_result=success" >> $GITHUB_OUTPUT > ~/run_result
