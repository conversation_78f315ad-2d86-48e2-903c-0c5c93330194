"use strict";(self.webpackChunkpageplug_datasource_frontend=self.webpackChunkpageplug_datasource_frontend||[]).push([[437,818],{241:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval("{/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   D: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* unused harmony export ThemeProvider */\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(6070);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(212);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(3017);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_2__);\n\r\n\r\n\r\nconst lightTheme = {\r\n    colors: {\r\n        primary: '#1890ff',\r\n        secondary: '#6c757d',\r\n        success: '#52c41a',\r\n        warning: '#faad14',\r\n        error: '#ff4d4f',\r\n        info: '#1890ff',\r\n        text: {\r\n            primary: '#262626',\r\n            secondary: '#595959',\r\n            disabled: '#bfbfbf'\r\n        },\r\n        background: {\r\n            primary: '#ffffff',\r\n            secondary: '#fafafa',\r\n            tertiary: '#f5f5f5'\r\n        },\r\n        border: {\r\n            primary: '#d9d9d9',\r\n            secondary: '#f0f0f0'\r\n        }\r\n    },\r\n    spacing: {\r\n        xs: '4px',\r\n        sm: '8px',\r\n        md: '16px',\r\n        lg: '24px',\r\n        xl: '32px',\r\n        xxl: '48px'\r\n    },\r\n    borderRadius: {\r\n        sm: '4px',\r\n        md: '6px',\r\n        lg: '8px'\r\n    },\r\n    shadows: {\r\n        sm: '0 2px 4px rgba(0, 0, 0, 0.06)',\r\n        md: '0 4px 12px rgba(0, 0, 0, 0.1)',\r\n        lg: '0 8px 24px rgba(0, 0, 0, 0.15)'\r\n    },\r\n    typography: {\r\n        fontFamily: \"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif\",\r\n        fontSize: {\r\n            xs: '12px',\r\n            sm: '14px',\r\n            md: '16px',\r\n            lg: '18px',\r\n            xl: '20px'\r\n        },\r\n        fontWeight: {\r\n            normal: 400,\r\n            medium: 500,\r\n            semibold: 600,\r\n            bold: 700\r\n        }\r\n    }\r\n};\r\nconst darkTheme = {\r\n    ...lightTheme,\r\n    colors: {\r\n        ...lightTheme.colors,\r\n        text: {\r\n            primary: '#ffffff',\r\n            secondary: '#d9d9d9',\r\n            disabled: '#595959'\r\n        },\r\n        background: {\r\n            primary: '#1f1f1f',\r\n            secondary: '#262626',\r\n            tertiary: '#141414'\r\n        },\r\n        border: {\r\n            primary: '#434343',\r\n            secondary: '#303030'\r\n        }\r\n    }\r\n};\r\nconst ThemeContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\r\nconst ThemeProvider = ({ children }) => {\r\n    const [isDark, setIsDark] = useState(() => {\r\n        const saved = localStorage.getItem('datasource-app-theme');\r\n        if (saved) {\r\n            return saved === 'dark';\r\n        }\r\n        return window.matchMedia('(prefers-color-scheme: dark)').matches;\r\n    });\r\n    const theme = isDark ? darkTheme : lightTheme;\r\n    const toggleTheme = () => {\r\n        setIsDark(prev => {\r\n            const newValue = !prev;\r\n            localStorage.setItem('datasource-app-theme', newValue ? 'dark' : 'light');\r\n            return newValue;\r\n        });\r\n    };\r\n    useEffect(() => {\r\n        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\r\n        const handleChange = (e) => {\r\n            const saved = localStorage.getItem('datasource-app-theme');\r\n            if (!saved) {\r\n                setIsDark(e.matches);\r\n            }\r\n        };\r\n        mediaQuery.addEventListener('change', handleChange);\r\n        return () => mediaQuery.removeEventListener('change', handleChange);\r\n    }, []);\r\n    useEffect(() => {\r\n        const root = document.documentElement;\r\n        if (isDark) {\r\n            root.classList.add('dark');\r\n        }\r\n        else {\r\n            root.classList.remove('dark');\r\n        }\r\n    }, [isDark]);\r\n    const contextValue = {\r\n        theme,\r\n        isDark,\r\n        toggleTheme\r\n    };\r\n    return (_jsx(ThemeContext.Provider, { value: contextValue, children: _jsx(StyledThemeProvider, { theme: theme, children: children }) }));\r\n};\r\nconst useTheme = () => {\r\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\r\n    if (context === undefined) {\r\n        throw new Error('useTheme must be used within a ThemeProvider');\r\n    }\r\n    return context;\r\n};\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///241\n\n}")},437:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval('{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(6070);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(212);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(8076);\n/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(antd__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(9017);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(3504);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(5469);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(1957);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(516);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(858);\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(1299);\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react_router_dom__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(3017);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _services_apiClient__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(8876);\n/* harmony import */ var _components_Common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(3128);\n/* harmony import */ var _hooks_useTheme__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(5869);\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nconst { Option } = antd__WEBPACK_IMPORTED_MODULE_2__.Select;\r\nconst { TextArea } = antd__WEBPACK_IMPORTED_MODULE_2__.Input;\r\nconst { Step } = antd__WEBPACK_IMPORTED_MODULE_2__.Steps;\r\nconst PageContainer = (styled_components__WEBPACK_IMPORTED_MODULE_10___default().div) `\n  padding: 24px;\n  max-width: 1000px;\n  margin: 0 auto;\n`;\r\nconst PageHeader = (styled_components__WEBPACK_IMPORTED_MODULE_10___default().div) `\n  margin-bottom: 24px;\n`;\r\nconst PageTitle = (styled_components__WEBPACK_IMPORTED_MODULE_10___default().h1) `\n  font-size: 24px;\n  font-weight: 600;\n  color: ${props => props.$isDark ? \'#ffffff\' : \'#262626\'};\n  margin: 0 0 8px 0;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n`;\r\nconst StepsContainer = (styled_components__WEBPACK_IMPORTED_MODULE_10___default().div) `\n  margin-bottom: 32px;\n`;\r\nconst FormSection = styled_components__WEBPACK_IMPORTED_MODULE_10___default()((0,antd__WEBPACK_IMPORTED_MODULE_2__.Card)) `\n  margin-bottom: 24px;\n`;\r\nconst TestResultContainer = (styled_components__WEBPACK_IMPORTED_MODULE_10___default().div) `\n  padding: 16px;\n  border-radius: 6px;\n  margin-top: 16px;\n  background-color: ${props => props.$success ? \'#f6ffed\' : \'#fff2f0\'};\n  border: 1px solid ${props => props.$success ? \'#b7eb8f\' : \'#ffccc7\'};\n`;\r\nconst DatasourceCreate = () => {\r\n    const { id } = (0,react_router_dom__WEBPACK_IMPORTED_MODULE_9__.useParams)();\r\n    const navigate = (0,react_router_dom__WEBPACK_IMPORTED_MODULE_9__.useNavigate)();\r\n    const { isDark } = (0,_hooks_useTheme__WEBPACK_IMPORTED_MODULE_13__/* .useTheme */ .D)();\r\n    const [form] = antd__WEBPACK_IMPORTED_MODULE_2__.Form.useForm();\r\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\r\n    const [plugins, setPlugins] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\r\n    const [selectedPlugin, setSelectedPlugin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\r\n    const [datasource, setDatasource] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\r\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\r\n    const [testing, setTesting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\r\n    const [testResult, setTestResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\r\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\r\n    const isEdit = !!id;\r\n    const loadPlugins = async () => {\r\n        try {\r\n            const response = await _services_apiClient__WEBPACK_IMPORTED_MODULE_11__/* .apiClient */ .u.get(\'/plugins\', {\r\n                params: { status: \'active\' }\r\n            });\r\n            setPlugins(response.data);\r\n        }\r\n        catch (error) {\r\n            antd__WEBPACK_IMPORTED_MODULE_2__.message.error(\'加载插件列表失败\');\r\n        }\r\n    };\r\n    const loadDatasource = async () => {\r\n        if (!id)\r\n            return;\r\n        try {\r\n            setLoading(true);\r\n            const response = await _services_apiClient__WEBPACK_IMPORTED_MODULE_11__/* .apiClient */ .u.get(`/datasources/${id}`);\r\n            const ds = response.data;\r\n            setDatasource(ds);\r\n            form.setFieldsValue({\r\n                name: ds.name,\r\n                pluginId: ds.pluginId,\r\n                url: ds.datasourceConfiguration.url,\r\n                databaseName: ds.datasourceConfiguration.databaseName,\r\n                authType: ds.datasourceConfiguration.authentication?.authType,\r\n                username: ds.datasourceConfiguration.authentication?.username,\r\n                sslEnabled: ds.datasourceConfiguration.connection?.ssl?.enabled,\r\n                timeout: ds.datasourceConfiguration.connection?.timeout\r\n            });\r\n            const plugin = plugins.find(p => p.id === ds.pluginId);\r\n            setSelectedPlugin(plugin || null);\r\n        }\r\n        catch (error) {\r\n            antd__WEBPACK_IMPORTED_MODULE_2__.message.error(\'加载数据源详情失败\');\r\n        }\r\n        finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\r\n        loadPlugins();\r\n    }, []);\r\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\r\n        if (plugins.length > 0 && isEdit) {\r\n            loadDatasource();\r\n        }\r\n    }, [plugins, id]);\r\n    const handlePluginChange = (pluginId) => {\r\n        const plugin = plugins.find(p => p.id === pluginId);\r\n        setSelectedPlugin(plugin || null);\r\n        setTestResult(null);\r\n        if (plugin) {\r\n            const defaults = {};\r\n            switch (plugin.name) {\r\n                case \'mysql\':\r\n                    defaults.url = \'mysql://localhost:3306\';\r\n                    defaults.timeout = 30000;\r\n                    break;\r\n                case \'postgresql\':\r\n                    defaults.url = \'postgresql://localhost:5432\';\r\n                    defaults.timeout = 15000;\r\n                    break;\r\n                case \'mongodb\':\r\n                    defaults.url = \'mongodb://localhost:27017\';\r\n                    break;\r\n                case \'redis\':\r\n                    defaults.url = \'redis://localhost:6379\';\r\n                    break;\r\n                case \'restapi\':\r\n                    defaults.url = \'https://api.example.com\';\r\n                    break;\r\n            }\r\n            form.setFieldsValue(defaults);\r\n        }\r\n    };\r\n    const handleTestConnection = async () => {\r\n        try {\r\n            await form.validateFields();\r\n            const values = form.getFieldsValue();\r\n            setTesting(true);\r\n            setTestResult(null);\r\n            const testRequest = {\r\n                name: values.name || \'Test Connection\',\r\n                pluginId: values.pluginId,\r\n                workspaceId: \'workspace-123\',\r\n                datasourceConfiguration: {\r\n                    url: values.url,\r\n                    databaseName: values.databaseName,\r\n                    authentication: values.authType ? {\r\n                        authType: values.authType,\r\n                        username: values.username,\r\n                        password: values.password\r\n                    } : undefined,\r\n                    connection: {\r\n                        ssl: {\r\n                            enabled: values.sslEnabled || false\r\n                        },\r\n                        timeout: values.timeout\r\n                    }\r\n                }\r\n            };\r\n            if (isEdit && id) {\r\n                const response = await _services_apiClient__WEBPACK_IMPORTED_MODULE_11__/* .apiClient */ .u.post(`/datasources/${id}/test`);\r\n                setTestResult(response.data);\r\n            }\r\n            else {\r\n                const mockResult = {\r\n                    success: Math.random() > 0.3,\r\n                    message: Math.random() > 0.3 ? \'连接成功\' : \'连接失败：认证错误\',\r\n                    responseTime: Math.floor(Math.random() * 1000) + 100\r\n                };\r\n                await new Promise(resolve => setTimeout(resolve, 1500));\r\n                setTestResult(mockResult);\r\n            }\r\n            if (testResult?.success) {\r\n                antd__WEBPACK_IMPORTED_MODULE_2__.message.success(\'连接测试成功\');\r\n                setCurrentStep(2);\r\n            }\r\n            else {\r\n                antd__WEBPACK_IMPORTED_MODULE_2__.message.error(\'连接测试失败\');\r\n            }\r\n        }\r\n        catch (error) {\r\n            antd__WEBPACK_IMPORTED_MODULE_2__.message.error(\'连接测试失败\');\r\n            setTestResult({\r\n                success: false,\r\n                message: \'连接测试失败\',\r\n                responseTime: 0\r\n            });\r\n        }\r\n        finally {\r\n            setTesting(false);\r\n        }\r\n    };\r\n    const handleSave = async () => {\r\n        try {\r\n            await form.validateFields();\r\n            const values = form.getFieldsValue();\r\n            setSaving(true);\r\n            const request = {\r\n                name: values.name,\r\n                pluginId: values.pluginId,\r\n                workspaceId: \'workspace-123\',\r\n                datasourceConfiguration: {\r\n                    url: values.url,\r\n                    databaseName: values.databaseName,\r\n                    authentication: values.authType ? {\r\n                        authType: values.authType,\r\n                        username: values.username,\r\n                        password: values.password\r\n                    } : undefined,\r\n                    connection: {\r\n                        ssl: {\r\n                            enabled: values.sslEnabled || false\r\n                        },\r\n                        timeout: values.timeout\r\n                    }\r\n                }\r\n            };\r\n            if (isEdit && id) {\r\n                await _services_apiClient__WEBPACK_IMPORTED_MODULE_11__/* .apiClient */ .u.put(`/datasources/${id}`, request);\r\n                antd__WEBPACK_IMPORTED_MODULE_2__.message.success(\'数据源更新成功\');\r\n            }\r\n            else {\r\n                const response = await _services_apiClient__WEBPACK_IMPORTED_MODULE_11__/* .apiClient */ .u.post(\'/datasources\', request);\r\n                antd__WEBPACK_IMPORTED_MODULE_2__.message.success(\'数据源创建成功\');\r\n                navigate(`/datasources/${response.data.id}`);\r\n                return;\r\n            }\r\n            navigate(\'/datasources\');\r\n        }\r\n        catch (error) {\r\n            antd__WEBPACK_IMPORTED_MODULE_2__.message.error(isEdit ? \'更新数据源失败\' : \'创建数据源失败\');\r\n        }\r\n        finally {\r\n            setSaving(false);\r\n        }\r\n    };\r\n    if (loading) {\r\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_Common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_12__/* .LoadingSpinner */ .kt, { text: "\\u52A0\\u8F7D\\u6570\\u636E\\u6E90\\u4FE1\\u606F\\u4E2D..." });\r\n    }\r\n    const steps = [\r\n        {\r\n            title: \'选择插件\',\r\n            description: \'选择数据源类型\',\r\n            icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A, {})\r\n        },\r\n        {\r\n            title: \'配置连接\',\r\n            description: \'设置连接参数\',\r\n            icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A, {})\r\n        },\r\n        {\r\n            title: \'测试连接\',\r\n            description: \'验证连接配置\',\r\n            icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A, {})\r\n        }\r\n    ];\r\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(PageContainer, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(PageHeader, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Space, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Button, { icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A, {}), onClick: () => navigate(\'/datasources\'), children: "\\u8FD4\\u56DE" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(PageTitle, { "$isDark": isDark, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A, {}), isEdit ? \'编辑数据源\' : \'新建数据源\'] })] }), !isEdit && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(StepsContainer, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Steps, { current: currentStep, items: steps }) })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Form, { form: form, layout: "vertical", onFinish: handleSave, initialValues: {\r\n                    authType: \'basic\',\r\n                    sslEnabled: false,\r\n                    timeout: 30000\r\n                }, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(FormSection, { title: "\\u57FA\\u672C\\u4FE1\\u606F", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Row, { gutter: 16, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Col, { span: 12, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Form.Item, { label: "\\u6570\\u636E\\u6E90\\u540D\\u79F0", name: "name", rules: [\r\n                                                { required: true, message: \'请输入数据源名称\' },\r\n                                                { min: 2, max: 50, message: \'名称长度为2-50个字符\' }\r\n                                            ], children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Input, { placeholder: "\\u8BF7\\u8F93\\u5165\\u6570\\u636E\\u6E90\\u540D\\u79F0" }) }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Col, { span: 12, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Form.Item, { label: "\\u63D2\\u4EF6\\u7C7B\\u578B", name: "pluginId", rules: [{ required: true, message: \'请选择插件类型\' }], children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Select, { placeholder: "\\u8BF7\\u9009\\u62E9\\u63D2\\u4EF6\\u7C7B\\u578B", onChange: handlePluginChange, disabled: isEdit, children: plugins.map(plugin => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Option, { value: plugin.id, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Space, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: plugin.displayName }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { style: { color: \'#8c8c8c\' }, children: ["v", plugin.version] })] }) }, plugin.id))) }) }) })] }), selectedPlugin && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Alert, { message: selectedPlugin.displayName, description: selectedPlugin.description, type: "info", showIcon: true, style: { marginBottom: 16 } }))] }), selectedPlugin && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(FormSection, { title: "\\u8FDE\\u63A5\\u914D\\u7F6E", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Row, { gutter: 16, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Col, { span: 12, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Form.Item, { label: "\\u8FDE\\u63A5\\u5730\\u5740", name: "url", rules: [{ required: true, message: \'请输入连接地址\' }], children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Input, { placeholder: "\\u4F8B\\u5982: mysql://localhost:3306" }) }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Col, { span: 12, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Form.Item, { label: "\\u6570\\u636E\\u5E93\\u540D", name: "databaseName", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Input, { placeholder: "\\u8BF7\\u8F93\\u5165\\u6570\\u636E\\u5E93\\u540D\\uFF08\\u53EF\\u9009\\uFF09" }) }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Divider, { orientation: "left", children: "\\u8BA4\\u8BC1\\u914D\\u7F6E" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Row, { gutter: 16, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Col, { span: 8, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Form.Item, { label: "\\u8BA4\\u8BC1\\u65B9\\u5F0F", name: "authType", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Select, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Option, { value: "basic", children: "\\u7528\\u6237\\u540D\\u5BC6\\u7801" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Option, { value: "oauth", children: "OAuth" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Option, { value: "apikey", children: "API Key" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Option, { value: "bearer", children: "Bearer Token" })] }) }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Col, { span: 8, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Form.Item, { label: "\\u7528\\u6237\\u540D", name: "username", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Input, { placeholder: "\\u8BF7\\u8F93\\u5165\\u7528\\u6237\\u540D" }) }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Col, { span: 8, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Form.Item, { label: "\\u5BC6\\u7801", name: "password", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Input.Password, { placeholder: "\\u8BF7\\u8F93\\u5165\\u5BC6\\u7801" }) }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Divider, { orientation: "left", children: "\\u9AD8\\u7EA7\\u914D\\u7F6E" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Row, { gutter: 16, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Col, { span: 8, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Form.Item, { label: "\\u542F\\u7528SSL", name: "sslEnabled", valuePropName: "checked", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Switch, {}) }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Col, { span: 8, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Form.Item, { label: "\\u8FDE\\u63A5\\u8D85\\u65F6(ms)", name: "timeout", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Input, { type: "number", placeholder: "30000" }) }) })] })] })), testResult && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(FormSection, { title: "\\u8FDE\\u63A5\\u6D4B\\u8BD5\\u7ED3\\u679C", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(TestResultContainer, { "$success": testResult.success, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { style: { marginBottom: 8 }, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("strong", { children: testResult.success ? \'✅ 连接成功\' : \'❌ 连接失败\' }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: ["\\u6D88\\u606F: ", testResult.message] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: ["\\u54CD\\u5E94\\u65F6\\u95F4: ", testResult.responseTime, "ms"] }), testResult.details && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { style: { marginTop: 8 }, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("details", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("summary", { children: "\\u8BE6\\u7EC6\\u4FE1\\u606F" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("pre", { style: { marginTop: 8, fontSize: \'12px\' }, children: JSON.stringify(testResult.details, null, 2) })] }) }))] }) })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Card, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Space, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Button, { type: "primary", icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A, {}), loading: testing, onClick: handleTestConnection, disabled: !selectedPlugin, children: "\\u6D4B\\u8BD5\\u8FDE\\u63A5" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Button, { type: "primary", icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .A, {}), loading: saving, onClick: handleSave, disabled: !testResult?.success, children: isEdit ? \'更新数据源\' : \'创建数据源\' }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Button, { onClick: () => navigate(\'/datasources\'), children: "\\u53D6\\u6D88" })] }) })] })] }));\r\n};\r\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DatasourceCreate);\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///437\n\n}')},5869:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval("{/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   D: () => (/* reexport safe */ _providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_0__.D)\n/* harmony export */ });\n/* harmony import */ var _providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(241);\n\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTg2OS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQ3NEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGFnZXBsdWctZGF0YXNvdXJjZS1mcm9udGVuZC8uL3NyYy9ob29rcy91c2VUaGVtZS50cz8zZTk1Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIOmHjeaWsOWvvOWHulRoZW1lUHJvdmlkZXLkuK3nmoR1c2VUaGVtZSBob29rXG5leHBvcnQgeyB1c2VUaGVtZSB9IGZyb20gJy4uL3Byb3ZpZGVycy9UaGVtZVByb3ZpZGVyJztcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///5869\n\n}")}}]);