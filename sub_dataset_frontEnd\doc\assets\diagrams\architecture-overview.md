# PagePlug 数据源架构图表

## 1. 整体架构图

```mermaid
graph TB
    subgraph "用户界面层"
        A[Web编辑器<br/>React] 
        B[移动端应用<br/>Taro]
        C[数据源管理<br/>Vue3]
    end
    
    subgraph "网关层"
        D[Nginx 反向代理]
    end
    
    subgraph "应用服务层"
        E[应用管理服务<br/>Java Spring]
        F[数据源服务<br/>Python FastAPI]
        G[用户服务<br/>Java Spring]
    end
    
    subgraph "插件层"
        H[MySQL插件]
        I[MongoDB插件]
        J[REST API插件]
        K[AI插件]
    end
    
    subgraph "数据存储层"
        L[(MongoDB<br/>主数据库)]
        M[(Redis<br/>缓存)]
        N[(外部数据源)]
    end
    
    A --> D
    B --> D
    C --> D
    D --> E
    D --> F
    D --> G
    F --> H
    F --> I
    F --> J
    F --> K
    E --> L
    F --> L
    G --> L
    F --> M
    H --> N
    I --> N
    J --> N
```

## 2. 数据源插件架构

```mermaid
graph TB
    subgraph "应用层"
        A[数据源管理界面]
        B[查询构建器]
        C[结果处理器]
    end
    
    subgraph "服务层"
        D[数据源服务]
        E[插件管理器]
        F[连接池管理]
    end
    
    subgraph "插件层"
        G[MySQL插件]
        H[PostgreSQL插件]
        I[MongoDB插件]
        J[Redis插件]
        K[REST API插件]
    end
    
    subgraph "接口层"
        L[PluginExecutor 统一接口]
    end
    
    subgraph "数据源"
        M[(MySQL)]
        N[(PostgreSQL)]
        O[(MongoDB)]
        P[(Redis)]
        Q[REST API]
    end
    
    A --> D
    B --> D
    C --> D
    D --> E
    D --> F
    E --> G
    E --> H
    E --> I
    E --> J
    E --> K
    G --> L
    H --> L
    I --> L
    J --> L
    K --> L
    G --> M
    H --> N
    I --> O
    J --> P
    K --> Q
```

## 3. 微前端架构

```mermaid
graph TB
    subgraph "主应用 Shell App"
        A[React 主框架]
        B[路由管理]
        C[状态管理]
        D[认证服务]
    end
    
    subgraph "数据源微前端"
        E[Vue3 应用]
        F[数据源路由]
        G[数据源状态]
        H[数据源组件]
    end
    
    subgraph "共享服务"
        I[通信总线]
        J[主题系统]
        K[工具库]
    end
    
    subgraph "Module Federation"
        L[远程模块加载]
        M[依赖共享]
        N[运行时集成]
    end
    
    A --> L
    E --> L
    L --> M
    L --> N
    A --> I
    E --> I
    I --> J
    I --> K
    B --> F
    C --> G
    D --> H
```

## 4. 数据流架构

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端应用
    participant A as API网关
    participant S as 数据源服务
    participant P as 插件系统
    participant D as 数据库
    participant E as 外部数据源
    
    U->>F: 创建数据源
    F->>A: POST /api/v1/datasources
    A->>S: 转发请求
    S->>P: 加载插件
    P->>E: 测试连接
    E-->>P: 连接结果
    P-->>S: 返回结果
    S->>D: 保存数据源
    D-->>S: 保存成功
    S-->>A: 返回响应
    A-->>F: 返回结果
    F-->>U: 显示结果
```

## 5. 插件生命周期

```mermaid
stateDiagram-v2
    [*] --> 未加载
    未加载 --> 加载中: 加载插件
    加载中 --> 已加载: 加载成功
    加载中 --> 加载失败: 加载失败
    已加载 --> 运行中: 启动插件
    运行中 --> 已停止: 停止插件
    已停止 --> 运行中: 重启插件
    运行中 --> 错误状态: 运行异常
    错误状态 --> 运行中: 恢复正常
    已加载 --> 未加载: 卸载插件
    已停止 --> 未加载: 卸载插件
    加载失败 --> 未加载: 清理资源
```

## 6. 缓存架构

```mermaid
graph LR
    subgraph "应用层"
        A[数据源服务]
    end
    
    subgraph "缓存层"
        B[本地缓存<br/>内存]
        C[分布式缓存<br/>Redis]
    end
    
    subgraph "数据层"
        D[(MongoDB)]
        E[(外部数据源)]
    end
    
    A --> B
    A --> C
    B --> C
    C --> D
    A --> E
    
    B -.->|缓存未命中| C
    C -.->|缓存未命中| D
```

## 7. 部署架构

```mermaid
graph TB
    subgraph "负载均衡层"
        A[Nginx/HAProxy]
    end
    
    subgraph "应用层"
        B[数据源服务实例1]
        C[数据源服务实例2]
        D[数据源服务实例3]
    end
    
    subgraph "数据层"
        E[(MongoDB 主节点)]
        F[(MongoDB 从节点1)]
        G[(MongoDB 从节点2)]
        H[(Redis 集群)]
    end
    
    subgraph "监控层"
        I[Prometheus]
        J[Grafana]
        K[ELK Stack]
    end
    
    A --> B
    A --> C
    A --> D
    B --> E
    C --> E
    D --> E
    E --> F
    E --> G
    B --> H
    C --> H
    D --> H
    B --> I
    C --> I
    D --> I
    I --> J
    B --> K
    C --> K
    D --> K
```
