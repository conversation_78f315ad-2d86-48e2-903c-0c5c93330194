import React from 'react';
import { Card, Empty } from 'antd';
import { CodeOutlined } from '@ant-design/icons';

interface AdvancedQueryEditorProps {
  value?: string;
  onChange?: (value: string) => void;
}

const AdvancedQueryEditor: React.FC<AdvancedQueryEditorProps> = ({ value, onChange }) => {
  return (
    <Card title="高级查询编辑器" extra={<CodeOutlined />}>
      <Empty
        description="高级查询编辑器开发中..."
        image={Empty.PRESENTED_IMAGE_SIMPLE}
      />
    </Card>
  );
};

export default AdvancedQueryEditor;
