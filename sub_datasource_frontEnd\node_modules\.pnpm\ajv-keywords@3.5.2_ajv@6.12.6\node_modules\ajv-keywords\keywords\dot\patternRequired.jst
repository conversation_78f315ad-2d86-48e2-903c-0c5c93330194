{{# def.definitions }}
{{# def.errors }}
{{# def.setupKeyword }}

{{
  var $key = 'key' + $lvl
    , $idx = 'idx' + $lvl
    , $matched = 'patternMatched' + $lvl
    , $dataProperties = 'dataProperties' + $lvl
    , $closingBraces = ''
    , $ownProperties = it.opts.ownProperties;
}}

var {{=$valid}} = true;
{{? $ownProperties }}
  var {{=$dataProperties}} = undefined;
{{?}}

{{~ $schema:$pProperty }}
  var {{=$matched}} = false;
  {{# def.iterateProperties }}
    {{=$matched}} = {{= it.usePattern($pProperty) }}.test({{=$key}});
    if ({{=$matched}}) break;
  }

  {{ var $missingPattern = it.util.escapeQuotes($pProperty); }}
  if (!{{=$matched}}) {
    {{=$valid}} = false;
    {{# def.addError:'patternRequired' }}
  } {{# def.elseIfValid }}
{{~}}

{{= $closingBraces }}
