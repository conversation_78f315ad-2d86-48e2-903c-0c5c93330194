# PagePlug 数据源模块迁移计划

## 1. 迁移目标和策略

### 1.1 迁移目标
- **技术现代化**: 从Java Spring Boot迁移到Python FastAPI
- **架构优化**: 从单体应用抽离为微前端子应用
- **性能提升**: 优化数据源连接和查询性能
- **维护性增强**: 提高代码可读性和维护效率
- **扩展性改进**: 更灵活的插件系统和API设计

### 1.2 迁移策略
- **渐进式迁移**: 分阶段逐步迁移，确保业务连续性
- **双轨运行**: 新旧系统并行运行，逐步切换流量
- **向后兼容**: 保持API向后兼容性
- **数据一致性**: 确保数据迁移的完整性和一致性
- **风险控制**: 制定回滚方案和应急预案

### 1.3 技术选型对比

| 方面 | 当前技术栈 | 目标技术栈 | 优势 |
|------|------------|------------|------|
| 后端框架 | Spring Boot 3.0.9 | FastAPI 0.104+ | 更高性能，更简洁的API设计 |
| 编程语言 | Java 17 | Python 3.11+ | 开发效率高，生态丰富 |
| 异步处理 | Project Reactor | asyncio + aiohttp | 原生异步支持，更直观 |
| 数据库ORM | Spring Data MongoDB | Motor + Beanie | 异步MongoDB驱动 |
| 缓存 | Spring Cache + Redis | aioredis | 异步Redis客户端 |
| 插件系统 | PF4J | 自定义插件系统 | 更轻量，更灵活 |
| 前端框架 | React 17 | Vue 3 | 更现代的响应式框架 |

## 2. 迁移阶段规划

### 2.1 第一阶段：基础设施准备 (4周)

#### 2.1.1 环境搭建
- **开发环境**: 搭建Python开发环境和工具链
- **CI/CD**: 配置Python项目的持续集成和部署
- **监控系统**: 部署应用监控和日志收集
- **测试环境**: 搭建独立的测试环境

#### 2.1.2 核心框架搭建
```python
# 项目结构
datasource-service/
├── app/
│   ├── api/                 # API路由
│   ├── core/               # 核心配置
│   ├── models/             # 数据模型
│   ├── services/           # 业务服务
│   ├── plugins/            # 插件系统
│   └── utils/              # 工具函数
├── tests/                  # 测试代码
├── migrations/             # 数据迁移
├── requirements.txt        # 依赖管理
└── docker/                 # 容器配置
```

#### 2.1.3 基础服务实现
- **FastAPI应用**: 基础API框架搭建
- **数据库连接**: MongoDB异步连接池
- **缓存服务**: Redis异步客户端
- **配置管理**: 环境配置和密钥管理
- **日志系统**: 结构化日志记录

### 2.2 第二阶段：核心功能迁移 (6周)

#### 2.2.1 数据模型迁移
```python
# 数据源模型
from beanie import Document
from pydantic import BaseModel
from typing import Optional, List, Dict

class DatasourceConfiguration(BaseModel):
    url: str
    authentication: Optional[Dict]
    properties: Optional[List[Dict]]
    headers: Optional[List[Dict]]
    connection: Optional[Dict]

class Datasource(Document):
    name: str
    plugin_id: str
    workspace_id: str
    datasource_configuration: DatasourceConfiguration
    structure: Optional[Dict]
    is_valid: bool = False
    is_configured: bool = False
    
    class Settings:
        name = "datasources"
```

#### 2.2.2 API接口迁移
```python
# FastAPI路由
from fastapi import APIRouter, Depends, HTTPException
from app.services.datasource_service import DatasourceService

router = APIRouter(prefix="/api/v1/datasources")

@router.post("/", response_model=DatasourceResponse)
async def create_datasource(
    datasource: DatasourceCreate,
    service: DatasourceService = Depends()
):
    try:
        result = await service.create(datasource)
        return DatasourceResponse(data=result)
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/{datasource_id}/test")
async def test_datasource(
    datasource_id: str,
    service: DatasourceService = Depends()
):
    result = await service.test_connection(datasource_id)
    return TestResultResponse(data=result)
```

#### 2.2.3 业务服务迁移
```python
# 数据源服务
class DatasourceService:
    def __init__(self, plugin_manager: PluginManager):
        self.plugin_manager = plugin_manager
        
    async def create(self, datasource_data: DatasourceCreate) -> Datasource:
        # 验证数据源配置
        await self._validate_configuration(datasource_data)
        
        # 创建数据源
        datasource = Datasource(**datasource_data.dict())
        await datasource.save()
        
        # 测试连接
        await self.test_connection(datasource.id)
        
        return datasource
    
    async def test_connection(self, datasource_id: str) -> TestResult:
        datasource = await Datasource.get(datasource_id)
        plugin = await self.plugin_manager.get_plugin(datasource.plugin_id)
        
        return await plugin.test_connection(datasource.datasource_configuration)
```

### 2.3 第三阶段：插件系统迁移 (8周)

#### 2.3.1 插件框架设计
```python
# 插件基类
from abc import ABC, abstractmethod
from typing import Any, Dict, List

class PluginExecutor(ABC):
    @abstractmethod
    async def create_connection(self, config: Dict) -> Any:
        pass
    
    @abstractmethod
    async def destroy_connection(self, connection: Any) -> None:
        pass
    
    @abstractmethod
    async def execute_query(self, connection: Any, query: str, params: List = None) -> Dict:
        pass
    
    @abstractmethod
    async def test_connection(self, config: Dict) -> Dict:
        pass
    
    async def get_structure(self, connection: Any) -> Dict:
        return {}

# 插件管理器
class PluginManager:
    def __init__(self):
        self.plugins = {}
        
    async def load_plugin(self, plugin_id: str) -> PluginExecutor:
        if plugin_id not in self.plugins:
            module = await self._import_plugin(plugin_id)
            self.plugins[plugin_id] = module.get_executor()
        return self.plugins[plugin_id]
    
    async def _import_plugin(self, plugin_id: str):
        return __import__(f"app.plugins.{plugin_id}", fromlist=[''])
```

#### 2.3.2 核心插件迁移
- **MySQL插件**: 使用aiomysql异步驱动
- **PostgreSQL插件**: 使用asyncpg异步驱动
- **MongoDB插件**: 使用motor异步驱动
- **Redis插件**: 使用aioredis异步客户端
- **REST API插件**: 使用aiohttp异步HTTP客户端

#### 2.3.3 插件示例实现
```python
# MySQL插件实现
import aiomysql
from app.plugins.base import PluginExecutor

class MySQLExecutor(PluginExecutor):
    async def create_connection(self, config: Dict) -> aiomysql.Connection:
        return await aiomysql.connect(
            host=config['host'],
            port=config.get('port', 3306),
            user=config['username'],
            password=config['password'],
            db=config.get('database'),
            autocommit=True
        )
    
    async def destroy_connection(self, connection: aiomysql.Connection) -> None:
        connection.close()
    
    async def execute_query(self, connection: aiomysql.Connection, 
                          query: str, params: List = None) -> Dict:
        async with connection.cursor(aiomysql.DictCursor) as cursor:
            await cursor.execute(query, params)
            if query.strip().upper().startswith('SELECT'):
                result = await cursor.fetchall()
                return {'data': result, 'rows_affected': cursor.rowcount}
            else:
                return {'rows_affected': cursor.rowcount}
```

### 2.4 第四阶段：前端重构 (6周)

#### 2.4.1 Vue3应用搭建
```javascript
// 项目结构
datasource-frontend/
├── src/
│   ├── components/         # 组件库
│   ├── views/             # 页面视图
│   ├── stores/            # 状态管理
│   ├── composables/       # 组合式API
│   ├── api/               # API接口
│   └── utils/             # 工具函数
├── tests/                 # 测试代码
└── vite.config.js         # 构建配置
```

#### 2.4.2 状态管理迁移
```javascript
// Pinia状态管理
import { defineStore } from 'pinia'
import { datasourceApi } from '@/api/datasource'

export const useDatasourceStore = defineStore('datasource', {
  state: () => ({
    datasources: [],
    currentDatasource: null,
    loading: false,
    error: null
  }),
  
  actions: {
    async fetchDatasources(workspaceId) {
      this.loading = true
      try {
        const response = await datasourceApi.list({ workspaceId })
        this.datasources = response.data
      } catch (error) {
        this.error = error.message
      } finally {
        this.loading = false
      }
    },
    
    async createDatasource(datasourceData) {
      const response = await datasourceApi.create(datasourceData)
      this.datasources.push(response.data)
      return response.data
    }
  }
})
```

#### 2.4.3 组件重构
```vue
<!-- 数据源列表组件 -->
<template>
  <div class="datasource-list">
    <el-table :data="datasources" :loading="loading">
      <el-table-column prop="name" label="名称" />
      <el-table-column prop="pluginName" label="类型" />
      <el-table-column prop="status" label="状态">
        <template #default="{ row }">
          <el-tag :type="row.isValid ? 'success' : 'danger'">
            {{ row.isValid ? '正常' : '异常' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="{ row }">
          <el-button @click="testConnection(row.id)">测试连接</el-button>
          <el-button @click="editDatasource(row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useDatasourceStore } from '@/stores/datasource'

const store = useDatasourceStore()
const { datasources, loading } = storeToRefs(store)

onMounted(() => {
  store.fetchDatasources()
})

const testConnection = async (datasourceId) => {
  await store.testConnection(datasourceId)
}
</script>
```

### 2.5 第五阶段：集成测试和部署 (4周)

#### 2.5.1 集成测试
- **API测试**: 使用pytest进行API接口测试
- **插件测试**: 各插件功能完整性测试
- **性能测试**: 并发性能和响应时间测试
- **兼容性测试**: 与现有系统的兼容性验证

#### 2.5.2 数据迁移
```python
# 数据迁移脚本
import asyncio
from motor.motor_asyncio import AsyncIOMotorClient

async def migrate_datasources():
    # 连接源数据库和目标数据库
    source_client = AsyncIOMotorClient("mongodb://source:27017")
    target_client = AsyncIOMotorClient("mongodb://target:27017")
    
    source_db = source_client.appsmith
    target_db = target_client.datasource_service
    
    # 迁移数据源数据
    async for datasource in source_db.datasources.find():
        # 数据格式转换
        migrated_data = transform_datasource_data(datasource)
        
        # 插入目标数据库
        await target_db.datasources.insert_one(migrated_data)
        
    print("数据迁移完成")

def transform_datasource_data(source_data):
    # 字段映射和格式转换
    return {
        'name': source_data['name'],
        'plugin_id': source_data['pluginId'],
        'workspace_id': source_data['workspaceId'],
        'datasource_configuration': source_data['datasourceConfiguration'],
        'is_valid': source_data.get('isValid', False),
        'created_at': source_data.get('createdAt'),
        'updated_at': source_data.get('updatedAt')
    }
```

#### 2.5.3 部署策略
```yaml
# Docker Compose部署
version: '3.8'
services:
  datasource-api:
    build: ./datasource-service
    ports:
      - "8000:8000"
    environment:
      - MONGODB_URL=mongodb://mongo:27017
      - REDIS_URL=redis://redis:6379
    depends_on:
      - mongo
      - redis
  
  datasource-frontend:
    build: ./datasource-frontend
    ports:
      - "3000:80"
    depends_on:
      - datasource-api
  
  mongo:
    image: mongo:6.0
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db
  
  redis:
    image: redis:7.0
    ports:
      - "6379:6379"
```

## 3. 风险评估和应对策略

### 3.1 技术风险
- **性能风险**: Python性能可能不如Java
  - **应对**: 使用异步编程和性能优化技术
- **生态风险**: Python数据库驱动成熟度
  - **应对**: 选择成熟稳定的异步驱动库
- **兼容性风险**: API接口变更影响现有客户端
  - **应对**: 保持API向后兼容，提供迁移指南

### 3.2 业务风险
- **数据丢失**: 迁移过程中数据丢失
  - **应对**: 完整的数据备份和验证机制
- **服务中断**: 迁移导致服务不可用
  - **应对**: 蓝绿部署和快速回滚机制
- **功能缺失**: 新系统功能不完整
  - **应对**: 详细的功能对比和测试验证

### 3.3 时间风险
- **开发延期**: 技术难度超出预期
  - **应对**: 预留缓冲时间，分阶段交付
- **测试不充分**: 测试时间不足
  - **应对**: 自动化测试和并行测试

## 4. 成功标准和验收条件

### 4.1 功能完整性
- [ ] 所有现有数据源类型支持
- [ ] API接口100%兼容
- [ ] 插件系统功能完整
- [ ] 前端功能对等

### 4.2 性能指标
- [ ] API响应时间 < 500ms
- [ ] 数据源连接时间 < 3s
- [ ] 查询执行性能不低于现有系统
- [ ] 并发处理能力 > 1000 QPS

### 4.3 质量标准
- [ ] 代码测试覆盖率 > 80%
- [ ] 零数据丢失
- [ ] 系统可用性 > 99.9%
- [ ] 安全漏洞扫描通过

### 4.4 运维指标
- [ ] 部署自动化完成
- [ ] 监控告警配置完整
- [ ] 日志收集和分析正常
- [ ] 备份恢复机制验证通过
