name: 🐞 Bug report
description: Create a bug report to help us improve Appsmith
title: "[Bug]: "
labels: [Bug, Needs Triaging]
assignees:
- <PERSON><PERSON>-Nandagopal
- pranavkanade
body:
- type: markdown
  attributes:
    value: |
      Thanks for taking the time to fill out this bug report! Please fill the form in English
- type: checkboxes
  attributes:
    label: Is there an existing issue for this?
    options:
    - label: I have searched the existing issues
      required: true
- type: textarea
  attributes:
    label: Description
    description: A concise description of what you're experiencing and what you expect.
    placeholder: |
      When I do <X>, <Y> happens and I see the error message attached below:
      ```...```
      What I expect is <Z>
  validations:
    required: true
- type: textarea
  attributes:
    label: Steps To Reproduce
    description: Add steps to reproduce this behaviour, include console / network logs & videos
    placeholder: |
      1. Go to '...'
      2. Click on '....'
      3. Scroll down to '....'
      4. See error
  validations:
    required: true
- type: input
  id: app
  attributes:
    label: Public Sample App
    description: "Share a public sample application that reproduces the bug using our mock databases if possible"
    placeholder: "https://app.appsmith.com/applications/61e022f1eb0501052b9fa205/pages/61e02308eb0501052b9fa20c"
  validations:
    required: false
- type: dropdown
  id: environment
  attributes:
    label: Environment
    description: "Environment where the issue is reproducible"
    options:
        - Production
        - Release
        - Deploy Preview
  validations:
    required: true
- type: dropdown
  id: severity
  attributes:
    label: Severity
    description: "The impact of the issue"
    options:
        - Low (Inconsistent UI/UX)
        - Medium (Frustrating UX)
        - High (Blocker to building or releasing)
        - Critical (Broken Production apps)
  validations:
    required: true
- type: input
  id: video
  attributes:
    label: Issue video log
    description: "Share a loom video recording of how the issue can be reporduced"
    placeholder: "https://www.loom.com/share/d54e04bc68e24798..."
  validations:
    required: false
- type: input
  id: version
  attributes:
    label: Version
    description: "The version of your Appsmith Instance"
    placeholder: "Cloud / Self Hosted - 1.6.6"
  validations:
    required: true
