{"name": "module-federation-plugin", "version": "0.8.4", "description": "Module federation for webpack@4", "main": "index.js", "directories": {"lib": "lib"}, "scripts": {"release": "release-it", "lint": "eslint --ignore-path .gitignore --ext .js,.ts --fix ."}, "repository": {"type": "git", "url": "git+https://github.com/CyanSalt/module-federation-plugin.git"}, "bugs": {"url": "https://github.com/CyanSalt/module-federation-plugin/issues"}, "homepage": "https://github.com/CyanSalt/module-federation-plugin#readme", "keywords": ["webpack", "federation"], "author": "CyanSalt", "license": "ISC", "devDependencies": {"@cyansalt/eslint-config-preset": "^1.0.2", "@release-it/conventional-changelog": "^3.3.0", "release-it": "^14.11.6", "webpack": "^4.46.0"}, "peerDependencies": {"webpack": ">= 4.0.0 < 5"}, "dependencies": {"schema-utils": "^3.1.1", "webpack-sources": "^1.4.3"}}