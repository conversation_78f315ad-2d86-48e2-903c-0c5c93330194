"use strict";(self.webpackChunkpageplug_datasource_frontend=self.webpackChunkpageplug_datasource_frontend||[]).push([[106,725],{241:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval("{/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   D: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* unused harmony export ThemeProvider */\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(6070);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(212);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(3017);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_2__);\n\r\n\r\n\r\nconst lightTheme = {\r\n    colors: {\r\n        primary: '#1890ff',\r\n        secondary: '#6c757d',\r\n        success: '#52c41a',\r\n        warning: '#faad14',\r\n        error: '#ff4d4f',\r\n        info: '#1890ff',\r\n        text: {\r\n            primary: '#262626',\r\n            secondary: '#595959',\r\n            disabled: '#bfbfbf'\r\n        },\r\n        background: {\r\n            primary: '#ffffff',\r\n            secondary: '#fafafa',\r\n            tertiary: '#f5f5f5'\r\n        },\r\n        border: {\r\n            primary: '#d9d9d9',\r\n            secondary: '#f0f0f0'\r\n        }\r\n    },\r\n    spacing: {\r\n        xs: '4px',\r\n        sm: '8px',\r\n        md: '16px',\r\n        lg: '24px',\r\n        xl: '32px',\r\n        xxl: '48px'\r\n    },\r\n    borderRadius: {\r\n        sm: '4px',\r\n        md: '6px',\r\n        lg: '8px'\r\n    },\r\n    shadows: {\r\n        sm: '0 2px 4px rgba(0, 0, 0, 0.06)',\r\n        md: '0 4px 12px rgba(0, 0, 0, 0.1)',\r\n        lg: '0 8px 24px rgba(0, 0, 0, 0.15)'\r\n    },\r\n    typography: {\r\n        fontFamily: \"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif\",\r\n        fontSize: {\r\n            xs: '12px',\r\n            sm: '14px',\r\n            md: '16px',\r\n            lg: '18px',\r\n            xl: '20px'\r\n        },\r\n        fontWeight: {\r\n            normal: 400,\r\n            medium: 500,\r\n            semibold: 600,\r\n            bold: 700\r\n        }\r\n    }\r\n};\r\nconst darkTheme = {\r\n    ...lightTheme,\r\n    colors: {\r\n        ...lightTheme.colors,\r\n        text: {\r\n            primary: '#ffffff',\r\n            secondary: '#d9d9d9',\r\n            disabled: '#595959'\r\n        },\r\n        background: {\r\n            primary: '#1f1f1f',\r\n            secondary: '#262626',\r\n            tertiary: '#141414'\r\n        },\r\n        border: {\r\n            primary: '#434343',\r\n            secondary: '#303030'\r\n        }\r\n    }\r\n};\r\nconst ThemeContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\r\nconst ThemeProvider = ({ children }) => {\r\n    const [isDark, setIsDark] = useState(() => {\r\n        const saved = localStorage.getItem('datasource-app-theme');\r\n        if (saved) {\r\n            return saved === 'dark';\r\n        }\r\n        return window.matchMedia('(prefers-color-scheme: dark)').matches;\r\n    });\r\n    const theme = isDark ? darkTheme : lightTheme;\r\n    const toggleTheme = () => {\r\n        setIsDark(prev => {\r\n            const newValue = !prev;\r\n            localStorage.setItem('datasource-app-theme', newValue ? 'dark' : 'light');\r\n            return newValue;\r\n        });\r\n    };\r\n    useEffect(() => {\r\n        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\r\n        const handleChange = (e) => {\r\n            const saved = localStorage.getItem('datasource-app-theme');\r\n            if (!saved) {\r\n                setIsDark(e.matches);\r\n            }\r\n        };\r\n        mediaQuery.addEventListener('change', handleChange);\r\n        return () => mediaQuery.removeEventListener('change', handleChange);\r\n    }, []);\r\n    useEffect(() => {\r\n        const root = document.documentElement;\r\n        if (isDark) {\r\n            root.classList.add('dark');\r\n        }\r\n        else {\r\n            root.classList.remove('dark');\r\n        }\r\n    }, [isDark]);\r\n    const contextValue = {\r\n        theme,\r\n        isDark,\r\n        toggleTheme\r\n    };\r\n    return (_jsx(ThemeContext.Provider, { value: contextValue, children: _jsx(StyledThemeProvider, { theme: theme, children: children }) }));\r\n};\r\nconst useTheme = () => {\r\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\r\n    if (context === undefined) {\r\n        throw new Error('useTheme must be used within a ThemeProvider');\r\n    }\r\n    return context;\r\n};\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///241\n\n}")},4106:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval('{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(6070);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(212);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(8076);\n/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(antd__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(6927);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(4702);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(3504);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(7348);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(5469);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(6025);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(6686);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(4332);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(9458);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(3017);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var _services_apiClient__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(8876);\n/* harmony import */ var _components_Common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(3128);\n/* harmony import */ var _hooks_useTheme__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(5869);\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nconst { Search } = antd__WEBPACK_IMPORTED_MODULE_2__.Input;\r\nconst { Option } = antd__WEBPACK_IMPORTED_MODULE_2__.Select;\r\nconst { Text, Paragraph, Title } = antd__WEBPACK_IMPORTED_MODULE_2__.Typography;\r\nconst PageContainer = (styled_components__WEBPACK_IMPORTED_MODULE_12___default().div) `\n  padding: 24px;\n`;\r\nconst PageHeader = (styled_components__WEBPACK_IMPORTED_MODULE_12___default().div) `\n  margin-bottom: 24px;\n`;\r\nconst PageTitle = (styled_components__WEBPACK_IMPORTED_MODULE_12___default().h1) `\n  font-size: 24px;\n  font-weight: 600;\n  color: ${props => props.$isDark ? \'#ffffff\' : \'#262626\'};\n  margin: 0 0 8px 0;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n`;\r\nconst FilterSection = (styled_components__WEBPACK_IMPORTED_MODULE_12___default().div) `\n  margin-bottom: 24px;\n  padding: 16px;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n`;\r\nconst PluginCard = styled_components__WEBPACK_IMPORTED_MODULE_12___default()((0,antd__WEBPACK_IMPORTED_MODULE_2__.Card)) `\n  height: 100%;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border: 2px solid transparent;\n  \n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);\n    border-color: #1890ff;\n  }\n  \n  .ant-card-head {\n    border-bottom: 1px solid #f0f0f0;\n    min-height: 60px;\n  }\n  \n  .ant-card-body {\n    height: 200px;\n    display: flex;\n    flex-direction: column;\n  }\n  \n  ${props => props.$status === \'deprecated\' && `\n    opacity: 0.6;\n    &:hover {\n      transform: none;\n      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n      border-color: transparent;\n    }\n  `}\n`;\r\nconst PluginIcon = (styled_components__WEBPACK_IMPORTED_MODULE_12___default().div) `\n  width: 48px;\n  height: 48px;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 24px;\n  color: white;\n  margin-bottom: 12px;\n  \n  ${props => {\r\n    switch (props.$category) {\r\n        case \'database\':\r\n            return \'background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);\';\r\n        case \'api\':\r\n            return \'background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);\';\r\n        case \'cloud\':\r\n            return \'background: linear-gradient(135deg, #722ed1 0%, #531dab 100%);\';\r\n        case \'ai\':\r\n            return \'background: linear-gradient(135deg, #fa8c16 0%, #d46b08 100%);\';\r\n        default:\r\n            return \'background: linear-gradient(135deg, #8c8c8c 0%, #595959 100%);\';\r\n    }\r\n}}\n`;\r\nconst PluginDescription = (styled_components__WEBPACK_IMPORTED_MODULE_12___default().div) `\n  flex: 1;\n  margin-bottom: 16px;\n`;\r\nconst PluginFooter = (styled_components__WEBPACK_IMPORTED_MODULE_12___default().div) `\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: auto;\n`;\r\nconst StatusTag = styled_components__WEBPACK_IMPORTED_MODULE_12___default()((0,antd__WEBPACK_IMPORTED_MODULE_2__.Tag)) `\n  ${props => {\r\n    switch (props.$status) {\r\n        case \'active\':\r\n            return `\n          background-color: #f6ffed;\n          border-color: #b7eb8f;\n          color: #52c41a;\n        `;\r\n        case \'inactive\':\r\n            return `\n          background-color: #fff7e6;\n          border-color: #ffd591;\n          color: #fa8c16;\n        `;\r\n        case \'deprecated\':\r\n            return `\n          background-color: #fff2f0;\n          border-color: #ffccc7;\n          color: #ff4d4f;\n        `;\r\n        default:\r\n            return \'\';\r\n    }\r\n}}\n`;\r\nconst PluginManagement = () => {\r\n    const { isDark } = (0,_hooks_useTheme__WEBPACK_IMPORTED_MODULE_15__/* .useTheme */ .D)();\r\n    const [plugins, setPlugins] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\r\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\r\n    const [searchText, setSearchText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\'\');\r\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\'\');\r\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\'\');\r\n    const [selectedPlugin, setSelectedPlugin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\r\n    const [detailModalVisible, setDetailModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\r\n    const loadPlugins = async () => {\r\n        try {\r\n            setLoading(true);\r\n            const response = await _services_apiClient__WEBPACK_IMPORTED_MODULE_13__/* .apiClient */ .u.get(\'/plugins\', {\r\n                params: {\r\n                    category: selectedCategory || undefined,\r\n                    status: selectedStatus || undefined\r\n                }\r\n            });\r\n            setPlugins(response.data);\r\n        }\r\n        catch (error) {\r\n            antd__WEBPACK_IMPORTED_MODULE_2__.message.error(\'加载插件列表失败\');\r\n            console.error(\'Load plugins error:\', error);\r\n        }\r\n        finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\r\n        loadPlugins();\r\n    }, [selectedCategory, selectedStatus]);\r\n    const getCategoryIcon = (category) => {\r\n        switch (category) {\r\n            case \'database\':\r\n                return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .A, {});\r\n            case \'api\':\r\n                return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A, {});\r\n            case \'cloud\':\r\n                return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A, {});\r\n            case \'ai\':\r\n                return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .A, {});\r\n            default:\r\n                return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A, {});\r\n        }\r\n    };\r\n    const getStatusIcon = (status) => {\r\n        switch (status) {\r\n            case \'active\':\r\n                return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A, { style: { color: \'#52c41a\' } });\r\n            case \'inactive\':\r\n                return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A, { style: { color: \'#fa8c16\' } });\r\n            case \'deprecated\':\r\n                return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A, { style: { color: \'#ff4d4f\' } });\r\n            default:\r\n                return null;\r\n        }\r\n    };\r\n    const filteredPlugins = plugins.filter(plugin => {\r\n        const matchesSearch = !searchText ||\r\n            plugin.name.toLowerCase().includes(searchText.toLowerCase()) ||\r\n            plugin.displayName.toLowerCase().includes(searchText.toLowerCase()) ||\r\n            plugin.description.toLowerCase().includes(searchText.toLowerCase());\r\n        return matchesSearch;\r\n    });\r\n    const stats = {\r\n        total: plugins.length,\r\n        active: plugins.filter(p => p.status === \'active\').length,\r\n        inactive: plugins.filter(p => p.status === \'inactive\').length,\r\n        deprecated: plugins.filter(p => p.status === \'deprecated\').length\r\n    };\r\n    const categoryStats = plugins.reduce((acc, plugin) => {\r\n        acc[plugin.category] = (acc[plugin.category] || 0) + 1;\r\n        return acc;\r\n    }, {});\r\n    const viewPluginDetail = (plugin) => {\r\n        setSelectedPlugin(plugin);\r\n        setDetailModalVisible(true);\r\n    };\r\n    if (loading) {\r\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_Common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_14__/* .LoadingSpinner */ .kt, { text: "\\u52A0\\u8F7D\\u63D2\\u4EF6\\u5217\\u8868\\u4E2D..." });\r\n    }\r\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(PageContainer, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(PageHeader, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(PageTitle, { "$isDark": isDark, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A, {}), "\\u63D2\\u4EF6\\u7BA1\\u7406"] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Text, { type: "secondary", children: "\\u7BA1\\u7406\\u548C\\u914D\\u7F6E\\u6570\\u636E\\u6E90\\u63D2\\u4EF6\\uFF0C\\u652F\\u6301\\u591A\\u79CD\\u6570\\u636E\\u5E93\\u548CAPI\\u670D\\u52A1\\u8FDE\\u63A5" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Row, { gutter: 16, style: { marginBottom: 24 }, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Col, { span: 6, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Card, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { style: { textAlign: \'center\' }, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { style: { fontSize: \'24px\', fontWeight: 600, color: \'#1890ff\' }, children: stats.total }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { style: { color: \'#8c8c8c\' }, children: "\\u603B\\u63D2\\u4EF6\\u6570" })] }) }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Col, { span: 6, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Card, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { style: { textAlign: \'center\' }, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { style: { fontSize: \'24px\', fontWeight: 600, color: \'#52c41a\' }, children: stats.active }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { style: { color: \'#8c8c8c\' }, children: "\\u6D3B\\u8DC3\\u63D2\\u4EF6" })] }) }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Col, { span: 6, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Card, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { style: { textAlign: \'center\' }, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { style: { fontSize: \'24px\', fontWeight: 600, color: \'#fa8c16\' }, children: stats.inactive }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { style: { color: \'#8c8c8c\' }, children: "\\u672A\\u6FC0\\u6D3B" })] }) }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Col, { span: 6, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Card, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { style: { textAlign: \'center\' }, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { style: { fontSize: \'24px\', fontWeight: 600, color: \'#ff4d4f\' }, children: stats.deprecated }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { style: { color: \'#8c8c8c\' }, children: "\\u5DF2\\u5E9F\\u5F03" })] }) }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(FilterSection, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Row, { gutter: 16, align: "middle", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Col, { span: 8, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Search, { placeholder: "\\u641C\\u7D22\\u63D2\\u4EF6\\u540D\\u79F0\\u6216\\u63CF\\u8FF0", allowClear: true, value: searchText, onChange: (e) => setSearchText(e.target.value), prefix: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .A, {}) }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Col, { span: 6, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Select, { placeholder: "\\u9009\\u62E9\\u5206\\u7C7B", allowClear: true, style: { width: \'100%\' }, value: selectedCategory, onChange: setSelectedCategory, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(Option, { value: "database", children: ["\\u6570\\u636E\\u5E93 (", categoryStats.database || 0, ")"] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(Option, { value: "api", children: ["API\\u670D\\u52A1 (", categoryStats.api || 0, ")"] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(Option, { value: "cloud", children: ["\\u4E91\\u670D\\u52A1 (", categoryStats.cloud || 0, ")"] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(Option, { value: "ai", children: ["AI\\u670D\\u52A1 (", categoryStats.ai || 0, ")"] })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Col, { span: 6, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Select, { placeholder: "\\u9009\\u62E9\\u72B6\\u6001", allowClear: true, style: { width: \'100%\' }, value: selectedStatus, onChange: setSelectedStatus, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(Option, { value: "active", children: ["\\u6D3B\\u8DC3 (", stats.active, ")"] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(Option, { value: "inactive", children: ["\\u672A\\u6FC0\\u6D3B (", stats.inactive, ")"] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(Option, { value: "deprecated", children: ["\\u5DF2\\u5E9F\\u5F03 (", stats.deprecated, ")"] })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Col, { span: 4, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(Text, { type: "secondary", children: ["\\u5171\\u627E\\u5230 ", filteredPlugins.length, " \\u4E2A\\u63D2\\u4EF6"] }) })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Row, { gutter: [16, 16], children: filteredPlugins.map(plugin => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Col, { xs: 24, sm: 12, md: 8, lg: 6, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(PluginCard, { "$status": plugin.status, onClick: () => viewPluginDetail(plugin), title: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Space, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: plugin.displayName }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Badge, { count: getStatusIcon(plugin.status), style: { backgroundColor: \'transparent\' } })] }), extra: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(StatusTag, { "$status": plugin.status, children: plugin.status === \'active\' ? \'活跃\' :\r\n                                plugin.status === \'inactive\' ? \'未激活\' : \'已废弃\' }), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(PluginIcon, { "$category": plugin.category, children: getCategoryIcon(plugin.category) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(PluginDescription, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Paragraph, { ellipsis: { rows: 3, tooltip: plugin.description }, style: { margin: 0, fontSize: \'14px\' }, children: plugin.description }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(PluginFooter, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Space, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Tag, { color: "blue", children: plugin.category }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(Text, { type: "secondary", style: { fontSize: \'12px\' }, children: ["v", plugin.version] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Tooltip, { title: "\\u67E5\\u770B\\u8BE6\\u60C5", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Button, { type: "text", size: "small", icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A, {}), onClick: (e) => {\r\n                                                e.stopPropagation();\r\n                                                viewPluginDetail(plugin);\r\n                                            } }) })] })] }) }, plugin.id))) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Modal, { title: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Space, { children: [selectedPlugin && getCategoryIcon(selectedPlugin.category), selectedPlugin?.displayName, (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(StatusTag, { "$status": selectedPlugin?.status || \'\', children: selectedPlugin?.status === \'active\' ? \'活跃\' :\r\n                                selectedPlugin?.status === \'inactive\' ? \'未激活\' : \'已废弃\' })] }), open: detailModalVisible, onCancel: () => setDetailModalVisible(false), footer: [\r\n                    (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Button, { onClick: () => setDetailModalVisible(false), children: "\\u5173\\u95ED" }, "close"),\r\n                    (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Button, { type: "primary", icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A, {}), onClick: () => {\r\n                            if (selectedPlugin?.documentationUrl) {\r\n                                window.open(selectedPlugin.documentationUrl, \'_blank\');\r\n                            }\r\n                        }, disabled: !selectedPlugin?.documentationUrl, children: "\\u67E5\\u770B\\u6587\\u6863" }, "docs")\r\n                ], width: 800, children: selectedPlugin && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Paragraph, { children: selectedPlugin.description }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Divider, { orientation: "left", children: "\\u57FA\\u672C\\u4FE1\\u606F" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Row, { gutter: 16, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Col, { span: 12, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Text, { strong: true, children: "\\u63D2\\u4EF6ID: " }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Text, { code: true, children: selectedPlugin.id })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Col, { span: 12, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Text, { strong: true, children: "\\u7248\\u672C: " }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Tag, { color: "blue", children: ["v", selectedPlugin.version] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Col, { span: 12, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Text, { strong: true, children: "\\u5206\\u7C7B: " }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Tag, { color: "green", children: selectedPlugin.category })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Col, { span: 12, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Text, { strong: true, children: "\\u652F\\u6301\\u64CD\\u4F5C: " }), selectedPlugin.supportedOperations.map(op => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Tag, { color: "orange", children: op }, op)))] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Divider, { orientation: "left", children: "\\u67E5\\u8BE2\\u6A21\\u677F" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Row, { gutter: 16, children: Object.entries(selectedPlugin.templates).map(([key, template]) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Col, { span: 24, style: { marginBottom: 8 }, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Card, { size: "small", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(Text, { strong: true, children: [key, ": "] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Text, { code: true, style: { fontSize: \'12px\' }, children: template })] }) }, key))) })] })) })] }));\r\n};\r\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PluginManagement);\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///4106\n\n}')},5869:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval("{/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   D: () => (/* reexport safe */ _providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_0__.D)\n/* harmony export */ });\n/* harmony import */ var _providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(241);\n\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTg2OS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQ3NEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGFnZXBsdWctZGF0YXNvdXJjZS1mcm9udGVuZC8uL3NyYy9ob29rcy91c2VUaGVtZS50cz8zZTk1Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIOmHjeaWsOWvvOWHulRoZW1lUHJvdmlkZXLkuK3nmoR1c2VUaGVtZSBob29rXG5leHBvcnQgeyB1c2VUaGVtZSB9IGZyb20gJy4uL3Byb3ZpZGVycy9UaGVtZVByb3ZpZGVyJztcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///5869\n\n}")}}]);