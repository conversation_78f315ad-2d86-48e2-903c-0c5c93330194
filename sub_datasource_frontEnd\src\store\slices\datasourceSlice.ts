import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { message } from 'antd';

import { 
  Datasource, 
  CreateDatasourceRequest, 
  UpdateDatasourceRequest,
  ConnectionTestResult,
  DatasourceStructure,
  PaginatedResponse
} from '../../types/api';
import { apiClient } from '../../services/apiClient';

// 异步thunk actions
export const fetchDatasources = createAsyncThunk(
  'datasource/fetchDatasources',
  async (params: {
    workspaceId?: string;
    pluginId?: string;
    page?: number;
    size?: number;
  } = {}) => {
    const response = await apiClient.get('/datasources', { params });
    return response.data;
  }
);

export const fetchDatasourceById = createAsyncThunk(
  'datasource/fetchDatasourceById',
  async (id: string) => {
    const response = await apiClient.get(`/datasources/${id}`);
    return response.data;
  }
);

export const createDatasource = createAsyncThunk(
  'datasource/createDatasource',
  async (request: CreateDatasourceRequest) => {
    const response = await apiClient.post('/datasources', request);
    message.success('数据源创建成功');
    return response.data;
  }
);

export const updateDatasource = createAsyncThunk(
  'datasource/updateDatasource',
  async ({ id, request }: { id: string; request: UpdateDatasourceRequest }) => {
    const response = await apiClient.put(`/datasources/${id}`, request);
    message.success('数据源更新成功');
    return response.data;
  }
);

export const deleteDatasource = createAsyncThunk(
  'datasource/deleteDatasource',
  async (id: string) => {
    await apiClient.delete(`/datasources/${id}`);
    message.success('数据源删除成功');
    return id;
  }
);

export const testDatasourceConnection = createAsyncThunk(
  'datasource/testConnection',
  async (id: string) => {
    const response = await apiClient.post(`/datasources/${id}/test`);
    return { id, result: response.data };
  }
);

export const fetchDatasourceStructure = createAsyncThunk(
  'datasource/fetchStructure',
  async (id: string) => {
    const response = await apiClient.get(`/datasources/${id}/structure`);
    return { id, structure: response.data };
  }
);

// 状态接口
interface DatasourceState {
  // 数据源列表
  datasources: Datasource[];
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };
  
  // 当前选中的数据源
  currentDatasource: Datasource | null;
  
  // 数据源结构
  structures: Record<string, DatasourceStructure>;
  
  // 连接测试结果
  testResults: Record<string, ConnectionTestResult>;
  
  // 加载状态
  loading: {
    list: boolean;
    detail: boolean;
    create: boolean;
    update: boolean;
    delete: boolean;
    test: boolean;
    structure: boolean;
  };
  
  // 错误信息
  error: string | null;
  
  // 过滤条件
  filters: {
    pluginId?: string;
    workspaceId?: string;
    searchText?: string;
  };
}

// 初始状态
const initialState: DatasourceState = {
  datasources: [],
  pagination: {
    current: 1,
    pageSize: 10,
    total: 0
  },
  currentDatasource: null,
  structures: {},
  testResults: {},
  loading: {
    list: false,
    detail: false,
    create: false,
    update: false,
    delete: false,
    test: false,
    structure: false
  },
  error: null,
  filters: {}
};

// 创建slice
const datasourceSlice = createSlice({
  name: 'datasource',
  initialState,
  reducers: {
    // 设置过滤条件
    setFilters: (state, action: PayloadAction<Partial<DatasourceState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    
    // 清除错误
    clearError: (state) => {
      state.error = null;
    },
    
    // 设置当前数据源
    setCurrentDatasource: (state, action: PayloadAction<Datasource | null>) => {
      state.currentDatasource = action.payload;
    },
    
    // 清除测试结果
    clearTestResult: (state, action: PayloadAction<string>) => {
      delete state.testResults[action.payload];
    },
    
    // 清除结构数据
    clearStructure: (state, action: PayloadAction<string>) => {
      delete state.structures[action.payload];
    }
  },
  extraReducers: (builder) => {
    // 获取数据源列表
    builder
      .addCase(fetchDatasources.pending, (state) => {
        state.loading.list = true;
        state.error = null;
      })
      .addCase(fetchDatasources.fulfilled, (state, action: PayloadAction<PaginatedResponse<Datasource>>) => {
        state.loading.list = false;
        state.datasources = action.payload.content;
        state.pagination = {
          current: action.payload.pagination.page,
          pageSize: action.payload.pagination.size,
          total: action.payload.pagination.total
        };
      })
      .addCase(fetchDatasources.rejected, (state, action) => {
        state.loading.list = false;
        state.error = action.error.message || '获取数据源列表失败';
      });

    // 获取数据源详情
    builder
      .addCase(fetchDatasourceById.pending, (state) => {
        state.loading.detail = true;
        state.error = null;
      })
      .addCase(fetchDatasourceById.fulfilled, (state, action: PayloadAction<Datasource>) => {
        state.loading.detail = false;
        state.currentDatasource = action.payload;
        
        // 更新列表中的数据源
        const index = state.datasources.findIndex(ds => ds.id === action.payload.id);
        if (index !== -1) {
          state.datasources[index] = action.payload;
        }
      })
      .addCase(fetchDatasourceById.rejected, (state, action) => {
        state.loading.detail = false;
        state.error = action.error.message || '获取数据源详情失败';
      });

    // 创建数据源
    builder
      .addCase(createDatasource.pending, (state) => {
        state.loading.create = true;
        state.error = null;
      })
      .addCase(createDatasource.fulfilled, (state, action: PayloadAction<Datasource>) => {
        state.loading.create = false;
        state.datasources.unshift(action.payload);
        state.pagination.total += 1;
      })
      .addCase(createDatasource.rejected, (state, action) => {
        state.loading.create = false;
        state.error = action.error.message || '创建数据源失败';
      });

    // 更新数据源
    builder
      .addCase(updateDatasource.pending, (state) => {
        state.loading.update = true;
        state.error = null;
      })
      .addCase(updateDatasource.fulfilled, (state, action: PayloadAction<Datasource>) => {
        state.loading.update = false;
        
        // 更新列表中的数据源
        const index = state.datasources.findIndex(ds => ds.id === action.payload.id);
        if (index !== -1) {
          state.datasources[index] = action.payload;
        }
        
        // 更新当前数据源
        if (state.currentDatasource?.id === action.payload.id) {
          state.currentDatasource = action.payload;
        }
      })
      .addCase(updateDatasource.rejected, (state, action) => {
        state.loading.update = false;
        state.error = action.error.message || '更新数据源失败';
      });

    // 删除数据源
    builder
      .addCase(deleteDatasource.pending, (state) => {
        state.loading.delete = true;
        state.error = null;
      })
      .addCase(deleteDatasource.fulfilled, (state, action: PayloadAction<string>) => {
        state.loading.delete = false;
        state.datasources = state.datasources.filter(ds => ds.id !== action.payload);
        state.pagination.total -= 1;
        
        // 清除相关数据
        if (state.currentDatasource?.id === action.payload) {
          state.currentDatasource = null;
        }
        delete state.testResults[action.payload];
        delete state.structures[action.payload];
      })
      .addCase(deleteDatasource.rejected, (state, action) => {
        state.loading.delete = false;
        state.error = action.error.message || '删除数据源失败';
      });

    // 测试连接
    builder
      .addCase(testDatasourceConnection.pending, (state) => {
        state.loading.test = true;
        state.error = null;
      })
      .addCase(testDatasourceConnection.fulfilled, (state, action) => {
        state.loading.test = false;
        state.testResults[action.payload.id] = action.payload.result;
      })
      .addCase(testDatasourceConnection.rejected, (state, action) => {
        state.loading.test = false;
        state.error = action.error.message || '连接测试失败';
      });

    // 获取数据源结构
    builder
      .addCase(fetchDatasourceStructure.pending, (state) => {
        state.loading.structure = true;
        state.error = null;
      })
      .addCase(fetchDatasourceStructure.fulfilled, (state, action) => {
        state.loading.structure = false;
        state.structures[action.payload.id] = action.payload.structure;
      })
      .addCase(fetchDatasourceStructure.rejected, (state, action) => {
        state.loading.structure = false;
        state.error = action.error.message || '获取数据源结构失败';
      });
  }
});

// 导出actions
export const {
  setFilters,
  clearError,
  setCurrentDatasource,
  clearTestResult,
  clearStructure
} = datasourceSlice.actions;

// 导出reducer
export default datasourceSlice.reducer;

// 选择器
export const selectDatasources = (state: { datasource: DatasourceState }) => state.datasource.datasources;
export const selectCurrentDatasource = (state: { datasource: DatasourceState }) => state.datasource.currentDatasource;
export const selectDatasourceLoading = (state: { datasource: DatasourceState }) => state.datasource.loading;
export const selectDatasourcePagination = (state: { datasource: DatasourceState }) => state.datasource.pagination;
export const selectDatasourceFilters = (state: { datasource: DatasourceState }) => state.datasource.filters;
export const selectTestResult = (id: string) => (state: { datasource: DatasourceState }) => state.datasource.testResults[id];
export const selectStructure = (id: string) => (state: { datasource: DatasourceState }) => state.datasource.structures[id];
