on:
  #This workflow is only triggered by the ok to test command dispatch
  repository_dispatch:
    types: [ci-merge-check-command]

jobs:
  ci-merge-check:
    runs-on: ubuntu-latest
    steps:
      # This step creates a comment on the PR with a link to this workflow run.
      - name: Add a comment on the PR with link to workflow run
        uses: peter-evans/create-or-update-comment@v3
        with:
          issue-number: ${{ github.event.client_payload.pull_request.number }}
          body: |
            Merge check is running at: <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}>
            PR: ${{ github.event.client_payload.pull_request.number }}

      - name: Find the latest workflow run comment
        uses: peter-evans/find-comment@v2
        id: find-comment
        with:
          issue-number: ${{ github.event.client_payload.pull_request.number }}
          body-includes: "Appsmith External Integration Test Workflow"
          comment-author: github-actions[bot]
          direction: last

      - name: Get the workflow run_id
        id: workflow
        run: |
          echo "run_id=$(echo '${{ steps.find-comment.outputs.comment-body }}' | grep -o 'runs/[0-9]\+' | cut -d/ -f2)" >> $GITHUB_OUTPUT

      - name: Get ci-test-result status from the run_id
        id: ci_test_result_status
        run: |
          run_status=`curl -s -H "Authorization: Bearer ${{ secrets.GITHUB_TOKEN }}" https://api.github.com/repos/${{github.repository}}/actions/runs/${{steps.workflow.outputs.run_id}}/jobs?per_page=100`
          echo "ci_test_result=`echo $run_status | jq -r '[ .jobs[] | select (select(.name | contains("ci-test-result")) | .conclusion | contains("failure"))] | length'`" >> $GITHUB_OUTPUT                                    
          echo "ci_test_status=`echo $run_status | jq -r '[ .jobs[] | select (select(.name | contains("ci-test ")) | .conclusion | contains("failure"))] | length'`" >> $GITHUB_OUTPUT
          echo "perf_test_status=`echo $run_status | jq -r '[ .jobs[] | select (select(.name | contains("perf-test")) | .conclusion | contains("failure"))] | length'`" >> $GITHUB_OUTPUT

      - name: get status-checks from pr
        id: pr_status_check
        run: |
          status_link="https://api.github.com/repos/${{github.repository}}/statuses/${{github.event.client_payload.pull_request.head.sha}}"
          echo "merge_freeze_status=`curl -s -H "Authorization: Bearer ${{ secrets.GITHUB_TOKEN }}" $status_link | jq -r '[.[] | select( select( .context | contains("mergefreeze")) | .state | contains("success") | not)] | length'`" >> $GITHUB_OUTPUT

      - name: get status-checks from check suite
        id: suite_status_check
        run: |
          check_runs_link="https://api.github.com/repos/${{github.repository}}/commits/${{github.event.client_payload.pull_request.head.sha}}/check-runs"
          check_run_result=`curl -s -H "Authorization: Bearer ${{ secrets.GITHUB_TOKEN }}" $check_runs_link`
          echo "test_plan_approval_status=`echo $check_run_result | jq -r '[[.check_runs[]|select(.name | contains("Test Plan Approved"))][0] | select( .conclusion | contains("success") | not)] | length'`" >> $GITHUB_OUTPUT
          echo "ci_test_available=`echo $check_run_result | jq -r '[.check_runs[] | select(.name | contains("ci-test-result"))] | length'`" >> $GITHUB_OUTPUT

      - name: get pr approval status
        id: pr_approval_status
        run: |
          pr_reviews_link="https://api.github.com/repos/${{github.repository}}/pulls/${{github.event.client_payload.pull_request.number}}/reviews"
          pr_reviews=`curl -s -H "Authorization: Bearer ${{ secrets.GITHUB_TOKEN }}" $pr_reviews_link`
          echo "approved_pr_reviews=`echo $pr_reviews | jq -r 'if length > 0 then [.[]|select(.state | contains ("APPROVED"))]|length else 0 end'`" >> $GITHUB_OUTPUT

      - name: Verify all the checks
        id: verify_checks
        run: |
          if [ ${{ steps.ci_test_result_status.outputs.ci_test_result }} == 1 ]; then
            if [ ${{ steps.ci_test_result_status.outputs.ci_test_status }} -gt 0 ]; then
              echo "msg=Looks like there are some Cypress failures. Can't Merge." >> $GITHUB_OUTPUT
              exit 1
            elif [ ${{ steps.ci_test_result_status.outputs.perf_test_status }} -gt 0 ]; then
              echo "msg=Looks like Perf-Tests failed. Contact perf team to get their confirmation. Can't Merge." >> $GITHUB_OUTPUT
              exit 1
            fi
          elif [ ${{ steps.suite_status_check.outputs.ci_test_available }} == 0 ]; then
            echo "msg=Looks like you forgot to run /ok-to-test on the latest commit. Can't Merge." >> $GITHUB_OUTPUT
            exit 1
          elif [ ${{ steps.pr_status_check.outputs.merge_freeze_status }} != 0 ]; then
            echo "msg=There's merge freeze. Can't Merge, please try after merge-freeze is lifted." >> $GITHUB_OUTPUT
            exit 1
          elif [ ${{ steps.suite_status_check.outputs.test_plan_approval_status }} == 1 ]; then
            echo "msg=Either get 'Test Plan Approved' or 'skip-testPlan' added and try again!" >> $GITHUB_OUTPUT
            exit 1
          elif [ ${{ steps.pr_approval_status.outputs.approved_pr_reviews }} == 0 ]; then
            echo "msg=Minimum 1 approval needed to merge this PR, please get it reviewed by your peer and try again!" >> $GITHUB_OUTPUT
            exit 1
          elif [ ${{ steps.suite_status_check.outputs.test_plan_approval_status }} == 0 ]; then
            echo "msg=Hurray!🎉 Proceeding to Merge!!!" >> $GITHUB_OUTPUT
            exit 0
          else
            echo "msg=Some checks have failed. Can't merge." >> $GITHUB_OUTPUT
            exit 1
          fi

      - name: Add a comment on the PR after all checks
        if: always()
        uses: peter-evans/create-or-update-comment@v3
        with:
          issue-number: ${{ github.event.client_payload.pull_request.number }}
          body: |
            ${{steps.verify_checks.outputs.msg}}

      - name: Auto merge if above checks are success and have 1 approval
        if: success()
        id: automerge
        uses: juliangruber/merge-pull-request-action@v1
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          number: ${{ github.event.client_payload.pull_request.number }}
          method: merge
          repo: ${{ github.repository }}

      - run: |
          echo ${{ steps.automerge.outputs }}
