name: "Build Storybook - Docs with Chromatic"

on:
  push:
    branches:
      - release
    paths:
      - "app/client/packages/design-system/**"
      - "app/client/packages/storybook/**"
  pull_request:
    paths:
      - "app/client/packages/design-system/**"
      - "app/client/packages/storybook/**"

jobs:
  chromatic-deployment:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout PR if pull_request event
        if: github.event_name == 'pull_request'
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: refs/pull/${{ github.event.pull_request.number }}/merge

      - name: Checkout PR if push event
        if: github.event_name == 'push'
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: release

      - name: Use Node.js
        uses: actions/setup-node@v3
        with:
          node-version-file: app/client/package.json

      - name: Install Dependencies
        working-directory: ./app/client/packages/storybook
        run: yarn install --immutable

      - name: Publish to Chromatic
        id: chromatic-publish
        uses: chromaui/action@v1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          projectToken: ${{ secrets.STORYBOOK_PROJECT_TOKEN }}
          workingDir: ./app/client/packages/storybook
          exitOnceUploaded: true
          buildScriptName: "build"
