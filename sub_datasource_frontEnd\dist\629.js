"use strict";(self.webpackChunkpageplug_datasource_frontend=self.webpackChunkpageplug_datasource_frontend||[]).push([[629],{2629:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval('{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(6070);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(212);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(8076);\n/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(antd__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(2691);\n\r\n\r\n\r\n\r\nconst AdvancedQueryEditor = ({ value, onChange }) => {\r\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Card, { title: "\\u9AD8\\u7EA7\\u67E5\\u8BE2\\u7F16\\u8F91\\u5668", extra: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A, {}), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Empty, { description: "\\u9AD8\\u7EA7\\u67E5\\u8BE2\\u7F16\\u8F91\\u5668\\u5F00\\u53D1\\u4E2D...", image: antd__WEBPACK_IMPORTED_MODULE_2__.Empty.PRESENTED_IMAGE_SIMPLE }) }));\r\n};\r\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AdvancedQueryEditor);\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMjYyOS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUEwQjtBQUNTO0FBQ2M7QUFPakQsTUFBTSxtQkFBbUIsR0FBdUMsQ0FBQyxFQUFFLEtBQUssRUFBRSxRQUFRLEVBQUUsRUFBRSxFQUFFO0lBQ3RGLE9BQU8sQ0FDTCx1REFBQyxzQ0FBSSxJQUFDLEtBQUssRUFBQyw0Q0FBUyxFQUFDLEtBQUssRUFBRSx1REFBQyxrRUFBWSxLQUFHLFlBQzNDLHVEQUFDLHVDQUFLLElBQ0osV0FBVyxFQUFDLGlFQUFlLEVBQzNCLEtBQUssRUFBRSx1Q0FBSyxDQUFDLHNCQUFzQixHQUNuQyxHQUNHLENBQ1IsQ0FBQztBQUNKLENBQUMsQ0FBQztBQUVGLGlFQUFlLG1CQUFtQixFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGFnZXBsdWctZGF0YXNvdXJjZS1mcm9udGVuZC8uL3NyYy9jb21wb25lbnRzL0FkdmFuY2VkUXVlcnlFZGl0b3IvaW5kZXgudHN4PzIyNDYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IENhcmQsIEVtcHR5IH0gZnJvbSAnYW50ZCc7XG5pbXBvcnQgeyBDb2RlT3V0bGluZWQgfSBmcm9tICdAYW50LWRlc2lnbi9pY29ucyc7XG5cbmludGVyZmFjZSBBZHZhbmNlZFF1ZXJ5RWRpdG9yUHJvcHMge1xuICB2YWx1ZT86IHN0cmluZztcbiAgb25DaGFuZ2U/OiAodmFsdWU6IHN0cmluZykgPT4gdm9pZDtcbn1cblxuY29uc3QgQWR2YW5jZWRRdWVyeUVkaXRvcjogUmVhY3QuRkM8QWR2YW5jZWRRdWVyeUVkaXRvclByb3BzPiA9ICh7IHZhbHVlLCBvbkNoYW5nZSB9KSA9PiB7XG4gIHJldHVybiAoXG4gICAgPENhcmQgdGl0bGU9XCLpq5jnuqfmn6Xor6LnvJbovpHlmahcIiBleHRyYT17PENvZGVPdXRsaW5lZCAvPn0+XG4gICAgICA8RW1wdHlcbiAgICAgICAgZGVzY3JpcHRpb249XCLpq5jnuqfmn6Xor6LnvJbovpHlmajlvIDlj5HkuK0uLi5cIlxuICAgICAgICBpbWFnZT17RW1wdHkuUFJFU0VOVEVEX0lNQUdFX1NJTVBMRX1cbiAgICAgIC8+XG4gICAgPC9DYXJkPlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgQWR2YW5jZWRRdWVyeUVkaXRvcjtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///2629\n\n}')}}]);