import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Tag,
  Tooltip,
  Input,
  Select,
  Card,
  Row,
  Col,
  Dropdown,
  Modal,
  message
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  EditOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  MoreOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  DatabaseOutlined
} from '@ant-design/icons';
import type { ColumnsType, TableProps } from 'antd/es/table';
import type { MenuProps } from 'antd';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import dayjs from 'dayjs';

import { Datasource, Plugin } from '../../types/api';
import { apiClient } from '../../services/apiClient';
import { LoadingSpinner } from '../../components/Common/LoadingSpinner';
import { useTheme } from '../../hooks/useTheme';

const { Search } = Input;
const { Option } = Select;
const { confirm } = Modal;

// 样式化组件
const PageContainer = styled.div`
  padding: 24px;
`;

const PageHeader = styled.div`
  margin-bottom: 24px;
`;

const PageTitle = styled.h1<{ $isDark: boolean }>`
  font-size: 24px;
  font-weight: 600;
  color: ${props => props.$isDark ? '#ffffff' : '#262626'};
  margin: 0 0 8px 0;
`;

const PageDescription = styled.p<{ $isDark: boolean }>`
  color: ${props => props.$isDark ? '#d9d9d9' : '#8c8c8c'};
  margin: 0 0 16px 0;
`;

const FilterSection = styled.div`
  margin-bottom: 16px;
`;

const StatsCard = styled(Card)`
  .ant-card-body {
    padding: 16px;
  }
`;

const StatsNumber = styled.div<{ $color: string }>`
  font-size: 24px;
  font-weight: 600;
  color: ${props => props.$color};
  margin-bottom: 4px;
`;

const StatsLabel = styled.div<{ $isDark: boolean }>`
  font-size: 14px;
  color: ${props => props.$isDark ? '#d9d9d9' : '#8c8c8c'};
`;

const StatusTag = styled(Tag)<{ $status: 'valid' | 'invalid' | 'unknown' }>`
  ${props => {
    switch (props.$status) {
      case 'valid':
        return `
          background-color: #f6ffed;
          border-color: #b7eb8f;
          color: #52c41a;
        `;
      case 'invalid':
        return `
          background-color: #fff2f0;
          border-color: #ffccc7;
          color: #ff4d4f;
        `;
      default:
        return `
          background-color: #fafafa;
          border-color: #d9d9d9;
          color: #8c8c8c;
        `;
    }
  }}
`;

interface DatasourceListProps {}

const DatasourceList: React.FC<DatasourceListProps> = () => {
  const [datasources, setDatasources] = useState<Datasource[]>([]);
  const [plugins, setPlugins] = useState<Plugin[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [selectedPlugin, setSelectedPlugin] = useState<string>('');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });

  const navigate = useNavigate();
  const { isDark } = useTheme();

  // 加载数据源列表
  const loadDatasources = async (page = 1, size = 10) => {
    try {
      setLoading(true);
      const response = await apiClient.get('/datasources', {
        params: {
          page,
          size,
          pluginId: selectedPlugin || undefined,
          workspaceId: 'workspace-123' // 固定工作空间ID
        }
      });

      setDatasources(response.data.content);
      setPagination({
        current: page,
        pageSize: size,
        total: response.data.pagination.total
      });
    } catch (error) {
      message.error('加载数据源列表失败');
      console.error('Load datasources error:', error);
    } finally {
      setLoading(false);
    }
  };

  // 加载插件列表
  const loadPlugins = async () => {
    try {
      const response = await apiClient.get('/plugins', {
        params: { status: 'active' }
      });
      setPlugins(response.data);
    } catch (error) {
      console.error('Load plugins error:', error);
    }
  };

  // 初始化数据
  useEffect(() => {
    loadDatasources();
    loadPlugins();
  }, [selectedPlugin]);

  // 删除数据源
  const handleDelete = (datasource: Datasource) => {
    confirm({
      title: '确认删除',
      content: `确定要删除数据源 "${datasource.name}" 吗？此操作不可恢复。`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await apiClient.delete(`/datasources/${datasource.id}`);
          message.success('数据源删除成功');
          loadDatasources(pagination.current, pagination.pageSize);
        } catch (error) {
          message.error('删除数据源失败');
        }
      }
    });
  };

  // 测试连接
  const handleTestConnection = async (datasource: Datasource) => {
    try {
      message.loading({ content: '正在测试连接...', key: 'test-connection' });
      const response = await apiClient.post(`/datasources/${datasource.id}/test`);
      
      if (response.data.success) {
        message.success({
          content: `连接成功 (${response.data.responseTime}ms)`,
          key: 'test-connection'
        });
      } else {
        message.error({
          content: `连接失败: ${response.data.message}`,
          key: 'test-connection'
        });
      }
    } catch (error) {
      message.error({
        content: '连接测试失败',
        key: 'test-connection'
      });
    }
  };

  // 表格列定义
  const columns: ColumnsType<Datasource> = [
    {
      title: '数据源名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (text, record) => (
        <Space>
          <DatabaseOutlined style={{ color: '#1890ff' }} />
          <span
            style={{ cursor: 'pointer', color: '#1890ff' }}
            onClick={() => navigate(`/datasources/${record.id}`)}
          >
            {text}
          </span>
        </Space>
      ),
      filteredValue: searchText ? [searchText] : null,
      onFilter: (value, record) =>
        record.name.toLowerCase().includes(value.toString().toLowerCase())
    },
    {
      title: '插件类型',
      dataIndex: 'pluginName',
      key: 'pluginName',
      width: 120,
      render: (text, record) => {
        const plugin = plugins.find(p => p.id === record.pluginId);
        return (
          <Tag color="blue">
            {plugin?.displayName || text}
          </Tag>
        );
      }
    },
    {
      title: '状态',
      dataIndex: 'isValid',
      key: 'status',
      width: 100,
      render: (isValid, record) => (
        <StatusTag
          $status={record.isConfigured ? (isValid ? 'valid' : 'invalid') : 'unknown'}
          icon={
            record.isConfigured
              ? isValid
                ? <CheckCircleOutlined />
                : <ExclamationCircleOutlined />
              : undefined
          }
        >
          {record.isConfigured ? (isValid ? '正常' : '异常') : '未配置'}
        </StatusTag>
      )
    },
    {
      title: '最后使用',
      dataIndex: 'lastUsed',
      key: 'lastUsed',
      width: 150,
      render: (text) => text ? dayjs(text).format('YYYY-MM-DD HH:mm') : '-'
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record) => {
        const menuItems: MenuProps['items'] = [
          {
            key: 'edit',
            icon: <EditOutlined />,
            label: '编辑',
            onClick: () => navigate(`/datasources/${record.id}/edit`)
          },
          {
            key: 'test',
            icon: <PlayCircleOutlined />,
            label: '测试连接',
            onClick: () => handleTestConnection(record)
          },
          {
            type: 'divider'
          },
          {
            key: 'delete',
            icon: <DeleteOutlined />,
            label: '删除',
            danger: true,
            onClick: () => handleDelete(record)
          }
        ];

        return (
          <Space>
            <Button
              type="link"
              size="small"
              onClick={() => navigate(`/datasources/${record.id}`)}
            >
              查看
            </Button>
            <Dropdown menu={{ items: menuItems }} trigger={['click']}>
              <Button type="text" size="small" icon={<MoreOutlined />} />
            </Dropdown>
          </Space>
        );
      }
    }
  ];

  // 表格变化处理
  const handleTableChange: TableProps<Datasource>['onChange'] = (paginationConfig) => {
    if (paginationConfig.current && paginationConfig.pageSize) {
      loadDatasources(paginationConfig.current, paginationConfig.pageSize);
    }
  };

  // 统计数据
  const stats = {
    total: datasources.length,
    valid: datasources.filter(ds => ds.isValid).length,
    invalid: datasources.filter(ds => !ds.isValid).length,
    unconfigured: datasources.filter(ds => !ds.isConfigured).length
  };

  return (
    <PageContainer>
      <PageHeader>
        <PageTitle $isDark={isDark}>数据源管理</PageTitle>
        <PageDescription $isDark={isDark}>
          管理和配置应用程序的数据源连接，支持多种数据库和API服务
        </PageDescription>

        {/* 统计卡片 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <StatsCard>
              <StatsNumber $color="#1890ff">{stats.total}</StatsNumber>
              <StatsLabel $isDark={isDark}>总数据源</StatsLabel>
            </StatsCard>
          </Col>
          <Col span={6}>
            <StatsCard>
              <StatsNumber $color="#52c41a">{stats.valid}</StatsNumber>
              <StatsLabel $isDark={isDark}>正常连接</StatsLabel>
            </StatsCard>
          </Col>
          <Col span={6}>
            <StatsCard>
              <StatsNumber $color="#ff4d4f">{stats.invalid}</StatsNumber>
              <StatsLabel $isDark={isDark}>连接异常</StatsLabel>
            </StatsCard>
          </Col>
          <Col span={6}>
            <StatsCard>
              <StatsNumber $color="#faad14">{stats.unconfigured}</StatsNumber>
              <StatsLabel $isDark={isDark}>未配置</StatsLabel>
            </StatsCard>
          </Col>
        </Row>

        {/* 操作栏 */}
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => navigate('/datasources/create')}
              >
                新建数据源
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => loadDatasources(pagination.current, pagination.pageSize)}
              >
                刷新
              </Button>
            </Space>
          </Col>
          <Col>
            <FilterSection>
              <Space>
                <Search
                  placeholder="搜索数据源名称"
                  allowClear
                  style={{ width: 200 }}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  prefix={<SearchOutlined />}
                />
                <Select
                  placeholder="选择插件类型"
                  allowClear
                  style={{ width: 150 }}
                  value={selectedPlugin}
                  onChange={setSelectedPlugin}
                >
                  {plugins.map(plugin => (
                    <Option key={plugin.id} value={plugin.id}>
                      {plugin.displayName}
                    </Option>
                  ))}
                </Select>
              </Space>
            </FilterSection>
          </Col>
        </Row>
      </PageHeader>

      {/* 数据表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={datasources}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条数据`
          }}
          onChange={handleTableChange}
          scroll={{ x: 800 }}
        />
      </Card>
    </PageContainer>
  );
};

export default DatasourceList;
