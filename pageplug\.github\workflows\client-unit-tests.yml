name: Client Unit Tests

on:
  workflow_call:
    inputs:
      pr:
        description: "This is the PR number in case the workflow is being called in a pull request"
        required: false
        type: number
      skip-tests:
        description: "This is a boolean value in case the workflow is being called in build deploy-preview"
        required: false
        type: string
        default: "false"
      branch:
        description: "This is the branch to be used for the build."
        required: false
        type: string

# Change the working directory for all the jobs in this workflow
defaults:
  run:
    working-directory: app/client

jobs:
  client-unit-tests:
    runs-on: ubuntu-latest-8-cores
    defaults:
      run:
        working-directory: app/client
        shell: bash

    steps:
      # The checkout steps MUST happen first because the default directory is set according to the code base.
      # GitHub Action expects all future commands to be executed in the code directory. Hence, we need to check out
      # the code before doing anything else.

      # Check out merge commit with the base branch in case this workflow is invoked via pull request
      - name: Checkout the merged commit from PR and base branch
        uses: actions/checkout@v4
        if: inputs.pr != 0
        with:
          ref: refs/pull/${{ inputs.pr }}/merge

      # Check out the specified branch in case this workflow is called by another workflow
      - name: Checkout the specified branch
        if: inputs.pr == 0 && inputs.branch != ''
        uses: actions/checkout@v4
        with:
          fetch-tags: true
          ref: ${{ inputs.branch }}

      # Checkout the code in the current branch in case the workflow is called because of a branch push event
      - name: Check out the head commit of the branch
        uses: actions/checkout@v4
        if: inputs.pr == 0 && inputs.branch == ''
        with:
          fetch-tags: true

      # In case this is second attempt try restoring status of the prior attempt from cache
      - name: Restore the previous run result
        uses: actions/cache@v3
        with:
          path: |
            ~/run_result
          key: ${{ github.run_id }}-${{ github.job }}-client

      # Fetch prior run result
      - name: Get the previous run result
        id: run_result
        run: cat ~/run_result 2>/dev/null || echo 'default'

      # In case of prior failure run the job
      - if: steps.run_result.outputs.run_result != 'success'
        run: echo "I'm alive!" && exit 0

      - name: Use Node.js
        if: steps.run_result.outputs.run_result != 'success'
        uses: actions/setup-node@v3
        with:
          node-version-file: app/client/package.json

      # actions/setup-node@v3 doesn’t work properly with Yarn 3
      # when the project lives in a subdirectory: https://github.com/actions/setup-node/issues/488
      # Restoring the cache manually instead
      - name: Restore Yarn cache
        if: steps.run_result.outputs.run_result != 'success'
        uses: actions/cache@v3
        with:
          path: app/client/.yarn/cache
          key: v1-yarn3-${{ hashFiles('app/client/yarn.lock') }}

      # Install all the dependencies
      - name: Install dependencies
        if: steps.run_result.outputs.run_result != 'success'
        run: yarn install --immutable

      # Run the Unit tests for client and packages
      - name: Run the jest tests
        if: steps.run_result.outputs.run_result != 'success'
        run: yarn run test:unit:ci

      # Set status = success
      - name: Save the status of the run
        run: echo "run_result=success" >> $GITHUB_OUTPUT > ~/run_result
