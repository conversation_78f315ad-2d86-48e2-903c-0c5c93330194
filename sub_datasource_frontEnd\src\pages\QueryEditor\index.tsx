import React, { useState, useEffect } from 'react';
import {
  Layout,
  Card,
  Button,
  Select,
  Input,
  Table,
  Tabs,
  Space,
  Tooltip,
  message,
  Modal,
  Form,
  Tag,
  Divider,
  Typography
} from 'antd';
import {
  PlayCircleOutlined,
  SaveOutlined,
  HistoryOutlined,
  DatabaseOutlined,
  TableOutlined,
  CodeOutlined,
  DownloadOutlined,
  FullscreenOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useParams } from 'react-router-dom';
import styled from 'styled-components';

import { Datasource, QueryExecutionResult, Query } from '../../types/api';
import { apiClient } from '../../services/apiClient';
import { LoadingSpinner } from '../../components/Common/LoadingSpinner';
import { useTheme } from '../../hooks/useTheme';

const { Sider, Content } = Layout;
const { Option } = Select;
const { TextArea } = Input;
const { TabPane } = Tabs;
const { Text } = Typography;

// 样式化组件
const QueryEditorContainer = styled.div`
  height: calc(100vh - 64px);
  display: flex;
  flex-direction: column;
`;

const EditorHeader = styled.div`
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: white;
`;

const EditorLayout = styled(Layout)`
  flex: 1;
  background: white;
`;

const EditorSider = styled(Sider)<{ $isDark: boolean }>`
  background: ${props => props.$isDark ? '#1f1f1f' : '#fafafa'} !important;
  border-right: 1px solid ${props => props.$isDark ? '#303030' : '#f0f0f0'};
`;

const EditorContent = styled(Content)`
  display: flex;
  flex-direction: column;
`;

const QueryInputArea = styled.div`
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
`;

const QueryTextArea = styled(TextArea)`
  font-family: 'JetBrains Mono', 'Consolas', monospace;
  font-size: 14px;
  line-height: 1.5;
`;

const ResultArea = styled.div`
  flex: 1;
  padding: 16px;
  overflow: auto;
`;

const ExecutionInfo = styled.div<{ $isDark: boolean }>`
  padding: 8px 16px;
  background: ${props => props.$isDark ? '#262626' : '#f5f5f5'};
  border-bottom: 1px solid ${props => props.$isDark ? '#303030' : '#e8e8e8'};
  font-size: 12px;
  color: ${props => props.$isDark ? '#d9d9d9' : '#666'};
`;

interface QueryEditorProps {}

const QueryEditor: React.FC<QueryEditorProps> = () => {
  const { datasourceId } = useParams<{ datasourceId: string }>();
  const { isDark } = useTheme();

  const [datasources, setDatasources] = useState<Datasource[]>([]);
  const [selectedDatasource, setSelectedDatasource] = useState<string>(datasourceId || '');
  const [queryText, setQueryText] = useState('');
  const [executing, setExecuting] = useState(false);
  const [executionResult, setExecutionResult] = useState<QueryExecutionResult | null>(null);
  const [savedQueries, setSavedQueries] = useState<Query[]>([]);
  const [saveModalVisible, setSaveModalVisible] = useState(false);
  const [saveForm] = Form.useForm();

  // 加载数据源列表
  const loadDatasources = async () => {
    try {
      const response = await apiClient.get('/datasources', {
        params: { workspaceId: 'workspace-123' }
      });
      setDatasources(response.data.content);
    } catch (error) {
      message.error('加载数据源列表失败');
    }
  };

  // 加载保存的查询
  const loadSavedQueries = async () => {
    try {
      const response = await apiClient.get('/queries', {
        params: { datasourceId: selectedDatasource }
      });
      setSavedQueries(response.data);
    } catch (error) {
      console.error('Load saved queries error:', error);
    }
  };

  // 执行查询
  const executeQuery = async () => {
    if (!selectedDatasource) {
      message.error('请先选择数据源');
      return;
    }

    if (!queryText.trim()) {
      message.error('请输入查询语句');
      return;
    }

    try {
      setExecuting(true);
      setExecutionResult(null);

      const response = await apiClient.post(`/datasources/${selectedDatasource}/execute`, {
        query: queryText,
        parameters: []
      });

      setExecutionResult(response.data);

      if (response.data.isExecutionSuccess) {
        message.success(`查询执行成功，返回 ${response.data.rowsAffected} 行数据`);
      } else {
        message.error(`查询执行失败: ${response.data.error?.message}`);
      }
    } catch (error) {
      message.error('查询执行失败');
      setExecutionResult({
        isExecutionSuccess: false,
        body: [],
        headers: {},
        statusCode: '500',
        executionTime: 0,
        rowsAffected: 0,
        error: {
          message: '查询执行失败',
          code: 'EXECUTION_ERROR'
        }
      });
    } finally {
      setExecuting(false);
    }
  };

  // 保存查询
  const saveQuery = async (values: any) => {
    try {
      await apiClient.post('/queries', {
        name: values.name,
        datasourceId: selectedDatasource,
        query: queryText,
        parameters: [],
        tags: values.tags ? values.tags.split(',').map((tag: string) => tag.trim()) : []
      });

      message.success('查询保存成功');
      setSaveModalVisible(false);
      saveForm.resetFields();
      loadSavedQueries();
    } catch (error) {
      message.error('保存查询失败');
    }
  };

  // 加载查询
  const loadQuery = (query: Query) => {
    setQueryText(query.query);
    message.success(`已加载查询: ${query.name}`);
  };

  // 导出结果
  const exportResults = () => {
    if (!executionResult?.body.length) {
      message.error('没有可导出的数据');
      return;
    }

    const csv = convertToCSV(executionResult.body);
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `query_result_${Date.now()}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // 转换为CSV格式
  const convertToCSV = (data: any[]) => {
    if (!data.length) return '';

    const headers = Object.keys(data[0]);
    const csvHeaders = headers.join(',');
    const csvRows = data.map(row =>
      headers.map(header => {
        const value = row[header];
        return typeof value === 'string' && value.includes(',')
          ? `"${value}"`
          : value;
      }).join(',')
    );

    return [csvHeaders, ...csvRows].join('\n');
  };

  // 构建结果表格列
  const buildResultColumns = () => {
    if (!executionResult?.body.length) return [];

    const firstRow = executionResult.body[0];
    return Object.keys(firstRow).map(key => ({
      title: key,
      dataIndex: key,
      key,
      width: 150,
      render: (value: any) => {
        if (value === null) return <Text type="secondary">NULL</Text>;
        if (typeof value === 'object') return JSON.stringify(value);
        return String(value);
      }
    }));
  };

  useEffect(() => {
    loadDatasources();
  }, []);

  useEffect(() => {
    if (selectedDatasource) {
      loadSavedQueries();
    }
  }, [selectedDatasource]);

  // 示例查询模板
  const queryTemplates = [
    {
      name: '查询所有记录',
      query: 'SELECT * FROM table_name LIMIT 10;'
    },
    {
      name: '统计记录数',
      query: 'SELECT COUNT(*) as total FROM table_name;'
    },
    {
      name: '按条件查询',
      query: 'SELECT * FROM table_name WHERE column_name = \'value\' LIMIT 10;'
    }
  ];

  return (
    <QueryEditorContainer>
      {/* 编辑器头部 */}
      <EditorHeader>
        <Space split={<Divider type="vertical" />}>
          <Space>
            <Text strong>数据源:</Text>
            <Select
              style={{ width: 200 }}
              placeholder="选择数据源"
              value={selectedDatasource}
              onChange={setSelectedDatasource}
            >
              {datasources.map(ds => (
                <Option key={ds.id} value={ds.id}>
                  <Space>
                    <DatabaseOutlined />
                    {ds.name}
                  </Space>
                </Option>
              ))}
            </Select>
          </Space>

          <Space>
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              loading={executing}
              onClick={executeQuery}
              disabled={!selectedDatasource}
            >
              执行查询
            </Button>
            <Button
              icon={<SaveOutlined />}
              onClick={() => setSaveModalVisible(true)}
              disabled={!queryText.trim()}
            >
              保存查询
            </Button>
            <Button
              icon={<DownloadOutlined />}
              onClick={exportResults}
              disabled={!executionResult?.body.length}
            >
              导出结果
            </Button>
          </Space>
        </Space>
      </EditorHeader>

      {/* 编辑器主体 */}
      <EditorLayout>
        {/* 侧边栏 */}
        <EditorSider $isDark={isDark} width={300} collapsible>
          <Tabs defaultActiveKey="saved" size="small">
            <TabPane tab="保存的查询" key="saved">
              <div style={{ padding: '8px' }}>
                {savedQueries.map(query => (
                  <Card
                    key={query.id}
                    size="small"
                    style={{ marginBottom: 8, cursor: 'pointer' }}
                    onClick={() => loadQuery(query)}
                    hoverable
                  >
                    <div style={{ marginBottom: 4 }}>
                      <Text strong>{query.name}</Text>
                    </div>
                    <div style={{ marginBottom: 4 }}>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {query.query.substring(0, 50)}...
                      </Text>
                    </div>
                    <div>
                      {query.tags.map(tag => (
                        <Tag key={tag} size="small">{tag}</Tag>
                      ))}
                    </div>
                  </Card>
                ))}
              </div>
            </TabPane>
            <TabPane tab="查询模板" key="templates">
              <div style={{ padding: '8px' }}>
                {queryTemplates.map((template, index) => (
                  <Card
                    key={index}
                    size="small"
                    style={{ marginBottom: 8, cursor: 'pointer' }}
                    onClick={() => setQueryText(template.query)}
                    hoverable
                  >
                    <div style={{ marginBottom: 4 }}>
                      <Text strong>{template.name}</Text>
                    </div>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {template.query}
                    </Text>
                  </Card>
                ))}
              </div>
            </TabPane>
          </Tabs>
        </EditorSider>

        {/* 主编辑区域 */}
        <EditorContent>
          {/* 查询输入区域 */}
          <QueryInputArea>
            <QueryTextArea
              value={queryText}
              onChange={(e) => setQueryText(e.target.value)}
              placeholder="请输入SQL查询语句..."
              rows={8}
              style={{ resize: 'vertical' }}
            />
          </QueryInputArea>

          {/* 执行信息 */}
          {executionResult && (
            <ExecutionInfo $isDark={isDark}>
              <Space split={<Divider type="vertical" />}>
                <span>
                  状态: {executionResult.isExecutionSuccess ? '成功' : '失败'}
                </span>
                <span>执行时间: {executionResult.executionTime}ms</span>
                <span>影响行数: {executionResult.rowsAffected}</span>
                {executionResult.error && (
                  <span style={{ color: '#ff4d4f' }}>
                    错误: {executionResult.error.message}
                  </span>
                )}
              </Space>
            </ExecutionInfo>
          )}

          {/* 结果展示区域 */}
          <ResultArea>
            {executing ? (
              <LoadingSpinner text="正在执行查询..." />
            ) : executionResult ? (
              executionResult.isExecutionSuccess ? (
                <Table
                  columns={buildResultColumns()}
                  dataSource={executionResult.body}
                  rowKey={(record, index) => index?.toString() || '0'}
                  scroll={{ x: true, y: 400 }}
                  pagination={{
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条记录`
                  }}
                  size="small"
                />
              ) : (
                <Card>
                  <div style={{ textAlign: 'center', padding: '40px 0' }}>
                    <Text type="danger" style={{ fontSize: '16px' }}>
                      查询执行失败
                    </Text>
                    <div style={{ marginTop: '16px' }}>
                      <Text type="secondary">
                        {executionResult.error?.message}
                      </Text>
                    </div>
                  </div>
                </Card>
              )
            ) : (
              <Card>
                <div style={{ textAlign: 'center', padding: '40px 0' }}>
                  <CodeOutlined style={{ fontSize: '48px', color: '#d9d9d9', marginBottom: '16px' }} />
                  <div>
                    <Text type="secondary">请输入查询语句并点击执行</Text>
                  </div>
                </div>
              </Card>
            )}
          </ResultArea>
        </EditorContent>
      </EditorLayout>

      {/* 保存查询对话框 */}
      <Modal
        title="保存查询"
        open={saveModalVisible}
        onCancel={() => setSaveModalVisible(false)}
        onOk={() => saveForm.submit()}
        okText="保存"
        cancelText="取消"
      >
        <Form form={saveForm} onFinish={saveQuery} layout="vertical">
          <Form.Item
            label="查询名称"
            name="name"
            rules={[{ required: true, message: '请输入查询名称' }]}
          >
            <Input placeholder="请输入查询名称" />
          </Form.Item>
          <Form.Item
            label="标签"
            name="tags"
            help="多个标签用逗号分隔"
          >
            <Input placeholder="例如: users, statistics" />
          </Form.Item>
        </Form>
      </Modal>
    </QueryEditorContainer>
  );
};

export default QueryEditor;
