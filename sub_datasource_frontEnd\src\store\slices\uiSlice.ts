import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// 状态接口
interface UIState {
  // 侧边栏状态
  sidebar: {
    collapsed: boolean;
    selectedKey: string;
  };
  
  // 主题设置
  theme: {
    mode: 'light' | 'dark';
    primaryColor: string;
  };
  
  // 页面加载状态
  pageLoading: boolean;
  
  // 全局通知
  notifications: Array<{
    id: string;
    type: 'success' | 'error' | 'warning' | 'info';
    title: string;
    message: string;
    timestamp: string;
    read: boolean;
  }>;
  
  // 模态框状态
  modals: {
    datasourceCreate: boolean;
    datasourceEdit: boolean;
    queryEditor: boolean;
    pluginDetail: boolean;
  };
  
  // 表格设置
  tableSettings: {
    pageSize: number;
    showSizeChanger: boolean;
    showQuickJumper: boolean;
  };
  
  // 查询编辑器设置
  queryEditor: {
    fontSize: number;
    theme: 'light' | 'dark';
    wordWrap: boolean;
    showLineNumbers: boolean;
    autoComplete: boolean;
  };
  
  // 面包屑导航
  breadcrumb: Array<{
    title: string;
    path?: string;
  }>;
  
  // 全局搜索
  globalSearch: {
    visible: boolean;
    query: string;
    results: any[];
    loading: boolean;
  };
}

// 初始状态
const initialState: UIState = {
  sidebar: {
    collapsed: false,
    selectedKey: '/datasources'
  },
  theme: {
    mode: 'light',
    primaryColor: '#1890ff'
  },
  pageLoading: false,
  notifications: [],
  modals: {
    datasourceCreate: false,
    datasourceEdit: false,
    queryEditor: false,
    pluginDetail: false
  },
  tableSettings: {
    pageSize: 10,
    showSizeChanger: true,
    showQuickJumper: true
  },
  queryEditor: {
    fontSize: 14,
    theme: 'light',
    wordWrap: true,
    showLineNumbers: true,
    autoComplete: true
  },
  breadcrumb: [],
  globalSearch: {
    visible: false,
    query: '',
    results: [],
    loading: false
  }
};

// 创建slice
const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // 侧边栏操作
    toggleSidebar: (state) => {
      state.sidebar.collapsed = !state.sidebar.collapsed;
    },
    
    setSidebarCollapsed: (state, action: PayloadAction<boolean>) => {
      state.sidebar.collapsed = action.payload;
    },
    
    setSidebarSelectedKey: (state, action: PayloadAction<string>) => {
      state.sidebar.selectedKey = action.payload;
    },
    
    // 主题操作
    setThemeMode: (state, action: PayloadAction<'light' | 'dark'>) => {
      state.theme.mode = action.payload;
      state.queryEditor.theme = action.payload;
    },
    
    setPrimaryColor: (state, action: PayloadAction<string>) => {
      state.theme.primaryColor = action.payload;
    },
    
    // 页面加载状态
    setPageLoading: (state, action: PayloadAction<boolean>) => {
      state.pageLoading = action.payload;
    },
    
    // 通知管理
    addNotification: (state, action: PayloadAction<Omit<UIState['notifications'][0], 'id' | 'timestamp' | 'read'>>) => {
      const notification = {
        ...action.payload,
        id: `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date().toISOString(),
        read: false
      };
      state.notifications.unshift(notification);
      
      // 限制通知数量
      if (state.notifications.length > 50) {
        state.notifications = state.notifications.slice(0, 50);
      }
    },
    
    markNotificationAsRead: (state, action: PayloadAction<string>) => {
      const notification = state.notifications.find(n => n.id === action.payload);
      if (notification) {
        notification.read = true;
      }
    },
    
    markAllNotificationsAsRead: (state) => {
      state.notifications.forEach(notification => {
        notification.read = true;
      });
    },
    
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(n => n.id !== action.payload);
    },
    
    clearAllNotifications: (state) => {
      state.notifications = [];
    },
    
    // 模态框操作
    setModalVisible: (state, action: PayloadAction<{ modal: keyof UIState['modals']; visible: boolean }>) => {
      const { modal, visible } = action.payload;
      state.modals[modal] = visible;
    },
    
    closeAllModals: (state) => {
      Object.keys(state.modals).forEach(key => {
        state.modals[key as keyof UIState['modals']] = false;
      });
    },
    
    // 表格设置
    setTablePageSize: (state, action: PayloadAction<number>) => {
      state.tableSettings.pageSize = action.payload;
    },
    
    setTableSettings: (state, action: PayloadAction<Partial<UIState['tableSettings']>>) => {
      state.tableSettings = { ...state.tableSettings, ...action.payload };
    },
    
    // 查询编辑器设置
    setQueryEditorFontSize: (state, action: PayloadAction<number>) => {
      state.queryEditor.fontSize = action.payload;
    },
    
    setQueryEditorSettings: (state, action: PayloadAction<Partial<UIState['queryEditor']>>) => {
      state.queryEditor = { ...state.queryEditor, ...action.payload };
    },
    
    // 面包屑导航
    setBreadcrumb: (state, action: PayloadAction<UIState['breadcrumb']>) => {
      state.breadcrumb = action.payload;
    },
    
    addBreadcrumbItem: (state, action: PayloadAction<{ title: string; path?: string }>) => {
      state.breadcrumb.push(action.payload);
    },
    
    clearBreadcrumb: (state) => {
      state.breadcrumb = [];
    },
    
    // 全局搜索
    setGlobalSearchVisible: (state, action: PayloadAction<boolean>) => {
      state.globalSearch.visible = action.payload;
      if (!action.payload) {
        state.globalSearch.query = '';
        state.globalSearch.results = [];
      }
    },
    
    setGlobalSearchQuery: (state, action: PayloadAction<string>) => {
      state.globalSearch.query = action.payload;
    },
    
    setGlobalSearchResults: (state, action: PayloadAction<any[]>) => {
      state.globalSearch.results = action.payload;
    },
    
    setGlobalSearchLoading: (state, action: PayloadAction<boolean>) => {
      state.globalSearch.loading = action.payload;
    }
  }
});

// 导出actions
export const {
  toggleSidebar,
  setSidebarCollapsed,
  setSidebarSelectedKey,
  setThemeMode,
  setPrimaryColor,
  setPageLoading,
  addNotification,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  removeNotification,
  clearAllNotifications,
  setModalVisible,
  closeAllModals,
  setTablePageSize,
  setTableSettings,
  setQueryEditorFontSize,
  setQueryEditorSettings,
  setBreadcrumb,
  addBreadcrumbItem,
  clearBreadcrumb,
  setGlobalSearchVisible,
  setGlobalSearchQuery,
  setGlobalSearchResults,
  setGlobalSearchLoading
} = uiSlice.actions;

// 导出reducer
export default uiSlice.reducer;

// 选择器
export const selectSidebar = (state: { ui: UIState }) => state.ui.sidebar;
export const selectTheme = (state: { ui: UIState }) => state.ui.theme;
export const selectPageLoading = (state: { ui: UIState }) => state.ui.pageLoading;
export const selectNotifications = (state: { ui: UIState }) => state.ui.notifications;
export const selectUnreadNotifications = (state: { ui: UIState }) => 
  state.ui.notifications.filter(n => !n.read);
export const selectModals = (state: { ui: UIState }) => state.ui.modals;
export const selectTableSettings = (state: { ui: UIState }) => state.ui.tableSettings;
export const selectQueryEditorSettings = (state: { ui: UIState }) => state.ui.queryEditor;
export const selectBreadcrumb = (state: { ui: UIState }) => state.ui.breadcrumb;
export const selectGlobalSearch = (state: { ui: UIState }) => state.ui.globalSearch;
