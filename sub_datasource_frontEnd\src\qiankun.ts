import { mount } from './bootstrap';

// 微前端生命周期钩子

/**
 * 应用启动前的准备工作
 */
export async function bootstrap() {
  console.log('[Datasource App] Bootstrap');
  
  // 可以在这里做一些全局的初始化工作
  // 例如：设置全局配置、初始化监控等
}

/**
 * 应用挂载
 */
export async function mount(props: any) {
  console.log('[Datasource App] Mount', props);
  
  const { container, ...otherProps } = props;
  
  // 获取挂载容器
  const mountContainer = container 
    ? container.querySelector('#datasource-app') || container
    : document.getElementById('datasource-app') || document.getElementById('root');
    
  if (!mountContainer) {
    throw new Error('Mount container not found');
  }

  // 挂载应用
  const app = mount(mountContainer, {
    basename: '/datasource',
    onNavigate: (location) => {
      // 通知主应用路由变化
      if (props.onGlobalStateChange) {
        props.onGlobalStateChange({
          type: 'ROUTE_CHANGE',
          payload: {
            app: 'datasource',
            path: location.pathname
          }
        });
      }
    },
    ...otherProps
  });

  // 保存应用实例到全局，用于卸载
  (window as any).__DATASOURCE_APP__ = app;
  
  // 监听全局状态变化
  if (props.onGlobalStateChange) {
    props.onGlobalStateChange((state: any, prev: any) => {
      console.log('[Datasource App] Global state changed:', state, prev);
      
      // 处理全局状态变化
      if (state.theme && state.theme !== prev?.theme) {
        // 主题变化处理
        document.documentElement.setAttribute('data-theme', state.theme);
      }
      
      if (state.user && state.user !== prev?.user) {
        // 用户信息变化处理
        console.log('[Datasource App] User changed:', state.user);
      }
    }, true);
  }

  return app;
}

/**
 * 应用卸载
 */
export async function unmount(props: any) {
  console.log('[Datasource App] Unmount', props);
  
  const app = (window as any).__DATASOURCE_APP__;
  if (app && app.unmount) {
    app.unmount();
  }
  
  // 清理全局变量
  delete (window as any).__DATASOURCE_APP__;
  
  // 清理事件监听器
  window.removeEventListener('popstate', () => {});
  
  // 清理定时器等资源
  // 如果有的话
}

/**
 * 应用更新
 */
export async function update(props: any) {
  console.log('[Datasource App] Update', props);
  
  const app = (window as any).__DATASOURCE_APP__;
  if (app && app.update) {
    app.update(props);
  }
}

// 导出应用信息
export const appInfo = {
  name: 'datasource-app',
  version: '1.0.0',
  description: 'PagePlug 数据源管理微前端应用',
  routes: [
    '/datasource',
    '/datasource/datasources',
    '/datasource/query',
    '/datasource/plugins'
  ],
  capabilities: [
    'datasource-management',
    'query-execution',
    'plugin-management'
  ]
};
