"use strict";(self.webpackChunkpageplug_datasource_frontend=self.webpackChunkpageplug_datasource_frontend||[]).push([[561,942],{241:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval("{/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   D: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* unused harmony export ThemeProvider */\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(6070);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(212);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(3017);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_2__);\n\r\n\r\n\r\nconst lightTheme = {\r\n    colors: {\r\n        primary: '#1890ff',\r\n        secondary: '#6c757d',\r\n        success: '#52c41a',\r\n        warning: '#faad14',\r\n        error: '#ff4d4f',\r\n        info: '#1890ff',\r\n        text: {\r\n            primary: '#262626',\r\n            secondary: '#595959',\r\n            disabled: '#bfbfbf'\r\n        },\r\n        background: {\r\n            primary: '#ffffff',\r\n            secondary: '#fafafa',\r\n            tertiary: '#f5f5f5'\r\n        },\r\n        border: {\r\n            primary: '#d9d9d9',\r\n            secondary: '#f0f0f0'\r\n        }\r\n    },\r\n    spacing: {\r\n        xs: '4px',\r\n        sm: '8px',\r\n        md: '16px',\r\n        lg: '24px',\r\n        xl: '32px',\r\n        xxl: '48px'\r\n    },\r\n    borderRadius: {\r\n        sm: '4px',\r\n        md: '6px',\r\n        lg: '8px'\r\n    },\r\n    shadows: {\r\n        sm: '0 2px 4px rgba(0, 0, 0, 0.06)',\r\n        md: '0 4px 12px rgba(0, 0, 0, 0.1)',\r\n        lg: '0 8px 24px rgba(0, 0, 0, 0.15)'\r\n    },\r\n    typography: {\r\n        fontFamily: \"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif\",\r\n        fontSize: {\r\n            xs: '12px',\r\n            sm: '14px',\r\n            md: '16px',\r\n            lg: '18px',\r\n            xl: '20px'\r\n        },\r\n        fontWeight: {\r\n            normal: 400,\r\n            medium: 500,\r\n            semibold: 600,\r\n            bold: 700\r\n        }\r\n    }\r\n};\r\nconst darkTheme = {\r\n    ...lightTheme,\r\n    colors: {\r\n        ...lightTheme.colors,\r\n        text: {\r\n            primary: '#ffffff',\r\n            secondary: '#d9d9d9',\r\n            disabled: '#595959'\r\n        },\r\n        background: {\r\n            primary: '#1f1f1f',\r\n            secondary: '#262626',\r\n            tertiary: '#141414'\r\n        },\r\n        border: {\r\n            primary: '#434343',\r\n            secondary: '#303030'\r\n        }\r\n    }\r\n};\r\nconst ThemeContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\r\nconst ThemeProvider = ({ children }) => {\r\n    const [isDark, setIsDark] = useState(() => {\r\n        const saved = localStorage.getItem('datasource-app-theme');\r\n        if (saved) {\r\n            return saved === 'dark';\r\n        }\r\n        return window.matchMedia('(prefers-color-scheme: dark)').matches;\r\n    });\r\n    const theme = isDark ? darkTheme : lightTheme;\r\n    const toggleTheme = () => {\r\n        setIsDark(prev => {\r\n            const newValue = !prev;\r\n            localStorage.setItem('datasource-app-theme', newValue ? 'dark' : 'light');\r\n            return newValue;\r\n        });\r\n    };\r\n    useEffect(() => {\r\n        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\r\n        const handleChange = (e) => {\r\n            const saved = localStorage.getItem('datasource-app-theme');\r\n            if (!saved) {\r\n                setIsDark(e.matches);\r\n            }\r\n        };\r\n        mediaQuery.addEventListener('change', handleChange);\r\n        return () => mediaQuery.removeEventListener('change', handleChange);\r\n    }, []);\r\n    useEffect(() => {\r\n        const root = document.documentElement;\r\n        if (isDark) {\r\n            root.classList.add('dark');\r\n        }\r\n        else {\r\n            root.classList.remove('dark');\r\n        }\r\n    }, [isDark]);\r\n    const contextValue = {\r\n        theme,\r\n        isDark,\r\n        toggleTheme\r\n    };\r\n    return (_jsx(ThemeContext.Provider, { value: contextValue, children: _jsx(StyledThemeProvider, { theme: theme, children: children }) }));\r\n};\r\nconst useTheme = () => {\r\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\r\n    if (context === undefined) {\r\n        throw new Error('useTheme must be used within a ThemeProvider');\r\n    }\r\n    return context;\r\n};\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///241\n\n}")},1561:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(6070);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(212);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(8076);\n/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(antd__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(3504);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(5469);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(3545);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(246);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(6025);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(853);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(1957);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(929);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(5941);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(9458);\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(1299);\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(react_router_dom__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(3017);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(6287);\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _services_apiClient__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(8876);\n/* harmony import */ var _hooks_useTheme__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(5869);\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nconst { Search } = antd__WEBPACK_IMPORTED_MODULE_2__.Input;\r\nconst { Option } = antd__WEBPACK_IMPORTED_MODULE_2__.Select;\r\nconst { confirm } = antd__WEBPACK_IMPORTED_MODULE_2__.Modal;\r\nconst PageContainer = (styled_components__WEBPACK_IMPORTED_MODULE_14___default().div) `\n  padding: 24px;\n`;\r\nconst PageHeader = (styled_components__WEBPACK_IMPORTED_MODULE_14___default().div) `\n  margin-bottom: 24px;\n`;\r\nconst PageTitle = (styled_components__WEBPACK_IMPORTED_MODULE_14___default().h1) `\n  font-size: 24px;\n  font-weight: 600;\n  color: ${props => props.$isDark ? '#ffffff' : '#262626'};\n  margin: 0 0 8px 0;\n`;\r\nconst PageDescription = (styled_components__WEBPACK_IMPORTED_MODULE_14___default().p) `\n  color: ${props => props.$isDark ? '#d9d9d9' : '#8c8c8c'};\n  margin: 0 0 16px 0;\n`;\r\nconst FilterSection = (styled_components__WEBPACK_IMPORTED_MODULE_14___default().div) `\n  margin-bottom: 16px;\n`;\r\nconst StatsCard = styled_components__WEBPACK_IMPORTED_MODULE_14___default()((0,antd__WEBPACK_IMPORTED_MODULE_2__.Card)) `\n  .ant-card-body {\n    padding: 16px;\n  }\n`;\r\nconst StatsNumber = (styled_components__WEBPACK_IMPORTED_MODULE_14___default().div) `\n  font-size: 24px;\n  font-weight: 600;\n  color: ${props => props.$color};\n  margin-bottom: 4px;\n`;\r\nconst StatsLabel = (styled_components__WEBPACK_IMPORTED_MODULE_14___default().div) `\n  font-size: 14px;\n  color: ${props => props.$isDark ? '#d9d9d9' : '#8c8c8c'};\n`;\r\nconst StatusTag = styled_components__WEBPACK_IMPORTED_MODULE_14___default()((0,antd__WEBPACK_IMPORTED_MODULE_2__.Tag)) `\n  ${props => {\r\n    switch (props.$status) {\r\n        case 'valid':\r\n            return `\n          background-color: #f6ffed;\n          border-color: #b7eb8f;\n          color: #52c41a;\n        `;\r\n        case 'invalid':\r\n            return `\n          background-color: #fff2f0;\n          border-color: #ffccc7;\n          color: #ff4d4f;\n        `;\r\n        default:\r\n            return `\n          background-color: #fafafa;\n          border-color: #d9d9d9;\n          color: #8c8c8c;\n        `;\r\n    }\r\n}}\n`;\r\nconst DatasourceList = () => {\r\n    const [datasources, setDatasources] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\r\n    const [plugins, setPlugins] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\r\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\r\n    const [searchText, setSearchText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\r\n    const [selectedPlugin, setSelectedPlugin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\r\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\r\n        current: 1,\r\n        pageSize: 10,\r\n        total: 0\r\n    });\r\n    const navigate = (0,react_router_dom__WEBPACK_IMPORTED_MODULE_13__.useNavigate)();\r\n    const { isDark } = (0,_hooks_useTheme__WEBPACK_IMPORTED_MODULE_17__/* .useTheme */ .D)();\r\n    const loadDatasources = async (page = 1, size = 10) => {\r\n        try {\r\n            setLoading(true);\r\n            const response = await _services_apiClient__WEBPACK_IMPORTED_MODULE_16__/* .apiClient */ .u.get('/datasources', {\r\n                params: {\r\n                    page,\r\n                    size,\r\n                    pluginId: selectedPlugin || undefined,\r\n                    workspaceId: 'workspace-123'\r\n                }\r\n            });\r\n            setDatasources(response.data.content);\r\n            setPagination({\r\n                current: page,\r\n                pageSize: size,\r\n                total: response.data.pagination.total\r\n            });\r\n        }\r\n        catch (error) {\r\n            antd__WEBPACK_IMPORTED_MODULE_2__.message.error('加载数据源列表失败');\r\n            console.error('Load datasources error:', error);\r\n        }\r\n        finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n    const loadPlugins = async () => {\r\n        try {\r\n            const response = await _services_apiClient__WEBPACK_IMPORTED_MODULE_16__/* .apiClient */ .u.get('/plugins', {\r\n                params: { status: 'active' }\r\n            });\r\n            setPlugins(response.data);\r\n        }\r\n        catch (error) {\r\n            console.error('Load plugins error:', error);\r\n        }\r\n    };\r\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\r\n        loadDatasources();\r\n        loadPlugins();\r\n    }, [selectedPlugin]);\r\n    const handleDelete = (datasource) => {\r\n        confirm({\r\n            title: '确认删除',\r\n            content: `确定要删除数据源 \"${datasource.name}\" 吗？此操作不可恢复。`,\r\n            okText: '删除',\r\n            okType: 'danger',\r\n            cancelText: '取消',\r\n            onOk: async () => {\r\n                try {\r\n                    await _services_apiClient__WEBPACK_IMPORTED_MODULE_16__/* .apiClient */ .u.delete(`/datasources/${datasource.id}`);\r\n                    antd__WEBPACK_IMPORTED_MODULE_2__.message.success('数据源删除成功');\r\n                    loadDatasources(pagination.current, pagination.pageSize);\r\n                }\r\n                catch (error) {\r\n                    antd__WEBPACK_IMPORTED_MODULE_2__.message.error('删除数据源失败');\r\n                }\r\n            }\r\n        });\r\n    };\r\n    const handleTestConnection = async (datasource) => {\r\n        try {\r\n            antd__WEBPACK_IMPORTED_MODULE_2__.message.loading({ content: '正在测试连接...', key: 'test-connection' });\r\n            const response = await _services_apiClient__WEBPACK_IMPORTED_MODULE_16__/* .apiClient */ .u.post(`/datasources/${datasource.id}/test`);\r\n            if (response.data.success) {\r\n                antd__WEBPACK_IMPORTED_MODULE_2__.message.success({\r\n                    content: `连接成功 (${response.data.responseTime}ms)`,\r\n                    key: 'test-connection'\r\n                });\r\n            }\r\n            else {\r\n                antd__WEBPACK_IMPORTED_MODULE_2__.message.error({\r\n                    content: `连接失败: ${response.data.message}`,\r\n                    key: 'test-connection'\r\n                });\r\n            }\r\n        }\r\n        catch (error) {\r\n            antd__WEBPACK_IMPORTED_MODULE_2__.message.error({\r\n                content: '连接测试失败',\r\n                key: 'test-connection'\r\n            });\r\n        }\r\n    };\r\n    const columns = [\r\n        {\r\n            title: '数据源名称',\r\n            dataIndex: 'name',\r\n            key: 'name',\r\n            width: 200,\r\n            render: (text, record) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Space, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* [\"default\"] */ .A, { style: { color: '#1890ff' } }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", { style: { cursor: 'pointer', color: '#1890ff' }, onClick: () => navigate(`/datasources/${record.id}`), children: text })] })),\r\n            filteredValue: searchText ? [searchText] : null,\r\n            onFilter: (value, record) => record.name.toLowerCase().includes(value.toString().toLowerCase())\r\n        },\r\n        {\r\n            title: '插件类型',\r\n            dataIndex: 'pluginName',\r\n            key: 'pluginName',\r\n            width: 120,\r\n            render: (text, record) => {\r\n                const plugin = plugins.find(p => p.id === record.pluginId);\r\n                return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Tag, { color: \"blue\", children: plugin?.displayName || text }));\r\n            }\r\n        },\r\n        {\r\n            title: '状态',\r\n            dataIndex: 'isValid',\r\n            key: 'status',\r\n            width: 100,\r\n            render: (isValid, record) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(StatusTag, { \"$status\": record.isConfigured ? (isValid ? 'valid' : 'invalid') : 'unknown', icon: record.isConfigured\r\n                    ? isValid\r\n                        ? (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* [\"default\"] */ .A, {})\r\n                        : (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* [\"default\"] */ .A, {})\r\n                    : undefined, children: record.isConfigured ? (isValid ? '正常' : '异常') : '未配置' }))\r\n        },\r\n        {\r\n            title: '最后使用',\r\n            dataIndex: 'lastUsed',\r\n            key: 'lastUsed',\r\n            width: 150,\r\n            render: (text) => text ? dayjs__WEBPACK_IMPORTED_MODULE_15___default()(text).format('YYYY-MM-DD HH:mm') : '-'\r\n        },\r\n        {\r\n            title: '创建时间',\r\n            dataIndex: 'createdAt',\r\n            key: 'createdAt',\r\n            width: 150,\r\n            render: (text) => dayjs__WEBPACK_IMPORTED_MODULE_15___default()(text).format('YYYY-MM-DD HH:mm')\r\n        },\r\n        {\r\n            title: '操作',\r\n            key: 'actions',\r\n            width: 120,\r\n            render: (_, record) => {\r\n                const menuItems = [\r\n                    {\r\n                        key: 'edit',\r\n                        icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* [\"default\"] */ .A, {}),\r\n                        label: '编辑',\r\n                        onClick: () => navigate(`/datasources/${record.id}/edit`)\r\n                    },\r\n                    {\r\n                        key: 'test',\r\n                        icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* [\"default\"] */ .A, {}),\r\n                        label: '测试连接',\r\n                        onClick: () => handleTestConnection(record)\r\n                    },\r\n                    {\r\n                        type: 'divider'\r\n                    },\r\n                    {\r\n                        key: 'delete',\r\n                        icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* [\"default\"] */ .A, {}),\r\n                        label: '删除',\r\n                        danger: true,\r\n                        onClick: () => handleDelete(record)\r\n                    }\r\n                ];\r\n                return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Space, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Button, { type: \"link\", size: \"small\", onClick: () => navigate(`/datasources/${record.id}`), children: \"\\u67E5\\u770B\" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Dropdown, { menu: { items: menuItems }, trigger: ['click'], children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Button, { type: \"text\", size: \"small\", icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* [\"default\"] */ .A, {}) }) })] }));\r\n            }\r\n        }\r\n    ];\r\n    const handleTableChange = (paginationConfig) => {\r\n        if (paginationConfig.current && paginationConfig.pageSize) {\r\n            loadDatasources(paginationConfig.current, paginationConfig.pageSize);\r\n        }\r\n    };\r\n    const stats = {\r\n        total: datasources.length,\r\n        valid: datasources.filter(ds => ds.isValid).length,\r\n        invalid: datasources.filter(ds => !ds.isValid).length,\r\n        unconfigured: datasources.filter(ds => !ds.isConfigured).length\r\n    };\r\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(PageContainer, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(PageHeader, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(PageTitle, { \"$isDark\": isDark, children: \"\\u6570\\u636E\\u6E90\\u7BA1\\u7406\" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(PageDescription, { \"$isDark\": isDark, children: \"\\u7BA1\\u7406\\u548C\\u914D\\u7F6E\\u5E94\\u7528\\u7A0B\\u5E8F\\u7684\\u6570\\u636E\\u6E90\\u8FDE\\u63A5\\uFF0C\\u652F\\u6301\\u591A\\u79CD\\u6570\\u636E\\u5E93\\u548CAPI\\u670D\\u52A1\" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Row, { gutter: 16, style: { marginBottom: 24 }, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Col, { span: 6, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(StatsCard, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(StatsNumber, { \"$color\": \"#1890ff\", children: stats.total }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(StatsLabel, { \"$isDark\": isDark, children: \"\\u603B\\u6570\\u636E\\u6E90\" })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Col, { span: 6, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(StatsCard, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(StatsNumber, { \"$color\": \"#52c41a\", children: stats.valid }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(StatsLabel, { \"$isDark\": isDark, children: \"\\u6B63\\u5E38\\u8FDE\\u63A5\" })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Col, { span: 6, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(StatsCard, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(StatsNumber, { \"$color\": \"#ff4d4f\", children: stats.invalid }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(StatsLabel, { \"$isDark\": isDark, children: \"\\u8FDE\\u63A5\\u5F02\\u5E38\" })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Col, { span: 6, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(StatsCard, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(StatsNumber, { \"$color\": \"#faad14\", children: stats.unconfigured }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(StatsLabel, { \"$isDark\": isDark, children: \"\\u672A\\u914D\\u7F6E\" })] }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Row, { justify: \"space-between\", align: \"middle\", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Col, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Space, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Button, { type: \"primary\", icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* [\"default\"] */ .A, {}), onClick: () => navigate('/datasources/create'), children: \"\\u65B0\\u5EFA\\u6570\\u636E\\u6E90\" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Button, { icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__/* [\"default\"] */ .A, {}), onClick: () => loadDatasources(pagination.current, pagination.pageSize), children: \"\\u5237\\u65B0\" })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Col, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(FilterSection, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Space, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Search, { placeholder: \"\\u641C\\u7D22\\u6570\\u636E\\u6E90\\u540D\\u79F0\", allowClear: true, style: { width: 200 }, value: searchText, onChange: (e) => setSearchText(e.target.value), prefix: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__/* [\"default\"] */ .A, {}) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Select, { placeholder: \"\\u9009\\u62E9\\u63D2\\u4EF6\\u7C7B\\u578B\", allowClear: true, style: { width: 150 }, value: selectedPlugin, onChange: setSelectedPlugin, children: plugins.map(plugin => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Option, { value: plugin.id, children: plugin.displayName }, plugin.id))) })] }) }) })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Card, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Table, { columns: columns, dataSource: datasources, rowKey: \"id\", loading: loading, pagination: {\r\n                        ...pagination,\r\n                        showSizeChanger: true,\r\n                        showQuickJumper: true,\r\n                        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条数据`\r\n                    }, onChange: handleTableChange, scroll: { x: 800 } }) })] }));\r\n};\r\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DatasourceList);\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///1561\n\n}")},5869:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval("{/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   D: () => (/* reexport safe */ _providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_0__.D)\n/* harmony export */ });\n/* harmony import */ var _providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(241);\n\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTg2OS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQ3NEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGFnZXBsdWctZGF0YXNvdXJjZS1mcm9udGVuZC8uL3NyYy9ob29rcy91c2VUaGVtZS50cz8zZTk1Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIOmHjeaWsOWvvOWHulRoZW1lUHJvdmlkZXLkuK3nmoR1c2VUaGVtZSBob29rXG5leHBvcnQgeyB1c2VUaGVtZSB9IGZyb20gJy4uL3Byb3ZpZGVycy9UaGVtZVByb3ZpZGVyJztcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///5869\n\n}")}}]);