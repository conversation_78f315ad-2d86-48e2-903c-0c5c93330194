# Vue3前端项目设计文档

## 设计概述

本设计文档基于需求文档，详细规划Vue3前端项目的技术架构、组件设计、API设计和实现方案。项目将在 `app/vue3/` 目录下创建，使用现代化的Vue3技术栈和模拟API服务。

## 技术架构设计

### 核心技术栈
- **Vue 3.3+**: 使用Composition API和`<script setup>`语法
- **Vite 4+**: 快速的构建工具和开发服务器
- **TypeScript**: 可选，提供类型安全
- **Vue Router 4**: 客户端路由管理
- **Pinia**: 轻量级状态管理
- **Element Plus**: UI组件库
- **Axios**: HTTP客户端
- **Mock Service Worker (MSW)**: API模拟服务

### 项目目录结构
```
app/vue3/
├── public/
│   ├── favicon.ico
│   ├── mockServiceWorker.js      # MSW服务工作者
│   └── index.html
├── src/
│   ├── api/                      # API接口层
│   │   ├── datasource.js         # 数据源API
│   │   ├── plugin.js             # 插件API
│   │   ├── system.js             # 系统API
│   │   └── index.js              # API统一导出
│   ├── components/               # 组件库
│   │   ├── common/               # 通用组件
│   │   │   ├── AppHeader.vue     # 应用头部
│   │   │   ├── AppSidebar.vue    # 侧边栏
│   │   │   ├── LoadingSpinner.vue # 加载动画
│   │   │   ├── ErrorMessage.vue   # 错误提示
│   │   │   └── ConfirmDialog.vue  # 确认对话框
│   │   ├── datasource/           # 数据源组件
│   │   │   ├── DatasourceCard.vue # 数据源卡片
│   │   │   ├── DatasourceForm.vue # 数据源表单
│   │   │   ├── ConnectionTest.vue # 连接测试
│   │   │   ├── StructureViewer.vue # 结构查看器
│   │   │   └── PluginSelector.vue # 插件选择器
│   │   └── plugin/               # 插件组件
│   │       ├── PluginCard.vue    # 插件卡片
│   │       └── PluginFilter.vue  # 插件筛选器
│   ├── composables/              # 组合式函数
│   │   ├── useDatasource.js      # 数据源管理
│   │   ├── usePlugin.js          # 插件管理
│   │   ├── useNotification.js    # 通知管理
│   │   ├── useValidation.js      # 表单验证
│   │   └── useApi.js             # API调用封装
│   ├── stores/                   # Pinia状态管理
│   │   ├── datasource.js         # 数据源状态
│   │   ├── plugin.js             # 插件状态
│   │   ├── system.js             # 系统状态
│   │   └── index.js              # Store统一导出
│   ├── views/                    # 页面视图
│   │   ├── Dashboard.vue         # 仪表板
│   │   ├── DatasourceList.vue    # 数据源列表
│   │   ├── DatasourceDetail.vue  # 数据源详情
│   │   ├── DatasourceCreate.vue  # 创建数据源
│   │   └── PluginManage.vue      # 插件管理
│   ├── router/                   # 路由配置
│   │   └── index.js
│   ├── utils/                    # 工具函数
│   │   ├── request.js            # HTTP请求封装
│   │   ├── constants.js          # 常量定义
│   │   ├── helpers.js            # 辅助函数
│   │   └── validation.js         # 验证规则
│   ├── styles/                   # 样式文件
│   │   ├── main.scss             # 主样式
│   │   ├── variables.scss        # 变量定义
│   │   ├── mixins.scss           # 混入
│   │   └── components.scss       # 组件样式
│   ├── mocks/                    # 模拟数据
│   │   ├── handlers/             # MSW处理器
│   │   │   ├── datasource.js     # 数据源API处理器
│   │   │   ├── plugin.js         # 插件API处理器
│   │   │   └── system.js         # 系统API处理器
│   │   ├── data/                 # 模拟数据
│   │   │   ├── datasources.js    # 数据源数据
│   │   │   ├── plugins.js        # 插件数据
│   │   │   └── system.js         # 系统数据
│   │   └── index.js              # Mock服务入口
│   ├── App.vue                   # 根组件
│   └── main.js                   # 应用入口
├── tests/                        # 测试文件
│   ├── unit/                     # 单元测试
│   └── e2e/                      # E2E测试
├── .env                          # 环境变量
├── .env.development              # 开发环境变量
├── .env.production               # 生产环境变量
├── .gitignore
├── package.json
├── vite.config.js                # Vite配置
├── vitest.config.js              # 测试配置
├── eslint.config.js              # ESLint配置
├── prettier.config.js            # Prettier配置
└── README.md
```

## 组件架构设计

### 布局组件设计

#### 主布局组件 (App.vue)
```vue
<template>
  <div id="app" class="app-container">
    <AppHeader />
    <div class="app-body">
      <AppSidebar v-if="showSidebar" />
      <main class="main-content">
        <router-view />
      </main>
    </div>
    <el-backtop />
  </div>
</template>
```

#### 应用头部 (AppHeader.vue)
- 包含应用标题、用户信息、通知中心
- 响应式设计，移动端显示汉堡菜单
- 支持主题切换和语言切换

#### 侧边栏 (AppSidebar.vue)
- 导航菜单，支持折叠/展开
- 当前页面高亮显示
- 移动端支持抽屉式显示

### 数据源组件设计

#### 数据源卡片 (DatasourceCard.vue)
```vue
<template>
  <el-card class="datasource-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <div class="plugin-info">
          <img :src="datasource.pluginIcon" class="plugin-icon" />
          <div>
            <h3>{{ datasource.name }}</h3>
            <p class="plugin-name">{{ datasource.pluginName }}</p>
          </div>
        </div>
        <el-tag :type="statusType" size="small">
          {{ statusText }}
        </el-tag>
      </div>
    </template>
    
    <div class="card-content">
      <p class="description">{{ datasource.description }}</p>
      <div class="connection-info">
        <el-text size="small" type="info">
          {{ connectionString }}
        </el-text>
      </div>
      <div class="meta-info">
        <span>最后连接: {{ formatDate(datasource.lastConnected) }}</span>
      </div>
    </div>
    
    <template #footer>
      <div class="card-actions">
        <el-button size="small" @click="testConnection">
          <el-icon><Connection /></el-icon>
          测试连接
        </el-button>
        <el-button size="small" @click="viewDetail">
          <el-icon><View /></el-icon>
          查看
        </el-button>
        <el-dropdown @command="handleCommand">
          <el-button size="small">
            <el-icon><More /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="edit">编辑</el-dropdown-item>
              <el-dropdown-item command="clone">克隆</el-dropdown-item>
              <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </template>
  </el-card>
</template>
```

#### 数据源表单 (DatasourceForm.vue)
- 动态表单，根据选择的插件类型渲染不同的配置项
- 支持表单验证和实时验证
- 支持配置预览和测试连接
- 分步骤表单，提供良好的用户体验

#### 插件选择器 (PluginSelector.vue)
```vue
<template>
  <div class="plugin-selector">
    <div class="search-section">
      <el-input
        v-model="searchQuery"
        placeholder="搜索插件..."
        prefix-icon="Search"
        clearable
      />
    </div>
    
    <div class="category-tabs">
      <el-tabs v-model="activeCategory" @tab-change="handleCategoryChange">
        <el-tab-pane label="全部" name="all" />
        <el-tab-pane label="数据库" name="database" />
        <el-tab-pane label="API" name="api" />
        <el-tab-pane label="云服务" name="cloud" />
      </el-tabs>
    </div>
    
    <div class="plugin-grid">
      <div
        v-for="plugin in filteredPlugins"
        :key="plugin.id"
        class="plugin-item"
        :class="{ active: selectedPlugin?.id === plugin.id }"
        @click="selectPlugin(plugin)"
      >
        <div class="plugin-icon">
          <img :src="plugin.icon" :alt="plugin.name" />
        </div>
        <div class="plugin-info">
          <h4>{{ plugin.name }}</h4>
          <p>{{ plugin.description }}</p>
          <el-tag size="small" type="info">{{ plugin.category }}</el-tag>
        </div>
      </div>
    </div>
  </div>
</template>
```

### 页面组件设计

#### 仪表板页面 (Dashboard.vue)
- 统计卡片：数据源总数、活跃连接、插件数量
- 最近使用的数据源列表
- 快速操作区域
- 系统状态监控

#### 数据源列表页面 (DatasourceList.vue)
- 搜索和筛选功能
- 数据源卡片网格布局
- 分页组件
- 批量操作功能

#### 数据源详情页面 (DatasourceDetail.vue)
- 标签页设计：概览、结构、查询、设置
- 数据源基本信息展示
- 表结构树形展示
- 查询测试工具

## 状态管理设计

### 数据源状态管理 (stores/datasource.js)
```javascript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import * as datasourceApi from '@/api/datasource'

export const useDatasourceStore = defineStore('datasource', () => {
  // 状态
  const datasources = ref([])
  const currentDatasource = ref(null)
  const loading = ref(false)
  const error = ref(null)
  
  // 分页状态
  const pagination = ref({
    page: 1,
    size: 12,
    total: 0
  })
  
  // 筛选状态
  const filters = ref({
    search: '',
    pluginType: '',
    status: '',
    sortBy: 'updatedAt',
    sortOrder: 'desc'
  })
  
  // 计算属性
  const activeDatasources = computed(() => 
    datasources.value.filter(ds => ds.status === 'active')
  )
  
  const datasourcesByPlugin = computed(() => {
    const grouped = {}
    datasources.value.forEach(ds => {
      const pluginName = ds.pluginName
      if (!grouped[pluginName]) {
        grouped[pluginName] = []
      }
      grouped[pluginName].push(ds)
    })
    return grouped
  })
  
  const totalPages = computed(() => 
    Math.ceil(pagination.value.total / pagination.value.size)
  )
  
  // 操作方法
  const fetchDatasources = async (params = {}) => {
    loading.value = true
    error.value = null
    
    try {
      const queryParams = {
        ...pagination.value,
        ...filters.value,
        ...params
      }
      
      const response = await datasourceApi.getDatasources(queryParams)
      
      datasources.value = response.data.items
      pagination.value = {
        page: response.data.page,
        size: response.data.size,
        total: response.data.total
      }
      
      return response.data
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const fetchDatasourceById = async (id) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await datasourceApi.getDatasourceById(id)
      currentDatasource.value = response.data
      return response.data
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const createDatasource = async (datasourceData) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await datasourceApi.createDatasource(datasourceData)
      
      // 添加到列表开头
      datasources.value.unshift(response.data)
      
      // 更新总数
      pagination.value.total += 1
      
      return response.data
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const updateDatasource = async (id, datasourceData) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await datasourceApi.updateDatasource(id, datasourceData)
      
      // 更新列表中的数据
      const index = datasources.value.findIndex(ds => ds.id === id)
      if (index !== -1) {
        datasources.value[index] = response.data
      }
      
      // 更新当前数据源
      if (currentDatasource.value?.id === id) {
        currentDatasource.value = response.data
      }
      
      return response.data
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const deleteDatasource = async (id) => {
    loading.value = true
    error.value = null
    
    try {
      await datasourceApi.deleteDatasource(id)
      
      // 从列表中移除
      datasources.value = datasources.value.filter(ds => ds.id !== id)
      
      // 更新总数
      pagination.value.total -= 1
      
      // 清除当前数据源
      if (currentDatasource.value?.id === id) {
        currentDatasource.value = null
      }
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const testConnection = async (id, config) => {
    try {
      const response = await datasourceApi.testConnection(id, config)
      return response.data
    } catch (err) {
      throw err
    }
  }
  
  // 筛选和搜索
  const updateFilters = (newFilters) => {
    filters.value = { ...filters.value, ...newFilters }
    pagination.value.page = 1 // 重置到第一页
  }
  
  const updatePagination = (newPagination) => {
    pagination.value = { ...pagination.value, ...newPagination }
  }
  
  // 重置状态
  const resetState = () => {
    datasources.value = []
    currentDatasource.value = null
    loading.value = false
    error.value = null
    pagination.value = { page: 1, size: 12, total: 0 }
    filters.value = { search: '', pluginType: '', status: '', sortBy: 'updatedAt', sortOrder: 'desc' }
  }
  
  return {
    // 状态
    datasources,
    currentDatasource,
    loading,
    error,
    pagination,
    filters,
    
    // 计算属性
    activeDatasources,
    datasourcesByPlugin,
    totalPages,
    
    // 方法
    fetchDatasources,
    fetchDatasourceById,
    createDatasource,
    updateDatasource,
    deleteDatasource,
    testConnection,
    updateFilters,
    updatePagination,
    resetState
  }
})
```

## API模拟服务设计

### Mock Service Worker (MSW) 配置

#### 模拟数据结构 (mocks/data/datasources.js)
```javascript
export const mockDatasources = [
  {
    id: 'ds_001',
    name: 'MySQL主数据库',
    description: '生产环境主数据库，存储用户和订单数据',
    pluginId: 'mysql-plugin',
    pluginName: 'MySQL',
    pluginIcon: '/icons/mysql.svg',
    status: 'active',
    connectionString: 'mysql://localhost:3306/mydb',
    lastConnected: '2024-01-15T10:30:00Z',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
    workspaceId: 'ws_001',
    isConfigured: true,
    isMock: false,
    configuration: {
      connection: {
        mode: 'READ_WRITE',
        defaultDatabaseName: 'mydb'
      },
      endpoints: [
        {
          host: 'localhost',
          port: 3306
        }
      ],
      authentication: {
        username: 'admin',
        databaseName: 'mydb',
        authType: 'USERNAME_PASSWORD'
      },
      ssl: {
        authType: 'DEFAULT'
      }
    }
  },
  {
    id: 'ds_002',
    name: 'MongoDB文档库',
    description: '存储产品信息和用户行为数据',
    pluginId: 'mongo-plugin',
    pluginName: 'MongoDB',
    pluginIcon: '/icons/mongodb.svg',
    status: 'active',
    connectionString: 'mongodb://localhost:27017/productdb',
    lastConnected: '2024-01-15T09:15:00Z',
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-15T09:15:00Z',
    workspaceId: 'ws_001',
    isConfigured: true,
    isMock: false,
    configuration: {
      connection: {
        mode: 'READ_WRITE',
        defaultDatabaseName: 'productdb'
      },
      endpoints: [
        {
          host: 'localhost',
          port: 27017
        }
      ],
      authentication: {
        username: 'admin',
        databaseName: 'admin',
        authType: 'SCRAM_SHA_1'
      }
    }
  },
  {
    id: 'ds_003',
    name: 'Redis缓存',
    description: '应用缓存和会话存储',
    pluginId: 'redis-plugin',
    pluginName: 'Redis',
    pluginIcon: '/icons/redis.svg',
    status: 'active',
    connectionString: 'redis://localhost:6379',
    lastConnected: '2024-01-15T11:00:00Z',
    createdAt: '2024-01-03T00:00:00Z',
    updatedAt: '2024-01-15T11:00:00Z',
    workspaceId: 'ws_001',
    isConfigured: true,
    isMock: false,
    configuration: {
      endpoints: [
        {
          host: 'localhost',
          port: 6379
        }
      ]
    }
  },
  {
    id: 'ds_004',
    name: '用户API服务',
    description: '第三方用户管理API接口',
    pluginId: 'rest-api-plugin',
    pluginName: 'REST API',
    pluginIcon: '/icons/api.svg',
    status: 'error',
    connectionString: 'https://api.example.com/users',
    lastConnected: '2024-01-14T16:30:00Z',
    createdAt: '2024-01-04T00:00:00Z',
    updatedAt: '2024-01-14T16:30:00Z',
    workspaceId: 'ws_001',
    isConfigured: true,
    isMock: false,
    configuration: {
      url: 'https://api.example.com',
      headers: [
        {
          key: 'Authorization',
          value: 'Bearer token123'
        }
      ]
    }
  }
]

export const mockDatasourceStructure = {
  'ds_001': {
    tables: [
      {
        type: 'TABLE',
        schema: 'mydb',
        name: 'users',
        columns: [
          {
            name: 'id',
            type: 'INT',
            defaultValue: null,
            isAutogenerated: true
          },
          {
            name: 'username',
            type: 'VARCHAR(50)',
            defaultValue: null,
            isAutogenerated: false
          },
          {
            name: 'email',
            type: 'VARCHAR(100)',
            defaultValue: null,
            isAutogenerated: false
          },
          {
            name: 'created_at',
            type: 'TIMESTAMP',
            defaultValue: 'CURRENT_TIMESTAMP',
            isAutogenerated: true
          }
        ],
        keys: [
          {
            type: 'primary key',
            name: 'PRIMARY',
            columnNames: ['id']
          }
        ],
        templates: [
          {
            title: '查找所有用户',
            body: 'SELECT * FROM users',
            isSuggested: true
          },
          {
            title: '根据ID查找用户',
            body: 'SELECT * FROM users WHERE id = ?',
            isSuggested: true
          }
        ]
      },
      {
        type: 'TABLE',
        schema: 'mydb',
        name: 'orders',
        columns: [
          {
            name: 'id',
            type: 'INT',
            defaultValue: null,
            isAutogenerated: true
          },
          {
            name: 'user_id',
            type: 'INT',
            defaultValue: null,
            isAutogenerated: false
          },
          {
            name: 'total_amount',
            type: 'DECIMAL(10,2)',
            defaultValue: null,
            isAutogenerated: false
          },
          {
            name: 'status',
            type: 'VARCHAR(20)',
            defaultValue: 'pending',
            isAutogenerated: false
          },
          {
            name: 'created_at',
            type: 'TIMESTAMP',
            defaultValue: 'CURRENT_TIMESTAMP',
            isAutogenerated: true
          }
        ],
        keys: [
          {
            type: 'primary key',
            name: 'PRIMARY',
            columnNames: ['id']
          },
          {
            type: 'foreign key',
            name: 'FK_orders_user_id',
            fromColumns: ['user_id'],
            toColumns: ['id']
          }
        ],
        templates: [
          {
            title: '查找所有订单',
            body: 'SELECT * FROM orders',
            isSuggested: true
          },
          {
            title: '根据用户ID查找订单',
            body: 'SELECT * FROM orders WHERE user_id = ?',
            isSuggested: true
          }
        ]
      }
    ]
  },
  'ds_002': {
    tables: [
      {
        type: 'COLLECTION',
        schema: 'productdb',
        name: 'products',
        columns: [
          {
            name: '_id',
            type: 'ObjectId',
            defaultValue: null,
            isAutogenerated: true
          },
          {
            name: 'name',
            type: 'String',
            defaultValue: null,
            isAutogenerated: false
          },
          {
            name: 'price',
            type: 'Number',
            defaultValue: null,
            isAutogenerated: false
          },
          {
            name: 'category',
            type: 'String',
            defaultValue: null,
            isAutogenerated: false
          },
          {
            name: 'createdAt',
            type: 'Date',
            defaultValue: null,
            isAutogenerated: true
          }
        ],
        keys: [],
        templates: [
          {
            title: '查找所有产品',
            body: 'db.products.find({})',
            isSuggested: true
          },
          {
            title: '根据分类查找产品',
            body: 'db.products.find({"category": "electronics"})',
            isSuggested: true
          }
        ]
      }
    ]
  }
}
```

#### API处理器 (mocks/handlers/datasource.js)
```javascript
import { rest } from 'msw'
import { mockDatasources, mockDatasourceStructure } from '../data/datasources'

let datasources = [...mockDatasources]
let nextId = 5

export const datasourceHandlers = [
  // 获取数据源列表
  rest.get('/api/datasources', (req, res, ctx) => {
    const url = new URL(req.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const size = parseInt(url.searchParams.get('size') || '12')
    const search = url.searchParams.get('search') || ''
    const pluginType = url.searchParams.get('pluginType') || ''
    const status = url.searchParams.get('status') || ''
    const sortBy = url.searchParams.get('sortBy') || 'updatedAt'
    const sortOrder = url.searchParams.get('sortOrder') || 'desc'
    
    // 筛选数据
    let filteredDatasources = datasources
    
    if (search) {
      filteredDatasources = filteredDatasources.filter(ds => 
        ds.name.toLowerCase().includes(search.toLowerCase()) ||
        ds.description.toLowerCase().includes(search.toLowerCase())
      )
    }
    
    if (pluginType) {
      filteredDatasources = filteredDatasources.filter(ds => 
        ds.pluginName.toLowerCase() === pluginType.toLowerCase()
      )
    }
    
    if (status) {
      filteredDatasources = filteredDatasources.filter(ds => 
        ds.status === status
      )
    }
    
    // 排序
    filteredDatasources.sort((a, b) => {
      const aValue = a[sortBy]
      const bValue = b[sortBy]
      
      if (sortOrder === 'desc') {
        return bValue > aValue ? 1 : -1
      } else {
        return aValue > bValue ? 1 : -1
      }
    })
    
    // 分页
    const total = filteredDatasources.length
    const startIndex = (page - 1) * size
    const endIndex = startIndex + size
    const items = filteredDatasources.slice(startIndex, endIndex)
    
    return res(
      ctx.delay(300), // 模拟网络延迟
      ctx.json({
        success: true,
        data: {
          items,
          total,
          page,
          size,
          totalPages: Math.ceil(total / size)
        }
      })
    )
  }),
  
  // 获取数据源详情
  rest.get('/api/datasources/:id', (req, res, ctx) => {
    const { id } = req.params
    const datasource = datasources.find(ds => ds.id === id)
    
    if (!datasource) {
      return res(
        ctx.status(404),
        ctx.json({
          success: false,
          message: '数据源不存在'
        })
      )
    }
    
    // 添加统计信息
    const datasourceWithStats = {
      ...datasource,
      statistics: {
        totalQueries: Math.floor(Math.random() * 10000),
        successRate: 95 + Math.random() * 5,
        avgResponseTime: 20 + Math.random() * 100,
        lastQueryTime: new Date(Date.now() - Math.random() * 86400000).toISOString()
      }
    }
    
    return res(
      ctx.delay(200),
      ctx.json({
        success: true,
        data: datasourceWithStats
      })
    )
  }),
  
  // 创建数据源
  rest.post('/api/datasources', async (req, res, ctx) => {
    const datasourceData = await req.json()
    
    const newDatasource = {
      id: `ds_${String(nextId++).padStart(3, '0')}`,
      ...datasourceData,
      status: 'active',
      lastConnected: null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isConfigured: true,
      isMock: false
    }
    
    datasources.unshift(newDatasource)
    
    return res(
      ctx.delay(500),
      ctx.json({
        success: true,
        data: newDatasource
      })
    )
  }),
  
  // 更新数据源
  rest.put('/api/datasources/:id', async (req, res, ctx) => {
    const { id } = req.params
    const updateData = await req.json()
    
    const index = datasources.findIndex(ds => ds.id === id)
    if (index === -1) {
      return res(
        ctx.status(404),
        ctx.json({
          success: false,
          message: '数据源不存在'
        })
      )
    }
    
    const updatedDatasource = {
      ...datasources[index],
      ...updateData,
      updatedAt: new Date().toISOString()
    }
    
    datasources[index] = updatedDatasource
    
    return res(
      ctx.delay(400),
      ctx.json({
        success: true,
        data: updatedDatasource
      })
    )
  }),
  
  // 删除数据源
  rest.delete('/api/datasources/:id', (req, res, ctx) => {
    const { id } = req.params
    const index = datasources.findIndex(ds => ds.id === id)
    
    if (index === -1) {
      return res(
        ctx.status(404),
        ctx.json({
          success: false,
          message: '数据源不存在'
        })
      )
    }
    
    datasources.splice(index, 1)
    
    return res(
      ctx.delay(300),
      ctx.json({
        success: true,
        message: '数据源删除成功'
      })
    )
  }),
  
  // 测试连接
  rest.post('/api/datasources/:id/test', async (req, res, ctx) => {
    const { id } = req.params
    const config = await req.json()
    
    // 模拟连接测试
    const isSuccess = Math.random() > 0.2 // 80% 成功率
    const responseTime = 50 + Math.random() * 200
    
    if (isSuccess) {
      return res(
        ctx.delay(responseTime),
        ctx.json({
          success: true,
          data: {
            isValid: true,
            message: '连接测试成功',
            responseTime: Math.round(responseTime),
            details: {
              serverVersion: '8.0.25',
              charset: 'utf8mb4'
            }
          }
        })
      )
    } else {
      return res(
        ctx.delay(responseTime),
        ctx.json({
          success: false,
          data: {
            isValid: false,
            message: '连接失败：无法连接到数据库服务器',
            responseTime: Math.round(responseTime)
          }
        })
      )
    }
  }),
  
  // 获取数据源结构
  rest.get('/api/datasources/:id/structure', (req, res, ctx) => {
    const { id } = req.params
    const structure = mockDatasourceStructure[id]
    
    if (!structure) {
      return res(
        ctx.status(404),
        ctx.json({
          success: false,
          message: '数据源结构不存在'
        })
      )
    }
    
    return res(
      ctx.delay(400),
      ctx.json({
        success: true,
        data: structure
      })
    )
  })
]
```

## 样式设计规范

### CSS变量定义 (styles/variables.scss)
```scss
:root {
  // 主色调
  --color-primary: #409eff;
  --color-primary-light: #79bbff;
  --color-primary-dark: #337ecc;
  
  // 辅助色
  --color-success: #67c23a;
  --color-warning: #e6a23c;
  --color-danger: #f56c6c;
  --color-info: #909399;
  
  // 中性色
  --color-text-primary: #303133;
  --color-text-regular: #606266;
  --color-text-secondary: #909399;
  --color-text-placeholder: #c0c4cc;
  
  // 背景色
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f5f7fa;
  --color-bg-tertiary: #fafafa;
  
  // 边框色
  --color-border-light: #ebeef5;
  --color-border-base: #dcdfe6;
  --color-border-dark: #d4d7de;
  
  // 阴影
  --shadow-light: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.12);
  --shadow-dark: 0 4px 8px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.12);
  
  // 间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  // 圆角
  --border-radius-sm: 2px;
  --border-radius-base: 4px;
  --border-radius-lg: 6px;
  --border-radius-xl: 8px;
  
  // 字体
  --font-size-xs: 12px;
  --font-size-sm: 13px;
  --font-size-base: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  
  // 布局
  --header-height: 60px;
  --sidebar-width: 240px;
  --sidebar-collapsed-width: 64px;
}

// 暗色主题
[data-theme="dark"] {
  --color-bg-primary: #1d1e1f;
  --color-bg-secondary: #25262b;
  --color-bg-tertiary: #2c2e33;
  
  --color-text-primary: #ffffff;
  --color-text-regular: #c1c2c5;
  --color-text-secondary: #909296;
  --color-text-placeholder: #5c5f66;
  
  --color-border-light: #373a40;
  --color-border-base: #424549;
  --color-border-dark: #4c4f54;
}
```

### 响应式设计断点
```scss
// 断点定义
$breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px
);

// 媒体查询混入
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  }
}

// 使用示例
.datasource-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-md);
  
  @include respond-to(sm) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @include respond-to(md) {
    grid-template-columns: repeat(3, 1fr);
  }
  
  @include respond-to(lg) {
    grid-template-columns: repeat(4, 1fr);
  }
}
```

## 错误处理和用户体验设计

### 全局错误处理
```javascript
// utils/errorHandler.js
import { ElMessage, ElNotification } from 'element-plus'

export class ErrorHandler {
  static handle(error, context = '') {
    console.error(`[${context}] Error:`, error)
    
    if (error.response) {
      // HTTP错误
      const { status, data } = error.response
      
      switch (status) {
        case 400:
          ElMessage.error(data.message || '请求参数错误')
          break
        case 401:
          ElMessage.error('未授权，请重新登录')
          // 跳转到登录页
          break
        case 403:
          ElMessage.error('权限不足')
          break
        case 404:
          ElMessage.error('资源不存在')
          break
        case 500:
          ElNotification.error({
            title: '服务器错误',
            message: '服务器内部错误，请稍后重试'
          })
          break
        default:
          ElMessage.error(data.message || '请求失败')
      }
    } else if (error.request) {
      // 网络错误
      ElNotification.error({
        title: '网络错误',
        message: '网络连接失败，请检查网络设置'
      })
    } else {
      // 其他错误
      ElMessage.error(error.message || '未知错误')
    }
  }
  
  static success(message) {
    ElMessage.success(message)
  }
  
  static warning(message) {
    ElMessage.warning(message)
  }
  
  static info(message) {
    ElMessage.info(message)
  }
}
```

### 加载状态管理
```javascript
// composables/useLoading.js
import { ref } from 'vue'

export function useLoading(initialState = false) {
  const loading = ref(initialState)
  
  const withLoading = async (asyncFn) => {
    loading.value = true
    try {
      const result = await asyncFn()
      return result
    } finally {
      loading.value = false
    }
  }
  
  const setLoading = (state) => {
    loading.value = state
  }
  
  return {
    loading: readonly(loading),
    withLoading,
    setLoading
  }
}
```

## 性能优化设计

### 组件懒加载
```javascript
// router/index.js
const routes = [
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue')
  },
  {
    path: '/datasources',
    name: 'DatasourceList',
    component: () => import('@/views/DatasourceList.vue')
  }
]
```

### 虚拟滚动
对于大量数据的列表，使用虚拟滚动技术：
```vue
<template>
  <el-virtual-list
    :data="datasources"
    :height="400"
    :item-size="120"
    #default="{ item }"
  >
    <DatasourceCard :datasource="item" />
  </el-virtual-list>
</template>
```

### 图片懒加载
```vue
<template>
  <img
    v-lazy="datasource.pluginIcon"
    :alt="datasource.pluginName"
    class="plugin-icon"
  />
</template>
```

## 测试策略设计

### 单元测试
使用Vitest进行组件和函数的单元测试：
```javascript
// tests/unit/components/DatasourceCard.test.js
import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import DatasourceCard from '@/components/datasource/DatasourceCard.vue'

describe('DatasourceCard', () => {
  it('renders datasource information correctly', () => {
    const datasource = {
      id: 'ds_001',
      name: 'Test Database',
      pluginName: 'MySQL',
      status: 'active'
    }
    
    const wrapper = mount(DatasourceCard, {
      props: { datasource }
    })
    
    expect(wrapper.text()).toContain('Test Database')
    expect(wrapper.text()).toContain('MySQL')
  })
})
```

### E2E测试
使用Cypress进行端到端测试：
```javascript
// tests/e2e/datasource.cy.js
describe('Datasource Management', () => {
  it('should create a new datasource', () => {
    cy.visit('/datasources')
    cy.get('[data-cy=create-datasource]').click()
    cy.get('[data-cy=datasource-name]').type('Test Datasource')
    cy.get('[data-cy=plugin-selector]').click()
    cy.get('[data-cy=mysql-plugin]').click()
    cy.get('[data-cy=submit-button]').click()
    cy.contains('数据源创建成功')
  })
})
```

## 部署配置设计

### 环境变量配置
```bash
# .env.development
VITE_API_BASE_URL=http://localhost:8080/api
VITE_MOCK_ENABLED=true
VITE_APP_TITLE=数据源管理系统

# .env.production
VITE_API_BASE_URL=/api
VITE_MOCK_ENABLED=false
VITE_APP_TITLE=数据源管理系统
```

### Vite配置
```javascript
// vite.config.js
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true
      }
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          ui: ['element-plus']
        }
      }
    }
  }
})
```

这个设计文档提供了Vue3前端项目的完整技术架构和实现方案，包括组件设计、状态管理、API模拟、样式规范、错误处理、性能优化和测试策略等各个方面的详细设计。