"use strict";(self.webpackChunkpageplug_datasource_frontend=self.webpackChunkpageplug_datasource_frontend||[]).push([[17,662],{241:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval("{/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   D: () => (/* binding */ useTheme),\n/* harmony export */   N: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(6070);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(212);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(3017);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_2__);\n\r\n\r\n\r\nconst lightTheme = {\r\n    colors: {\r\n        primary: '#1890ff',\r\n        secondary: '#6c757d',\r\n        success: '#52c41a',\r\n        warning: '#faad14',\r\n        error: '#ff4d4f',\r\n        info: '#1890ff',\r\n        text: {\r\n            primary: '#262626',\r\n            secondary: '#595959',\r\n            disabled: '#bfbfbf'\r\n        },\r\n        background: {\r\n            primary: '#ffffff',\r\n            secondary: '#fafafa',\r\n            tertiary: '#f5f5f5'\r\n        },\r\n        border: {\r\n            primary: '#d9d9d9',\r\n            secondary: '#f0f0f0'\r\n        }\r\n    },\r\n    spacing: {\r\n        xs: '4px',\r\n        sm: '8px',\r\n        md: '16px',\r\n        lg: '24px',\r\n        xl: '32px',\r\n        xxl: '48px'\r\n    },\r\n    borderRadius: {\r\n        sm: '4px',\r\n        md: '6px',\r\n        lg: '8px'\r\n    },\r\n    shadows: {\r\n        sm: '0 2px 4px rgba(0, 0, 0, 0.06)',\r\n        md: '0 4px 12px rgba(0, 0, 0, 0.1)',\r\n        lg: '0 8px 24px rgba(0, 0, 0, 0.15)'\r\n    },\r\n    typography: {\r\n        fontFamily: \"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif\",\r\n        fontSize: {\r\n            xs: '12px',\r\n            sm: '14px',\r\n            md: '16px',\r\n            lg: '18px',\r\n            xl: '20px'\r\n        },\r\n        fontWeight: {\r\n            normal: 400,\r\n            medium: 500,\r\n            semibold: 600,\r\n            bold: 700\r\n        }\r\n    }\r\n};\r\nconst darkTheme = {\r\n    ...lightTheme,\r\n    colors: {\r\n        ...lightTheme.colors,\r\n        text: {\r\n            primary: '#ffffff',\r\n            secondary: '#d9d9d9',\r\n            disabled: '#595959'\r\n        },\r\n        background: {\r\n            primary: '#1f1f1f',\r\n            secondary: '#262626',\r\n            tertiary: '#141414'\r\n        },\r\n        border: {\r\n            primary: '#434343',\r\n            secondary: '#303030'\r\n        }\r\n    }\r\n};\r\nconst ThemeContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\r\nconst ThemeProvider = ({ children }) => {\r\n    const [isDark, setIsDark] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(() => {\r\n        const saved = localStorage.getItem('datasource-app-theme');\r\n        if (saved) {\r\n            return saved === 'dark';\r\n        }\r\n        return window.matchMedia('(prefers-color-scheme: dark)').matches;\r\n    });\r\n    const theme = isDark ? darkTheme : lightTheme;\r\n    const toggleTheme = () => {\r\n        setIsDark(prev => {\r\n            const newValue = !prev;\r\n            localStorage.setItem('datasource-app-theme', newValue ? 'dark' : 'light');\r\n            return newValue;\r\n        });\r\n    };\r\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\r\n        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\r\n        const handleChange = (e) => {\r\n            const saved = localStorage.getItem('datasource-app-theme');\r\n            if (!saved) {\r\n                setIsDark(e.matches);\r\n            }\r\n        };\r\n        mediaQuery.addEventListener('change', handleChange);\r\n        return () => mediaQuery.removeEventListener('change', handleChange);\r\n    }, []);\r\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\r\n        const root = document.documentElement;\r\n        if (isDark) {\r\n            root.classList.add('dark');\r\n        }\r\n        else {\r\n            root.classList.remove('dark');\r\n        }\r\n    }, [isDark]);\r\n    const contextValue = {\r\n        theme,\r\n        isDark,\r\n        toggleTheme\r\n    };\r\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(ThemeContext.Provider, { value: contextValue, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(styled_components__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, { theme: theme, children: children }) }));\r\n};\r\nconst useTheme = () => {\r\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\r\n    if (context === undefined) {\r\n        throw new Error('useTheme must be used within a ThemeProvider');\r\n    }\r\n    return context;\r\n};\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///241\n\n}")},1664:Q=>{Q.exports="data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 20 20%27%3e%3cpath stroke=%27%236b7280%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%271.5%27 d=%27M6 8l4 4 4-4%27/%3e%3c/svg%3e"},1833:(module,__webpack_exports__,__webpack_require__)=>{eval("{/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_pnpm_css_loader_6_11_0_webpack_5_100_2_node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(6421);\n/* harmony import */ var _node_modules_pnpm_css_loader_6_11_0_webpack_5_100_2_node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_pnpm_css_loader_6_11_0_webpack_5_100_2_node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_pnpm_css_loader_6_11_0_webpack_5_100_2_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(9859);\n/* harmony import */ var _node_modules_pnpm_css_loader_6_11_0_webpack_5_100_2_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_pnpm_css_loader_6_11_0_webpack_5_100_2_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _node_modules_pnpm_css_loader_6_11_0_webpack_5_100_2_node_modules_css_loader_dist_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(6954);\n/* harmony import */ var _node_modules_pnpm_css_loader_6_11_0_webpack_5_100_2_node_modules_css_loader_dist_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_node_modules_pnpm_css_loader_6_11_0_webpack_5_100_2_node_modules_css_loader_dist_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_2__);\n// Imports\n\n\n\nvar ___CSS_LOADER_URL_IMPORT_0___ = new URL(/* asset import */ __webpack_require__(1664), __webpack_require__.b);\nvar ___CSS_LOADER_URL_IMPORT_1___ = new URL(/* asset import */ __webpack_require__(2031), __webpack_require__.b);\nvar ___CSS_LOADER_URL_IMPORT_2___ = new URL(/* asset import */ __webpack_require__(5270), __webpack_require__.b);\nvar ___CSS_LOADER_URL_IMPORT_3___ = new URL(/* asset import */ __webpack_require__(3569), __webpack_require__.b);\nvar ___CSS_LOADER_EXPORT___ = _node_modules_pnpm_css_loader_6_11_0_webpack_5_100_2_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default()((_node_modules_pnpm_css_loader_6_11_0_webpack_5_100_2_node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default()));\nvar ___CSS_LOADER_URL_REPLACEMENT_0___ = _node_modules_pnpm_css_loader_6_11_0_webpack_5_100_2_node_modules_css_loader_dist_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_2___default()(___CSS_LOADER_URL_IMPORT_0___);\nvar ___CSS_LOADER_URL_REPLACEMENT_1___ = _node_modules_pnpm_css_loader_6_11_0_webpack_5_100_2_node_modules_css_loader_dist_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_2___default()(___CSS_LOADER_URL_IMPORT_1___);\nvar ___CSS_LOADER_URL_REPLACEMENT_2___ = _node_modules_pnpm_css_loader_6_11_0_webpack_5_100_2_node_modules_css_loader_dist_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_2___default()(___CSS_LOADER_URL_IMPORT_2___);\nvar ___CSS_LOADER_URL_REPLACEMENT_3___ = _node_modules_pnpm_css_loader_6_11_0_webpack_5_100_2_node_modules_css_loader_dist_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_2___default()(___CSS_LOADER_URL_IMPORT_3___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `/* TailwindCSS 基础样式 */\n*, ::before, ::after {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}\n::backdrop {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}\n/* ! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com */\n/*\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\n*/\n*,\n::before,\n::after {\n  box-sizing: border-box; /* 1 */\n  border-width: 0; /* 2 */\n  border-style: solid; /* 2 */\n  border-color: #e5e7eb; /* 2 */\n}\n::before,\n::after {\n  --tw-content: '';\n}\n/*\n1. Use a consistent sensible line-height in all browsers.\n2. Prevent adjustments of font size after orientation changes in iOS.\n3. Use a more readable tab size.\n4. Use the user's configured \\`sans\\` font-family by default.\n5. Use the user's configured \\`sans\\` font-feature-settings by default.\n6. Use the user's configured \\`sans\\` font-variation-settings by default.\n7. Disable tap highlights on iOS\n*/\nhtml,\n:host {\n  line-height: 1.5; /* 1 */\n  -webkit-text-size-adjust: 100%; /* 2 */ /* 3 */\n  tab-size: 4; /* 3 */\n  font-family: Inter, system-ui, sans-serif; /* 4 */\n  font-feature-settings: normal; /* 5 */\n  font-variation-settings: normal; /* 6 */\n  -webkit-tap-highlight-color: transparent; /* 7 */\n}\n/*\n1. Remove the margin in all browsers.\n2. Inherit line-height from \\`html\\` so users can set them as a class directly on the \\`html\\` element.\n*/\nbody {\n  margin: 0; /* 1 */\n  line-height: inherit; /* 2 */\n}\n/*\n1. Add the correct height in Firefox.\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\n3. Ensure horizontal rules are visible by default.\n*/\nhr {\n  height: 0; /* 1 */\n  color: inherit; /* 2 */\n  border-top-width: 1px; /* 3 */\n}\n/*\nAdd the correct text decoration in Chrome, Edge, and Safari.\n*/\nabbr:where([title]) {\n  -webkit-text-decoration: underline dotted;\n          text-decoration: underline dotted;\n}\n/*\nRemove the default font size and weight for headings.\n*/\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  font-size: inherit;\n  font-weight: inherit;\n}\n/*\nReset links to optimize for opt-in styling instead of opt-out.\n*/\na {\n  color: inherit;\n  text-decoration: inherit;\n}\n/*\nAdd the correct font weight in Edge and Safari.\n*/\nb,\nstrong {\n  font-weight: bolder;\n}\n/*\n1. Use the user's configured \\`mono\\` font-family by default.\n2. Use the user's configured \\`mono\\` font-feature-settings by default.\n3. Use the user's configured \\`mono\\` font-variation-settings by default.\n4. Correct the odd \\`em\\` font sizing in all browsers.\n*/\ncode,\nkbd,\nsamp,\npre {\n  font-family: JetBrains Mono, Consolas, monospace; /* 1 */\n  font-feature-settings: normal; /* 2 */\n  font-variation-settings: normal; /* 3 */\n  font-size: 1em; /* 4 */\n}\n/*\nAdd the correct font size in all browsers.\n*/\nsmall {\n  font-size: 80%;\n}\n/*\nPrevent \\`sub\\` and \\`sup\\` elements from affecting the line height in all browsers.\n*/\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\nsub {\n  bottom: -0.25em;\n}\nsup {\n  top: -0.5em;\n}\n/*\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\n3. Remove gaps between table borders by default.\n*/\ntable {\n  text-indent: 0; /* 1 */\n  border-color: inherit; /* 2 */\n  border-collapse: collapse; /* 3 */\n}\n/*\n1. Change the font styles in all browsers.\n2. Remove the margin in Firefox and Safari.\n3. Remove default padding in all browsers.\n*/\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  font-family: inherit; /* 1 */\n  font-feature-settings: inherit; /* 1 */\n  font-variation-settings: inherit; /* 1 */\n  font-size: 100%; /* 1 */\n  font-weight: inherit; /* 1 */\n  line-height: inherit; /* 1 */\n  letter-spacing: inherit; /* 1 */\n  color: inherit; /* 1 */\n  margin: 0; /* 2 */\n  padding: 0; /* 3 */\n}\n/*\nRemove the inheritance of text transform in Edge and Firefox.\n*/\nbutton,\nselect {\n  text-transform: none;\n}\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Remove default button styles.\n*/\nbutton,\ninput:where([type='button']),\ninput:where([type='reset']),\ninput:where([type='submit']) {\n  -webkit-appearance: button; /* 1 */\n  background-color: transparent; /* 2 */\n  background-image: none; /* 2 */\n}\n/*\nUse the modern Firefox focus style for all focusable elements.\n*/\n:-moz-focusring {\n  outline: auto;\n}\n/*\nRemove the additional \\`:invalid\\` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\n*/\n:-moz-ui-invalid {\n  box-shadow: none;\n}\n/*\nAdd the correct vertical alignment in Chrome and Firefox.\n*/\nprogress {\n  vertical-align: baseline;\n}\n/*\nCorrect the cursor style of increment and decrement buttons in Safari.\n*/\n::-webkit-inner-spin-button,\n::-webkit-outer-spin-button {\n  height: auto;\n}\n/*\n1. Correct the odd appearance in Chrome and Safari.\n2. Correct the outline style in Safari.\n*/\n[type='search'] {\n  -webkit-appearance: textfield; /* 1 */\n  outline-offset: -2px; /* 2 */\n}\n/*\nRemove the inner padding in Chrome and Safari on macOS.\n*/\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Change font properties to \\`inherit\\` in Safari.\n*/\n::-webkit-file-upload-button {\n  -webkit-appearance: button; /* 1 */\n  font: inherit; /* 2 */\n}\n/*\nAdd the correct display in Chrome and Safari.\n*/\nsummary {\n  display: list-item;\n}\n/*\nRemoves the default spacing and border for appropriate elements.\n*/\nblockquote,\ndl,\ndd,\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\nhr,\nfigure,\np,\npre {\n  margin: 0;\n}\nfieldset {\n  margin: 0;\n  padding: 0;\n}\nlegend {\n  padding: 0;\n}\nol,\nul,\nmenu {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n/*\nReset default styling for dialogs.\n*/\ndialog {\n  padding: 0;\n}\n/*\nPrevent resizing textareas horizontally by default.\n*/\ntextarea {\n  resize: vertical;\n}\n/*\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\n2. Set the default placeholder color to the user's configured gray 400 color.\n*/\ninput::placeholder,\ntextarea::placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\n/*\nSet the default cursor for buttons.\n*/\nbutton,\n[role=\"button\"] {\n  cursor: pointer;\n}\n/*\nMake sure disabled buttons don't get the pointer cursor.\n*/\n:disabled {\n  cursor: default;\n}\n/*\n1. Make replaced elements \\`display: block\\` by default. (https://github.com/mozdevs/cssremedy/issues/14)\n2. Add \\`vertical-align: middle\\` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\n   This can trigger a poorly considered lint error in some tools but is included by design.\n*/\nimg,\nsvg,\nvideo,\ncanvas,\naudio,\niframe,\nembed,\nobject {\n  display: block; /* 1 */\n  vertical-align: middle; /* 2 */\n}\n/*\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\n*/\nimg,\nvideo {\n  max-width: 100%;\n  height: auto;\n}\n/* Make elements with the HTML hidden attribute stay hidden by default */\n[hidden]:where(:not([hidden=\"until-found\"])) {\n  display: none;\n}\n[type='text'],input:where(:not([type])),[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {\n  appearance: none;\n  background-color: #fff;\n  border-color: #6b7280;\n  border-width: 1px;\n  border-radius: 0px;\n  padding-top: 0.5rem;\n  padding-right: 0.75rem;\n  padding-bottom: 0.5rem;\n  padding-left: 0.75rem;\n  font-size: 1rem;\n  line-height: 1.5rem;\n  --tw-shadow: 0 0 #0000;\n}\n[type='text']:focus, input:where(:not([type])):focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: #2563eb;\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  border-color: #2563eb;\n}\ninput::placeholder,textarea::placeholder {\n  color: #6b7280;\n  opacity: 1;\n}\n::-webkit-datetime-edit-fields-wrapper {\n  padding: 0;\n}\n::-webkit-date-and-time-value {\n  min-height: 1.5em;\n  text-align: inherit;\n}\n::-webkit-datetime-edit {\n  display: inline-flex;\n}\n::-webkit-datetime-edit,::-webkit-datetime-edit-year-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-meridiem-field {\n  padding-top: 0;\n  padding-bottom: 0;\n}\nselect {\n  background-image: url(${___CSS_LOADER_URL_REPLACEMENT_0___});\n  background-position: right 0.5rem center;\n  background-repeat: no-repeat;\n  background-size: 1.5em 1.5em;\n  padding-right: 2.5rem;\n  -webkit-print-color-adjust: exact;\n          print-color-adjust: exact;\n}\n[multiple],[size]:where(select:not([size=\"1\"])) {\n  background-image: initial;\n  background-position: initial;\n  background-repeat: unset;\n  background-size: initial;\n  padding-right: 0.75rem;\n  -webkit-print-color-adjust: unset;\n          print-color-adjust: unset;\n}\n[type='checkbox'],[type='radio'] {\n  appearance: none;\n  padding: 0;\n  -webkit-print-color-adjust: exact;\n          print-color-adjust: exact;\n  display: inline-block;\n  vertical-align: middle;\n  background-origin: border-box;\n  -webkit-user-select: none;\n          user-select: none;\n  flex-shrink: 0;\n  height: 1rem;\n  width: 1rem;\n  color: #2563eb;\n  background-color: #fff;\n  border-color: #6b7280;\n  border-width: 1px;\n  --tw-shadow: 0 0 #0000;\n}\n[type='checkbox'] {\n  border-radius: 0px;\n}\n[type='radio'] {\n  border-radius: 100%;\n}\n[type='checkbox']:focus,[type='radio']:focus {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);\n  --tw-ring-offset-width: 2px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: #2563eb;\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n}\n[type='checkbox']:checked,[type='radio']:checked {\n  border-color: transparent;\n  background-color: currentColor;\n  background-size: 100% 100%;\n  background-position: center;\n  background-repeat: no-repeat;\n}\n[type='checkbox']:checked {\n  background-image: url(${___CSS_LOADER_URL_REPLACEMENT_1___});\n}\n@media (forced-colors: active)  {\n  [type='checkbox']:checked {\n    appearance: auto;\n  }\n}\n[type='radio']:checked {\n  background-image: url(${___CSS_LOADER_URL_REPLACEMENT_2___});\n}\n@media (forced-colors: active)  {\n  [type='radio']:checked {\n    appearance: auto;\n  }\n}\n[type='checkbox']:checked:hover,[type='checkbox']:checked:focus,[type='radio']:checked:hover,[type='radio']:checked:focus {\n  border-color: transparent;\n  background-color: currentColor;\n}\n[type='checkbox']:indeterminate {\n  background-image: url(${___CSS_LOADER_URL_REPLACEMENT_3___});\n  border-color: transparent;\n  background-color: currentColor;\n  background-size: 100% 100%;\n  background-position: center;\n  background-repeat: no-repeat;\n}\n@media (forced-colors: active)  {\n  [type='checkbox']:indeterminate {\n    appearance: auto;\n  }\n}\n[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {\n  border-color: transparent;\n  background-color: currentColor;\n}\n[type='file'] {\n  background: unset;\n  border-color: inherit;\n  border-width: 0;\n  border-radius: 0;\n  padding: 0;\n  font-size: unset;\n  line-height: inherit;\n}\n[type='file']:focus {\n  outline: 1px solid ButtonText;\n  outline: 1px auto -webkit-focus-ring-color;\n}\n\n/* 全局样式重置 */\n* {\n  box-sizing: border-box;\n  margin: 0;\n  padding: 0;\n}\n\nhtml {\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  font-size: 14px;\n  line-height: 1.5;\n}\n\nbody {\n  margin: 0;\n  background-color: #f5f5f5;\n  color: #333;\n}\n\n/* 代码字体 */\ncode {\n  font-family: 'JetBrains Mono', 'Consolas', 'Monaco', 'Courier New', monospace;\n}\n\n/* 滚动条样式 */\n::-webkit-scrollbar {\n  width: 8px;\n  height: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n/* Ant Design 样式覆盖 */\n.ant-layout {\n  background: transparent !important;\n}\n\n.ant-layout-header {\n  background: #fff !important;\n  border-bottom: 1px solid #f0f0f0;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  padding: 0 24px;\n  height: 64px;\n  line-height: 64px;\n}\n\n.ant-layout-sider {\n  background: #fff !important;\n  border-right: 1px solid #f0f0f0;\n}\n\n.ant-menu {\n  border-right: none !important;\n}\n\n.ant-menu-item {\n  margin: 4px 8px !important;\n  border-radius: 6px !important;\n  width: auto !important;\n}\n\n.ant-menu-item-selected {\n  background-color: #e6f7ff !important;\n  color: #1890ff !important;\n}\n\n.ant-menu-item:hover {\n  background-color: #f5f5f5 !important;\n}\n\n/* 表格样式优化 */\n.ant-table {\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n.ant-table-thead > tr > th {\n  background-color: #fafafa;\n  border-bottom: 1px solid #f0f0f0;\n  font-weight: 600;\n}\n\n.ant-table-tbody > tr:hover > td {\n  background-color: #f5f5f5;\n}\n\n/* 表单样式优化 */\n.ant-form-item-label > label {\n  font-weight: 500;\n}\n\n.ant-input,\n.ant-select-selector,\n.ant-input-number {\n  border-radius: 6px;\n}\n\n.ant-input:focus,\n.ant-select-focused .ant-select-selector,\n.ant-input-number:focus {\n  border-color: #40a9ff;\n  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n}\n\n/* 按钮样式优化 */\n.ant-btn {\n  border-radius: 6px;\n  font-weight: 500;\n  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.02);\n}\n\n.ant-btn-primary {\n  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);\n  border: none;\n}\n\n.ant-btn-primary:hover {\n  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);\n}\n\n/* 卡片样式优化 */\n.ant-card {\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  border: 1px solid #f0f0f0;\n}\n\n.ant-card-head {\n  border-bottom: 1px solid #f0f0f0;\n  background-color: #fafafa;\n}\n\n/* 模态框样式优化 */\n.ant-modal {\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n.ant-modal-header {\n  background-color: #fafafa;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n/* 标签样式优化 */\n.ant-tag {\n  border-radius: 4px;\n  font-weight: 500;\n}\n\n/* 通知样式优化 */\n.ant-notification {\n  border-radius: 8px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n/* 自定义工具类 */\n.tw-fade-in {\n  animation: fadeIn 0.3s ease-in-out;\n}\n\n.tw-slide-up {\n  animation: slideUp 0.3s ease-out;\n}\n\n.tw-hover-lift {\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n}\n\n.tw-hover-lift:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n/* 响应式工具类 */\n@media (max-width: 768px) {\n  .ant-layout-content {\n    padding: 16px;\n  }\n  \n  .ant-table {\n    font-size: 12px;\n  }\n  \n  .ant-btn {\n    font-size: 12px;\n    height: 32px;\n    padding: 0 12px;\n  }\n}\n\n/* 暗色主题支持 */\n.dark {\n  .ant-layout-header {\n    background: #1f1f1f !important;\n    border-bottom-color: #303030;\n  }\n  \n  .ant-layout-sider {\n    background: #1f1f1f !important;\n    border-right-color: #303030;\n  }\n  \n  .ant-menu {\n    background: transparent !important;\n    color: #fff;\n  }\n  \n  .ant-menu-item {\n    color: #fff;\n  }\n  \n  .ant-menu-item:hover {\n    background-color: #262626 !important;\n  }\n  \n  .ant-menu-item-selected {\n    background-color: #1890ff !important;\n  }\n}\n`, \"\",{\"version\":3,\"sources\":[\"webpack://./src/styles/index.css\"],\"names\":[],\"mappings\":\"AAAA,qBAAqB;AACrB;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;AAAd,kEAAc;AAAd;;;CAAc;AAAd;;;EAAA,sBAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,mBAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;AAAd;;EAAA,gBAAc;AAAA;AAAd;;;;;;;;CAAc;AAAd;;EAAA,gBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc,EAAd,MAAc;EAAd,WAAc,EAAd,MAAc;EAAd,yCAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,wCAAc,EAAd,MAAc;AAAA;AAAd;;;CAAc;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;AAAd;;;;CAAc;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;AAAd;;CAAc;AAAd;EAAA,yCAAc;UAAd,iCAAc;AAAA;AAAd;;CAAc;AAAd;;;;;;EAAA,kBAAc;EAAd,oBAAc;AAAA;AAAd;;CAAc;AAAd;EAAA,cAAc;EAAd,wBAAc;AAAA;AAAd;;CAAc;AAAd;;EAAA,mBAAc;AAAA;AAAd;;;;;CAAc;AAAd;;;;EAAA,gDAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;AAAd;;CAAc;AAAd;EAAA,cAAc;AAAA;AAAd;;CAAc;AAAd;;EAAA,cAAc;EAAd,cAAc;EAAd,kBAAc;EAAd,wBAAc;AAAA;AAAd;EAAA,eAAc;AAAA;AAAd;EAAA,WAAc;AAAA;AAAd;;;;CAAc;AAAd;EAAA,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;EAAd,yBAAc,EAAd,MAAc;AAAA;AAAd;;;;CAAc;AAAd;;;;;EAAA,oBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gCAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,uBAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,SAAc,EAAd,MAAc;EAAd,UAAc,EAAd,MAAc;AAAA;AAAd;;CAAc;AAAd;;EAAA,oBAAc;AAAA;AAAd;;;CAAc;AAAd;;;;EAAA,0BAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;AAAd;;CAAc;AAAd;EAAA,aAAc;AAAA;AAAd;;CAAc;AAAd;EAAA,gBAAc;AAAA;AAAd;;CAAc;AAAd;EAAA,wBAAc;AAAA;AAAd;;CAAc;AAAd;;EAAA,YAAc;AAAA;AAAd;;;CAAc;AAAd;EAAA,6BAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;AAAd;;CAAc;AAAd;EAAA,wBAAc;AAAA;AAAd;;;CAAc;AAAd;EAAA,0BAAc,EAAd,MAAc;EAAd,aAAc,EAAd,MAAc;AAAA;AAAd;;CAAc;AAAd;EAAA,kBAAc;AAAA;AAAd;;CAAc;AAAd;;;;;;;;;;;;;EAAA,SAAc;AAAA;AAAd;EAAA,SAAc;EAAd,UAAc;AAAA;AAAd;EAAA,UAAc;AAAA;AAAd;;;EAAA,gBAAc;EAAd,SAAc;EAAd,UAAc;AAAA;AAAd;;CAAc;AAAd;EAAA,UAAc;AAAA;AAAd;;CAAc;AAAd;EAAA,gBAAc;AAAA;AAAd;;;CAAc;AAAd;;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;AAAd;;CAAc;AAAd;;EAAA,eAAc;AAAA;AAAd;;CAAc;AAAd;EAAA,eAAc;AAAA;AAAd;;;;CAAc;AAAd;;;;;;;;EAAA,cAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;AAAd;;CAAc;AAAd;;EAAA,eAAc;EAAd,YAAc;AAAA;AAAd,wEAAc;AAAd;EAAA,aAAc;AAAA;AAAd;EAAA,gBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,mBAAc;EAAd,sBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd,eAAc;EAAd,mBAAc;EAAd,sBAAc;AAAA;AAAd;EAAA,8BAAc;EAAd,mBAAc;EAAd,4CAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,wBAAc;EAAd,2GAAc;EAAd,yGAAc;EAAd,iFAAc;EAAd;AAAc;AAAd;EAAA,cAAc;EAAd;AAAc;AAAd;EAAA;AAAc;AAAd;EAAA,iBAAc;EAAd;AAAc;AAAd;EAAA;AAAc;AAAd;EAAA,cAAc;EAAd;AAAc;AAAd;EAAA,yDAAc;EAAd,wCAAc;EAAd,4BAAc;EAAd,4BAAc;EAAd,qBAAc;EAAd,iCAAc;UAAd;AAAc;AAAd;EAAA,yBAAc;EAAd,4BAAc;EAAd,wBAAc;EAAd,wBAAc;EAAd,sBAAc;EAAd,iCAAc;UAAd;AAAc;AAAd;EAAA,gBAAc;EAAd,UAAc;EAAd,iCAAc;UAAd,yBAAc;EAAd,qBAAc;EAAd,sBAAc;EAAd,6BAAc;EAAd,yBAAc;UAAd,iBAAc;EAAd,cAAc;EAAd,YAAc;EAAd,WAAc;EAAd,cAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd,iBAAc;EAAd;AAAc;AAAd;EAAA;AAAc;AAAd;EAAA;AAAc;AAAd;EAAA,8BAAc;EAAd,mBAAc;EAAd,4CAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,wBAAc;EAAd,2GAAc;EAAd,yGAAc;EAAd;AAAc;AAAd;EAAA,yBAAc;EAAd,8BAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd;AAAc;AAAd;EAAA,yDAAc;AAAA;AAAd;EAAA;IAAA;EAAc;AAAA;AAAd;EAAA,yDAAc;AAAA;AAAd;EAAA;IAAA;EAAc;AAAA;AAAd;EAAA,yBAAc;EAAd;AAAc;AAAd;EAAA,yDAAc;EAAd,yBAAc;EAAd,8BAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,4BAAc;AAAA;AAAd;EAAA;IAAA;EAAc;AAAA;AAAd;EAAA,yBAAc;EAAd;AAAc;AAAd;EAAA,iBAAc;EAAd,qBAAc;EAAd,eAAc;EAAd,gBAAc;EAAd,UAAc;EAAd,gBAAc;EAAd;AAAc;AAAd;EAAA,6BAAc;EAAd;AAAc;;AAId,WAAW;AACX;EACE,sBAAsB;EACtB,SAAS;EACT,UAAU;AACZ;;AAEA;EACE;kFACgF;EAChF,mCAAmC;EACnC,kCAAkC;EAClC,eAAe;EACf,gBAAgB;AAClB;;AAEA;EACE,SAAS;EACT,yBAAyB;EACzB,WAAW;AACb;;AAEA,SAAS;AACT;EACE,6EAA6E;AAC/E;;AAEA,UAAU;AACV;EACE,UAAU;EACV,WAAW;AACb;;AAEA;EACE,mBAAmB;EACnB,kBAAkB;AACpB;;AAEA;EACE,mBAAmB;EACnB,kBAAkB;AACpB;;AAEA;EACE,mBAAmB;AACrB;;AAEA,oBAAoB;AACpB;EACE,kCAAkC;AACpC;;AAEA;EACE,2BAA2B;EAC3B,gCAAgC;EAChC,yCAAyC;EACzC,eAAe;EACf,YAAY;EACZ,iBAAiB;AACnB;;AAEA;EACE,2BAA2B;EAC3B,+BAA+B;AACjC;;AAEA;EACE,6BAA6B;AAC/B;;AAEA;EACE,0BAA0B;EAC1B,6BAA6B;EAC7B,sBAAsB;AACxB;;AAEA;EACE,oCAAoC;EACpC,yBAAyB;AAC3B;;AAEA;EACE,oCAAoC;AACtC;;AAEA,WAAW;AACX;EACE,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,yBAAyB;EACzB,gCAAgC;EAChC,gBAAgB;AAClB;;AAEA;EACE,yBAAyB;AAC3B;;AAEA,WAAW;AACX;EACE,gBAAgB;AAClB;;AAEA;;;EAGE,kBAAkB;AACpB;;AAEA;;;EAGE,qBAAqB;EACrB,6CAA6C;AAC/C;;AAEA,WAAW;AACX;EACE,kBAAkB;EAClB,gBAAgB;EAChB,uCAAuC;AACzC;;AAEA;EACE,6DAA6D;EAC7D,YAAY;AACd;;AAEA;EACE,6DAA6D;EAC7D,2BAA2B;EAC3B,8CAA8C;AAChD;;AAEA,WAAW;AACX;EACE,kBAAkB;EAClB,yCAAyC;EACzC,yBAAyB;AAC3B;;AAEA;EACE,gCAAgC;EAChC,yBAAyB;AAC3B;;AAEA,YAAY;AACZ;EACE,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,yBAAyB;EACzB,gCAAgC;AAClC;;AAEA,WAAW;AACX;EACE,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA,WAAW;AACX;EACE,kBAAkB;EAClB,0CAA0C;AAC5C;;AAEA,WAAW;AACX;EACE,kCAAkC;AACpC;;AAEA;EACE,gCAAgC;AAClC;;AAEA;EACE,qDAAqD;AACvD;;AAEA;EACE,2BAA2B;EAC3B,yCAAyC;AAC3C;;AAEA,WAAW;AACX;EACE;IACE,aAAa;EACf;;EAEA;IACE,eAAe;EACjB;;EAEA;IACE,eAAe;IACf,YAAY;IACZ,eAAe;EACjB;AACF;;AAEA,WAAW;AACX;EACE;IACE,8BAA8B;IAC9B,4BAA4B;EAC9B;;EAEA;IACE,8BAA8B;IAC9B,2BAA2B;EAC7B;;EAEA;IACE,kCAAkC;IAClC,WAAW;EACb;;EAEA;IACE,WAAW;EACb;;EAEA;IACE,oCAAoC;EACtC;;EAEA;IACE,oCAAoC;EACtC;AACF\",\"sourcesContent\":[\"/* TailwindCSS 基础样式 */\\n@tailwind base;\\n@tailwind components;\\n@tailwind utilities;\\n\\n/* 全局样式重置 */\\n* {\\n  box-sizing: border-box;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nhtml {\\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n  font-size: 14px;\\n  line-height: 1.5;\\n}\\n\\nbody {\\n  margin: 0;\\n  background-color: #f5f5f5;\\n  color: #333;\\n}\\n\\n/* 代码字体 */\\ncode {\\n  font-family: 'JetBrains Mono', 'Consolas', 'Monaco', 'Courier New', monospace;\\n}\\n\\n/* 滚动条样式 */\\n::-webkit-scrollbar {\\n  width: 8px;\\n  height: 8px;\\n}\\n\\n::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 4px;\\n}\\n\\n::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 4px;\\n}\\n\\n::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n\\n/* Ant Design 样式覆盖 */\\n.ant-layout {\\n  background: transparent !important;\\n}\\n\\n.ant-layout-header {\\n  background: #fff !important;\\n  border-bottom: 1px solid #f0f0f0;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\\n  padding: 0 24px;\\n  height: 64px;\\n  line-height: 64px;\\n}\\n\\n.ant-layout-sider {\\n  background: #fff !important;\\n  border-right: 1px solid #f0f0f0;\\n}\\n\\n.ant-menu {\\n  border-right: none !important;\\n}\\n\\n.ant-menu-item {\\n  margin: 4px 8px !important;\\n  border-radius: 6px !important;\\n  width: auto !important;\\n}\\n\\n.ant-menu-item-selected {\\n  background-color: #e6f7ff !important;\\n  color: #1890ff !important;\\n}\\n\\n.ant-menu-item:hover {\\n  background-color: #f5f5f5 !important;\\n}\\n\\n/* 表格样式优化 */\\n.ant-table {\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n\\n.ant-table-thead > tr > th {\\n  background-color: #fafafa;\\n  border-bottom: 1px solid #f0f0f0;\\n  font-weight: 600;\\n}\\n\\n.ant-table-tbody > tr:hover > td {\\n  background-color: #f5f5f5;\\n}\\n\\n/* 表单样式优化 */\\n.ant-form-item-label > label {\\n  font-weight: 500;\\n}\\n\\n.ant-input,\\n.ant-select-selector,\\n.ant-input-number {\\n  border-radius: 6px;\\n}\\n\\n.ant-input:focus,\\n.ant-select-focused .ant-select-selector,\\n.ant-input-number:focus {\\n  border-color: #40a9ff;\\n  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\\n}\\n\\n/* 按钮样式优化 */\\n.ant-btn {\\n  border-radius: 6px;\\n  font-weight: 500;\\n  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.02);\\n}\\n\\n.ant-btn-primary {\\n  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);\\n  border: none;\\n}\\n\\n.ant-btn-primary:hover {\\n  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);\\n}\\n\\n/* 卡片样式优化 */\\n.ant-card {\\n  border-radius: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\\n  border: 1px solid #f0f0f0;\\n}\\n\\n.ant-card-head {\\n  border-bottom: 1px solid #f0f0f0;\\n  background-color: #fafafa;\\n}\\n\\n/* 模态框样式优化 */\\n.ant-modal {\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n\\n.ant-modal-header {\\n  background-color: #fafafa;\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n\\n/* 标签样式优化 */\\n.ant-tag {\\n  border-radius: 4px;\\n  font-weight: 500;\\n}\\n\\n/* 通知样式优化 */\\n.ant-notification {\\n  border-radius: 8px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n}\\n\\n/* 自定义工具类 */\\n.tw-fade-in {\\n  animation: fadeIn 0.3s ease-in-out;\\n}\\n\\n.tw-slide-up {\\n  animation: slideUp 0.3s ease-out;\\n}\\n\\n.tw-hover-lift {\\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\\n}\\n\\n.tw-hover-lift:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n\\n/* 响应式工具类 */\\n@media (max-width: 768px) {\\n  .ant-layout-content {\\n    padding: 16px;\\n  }\\n  \\n  .ant-table {\\n    font-size: 12px;\\n  }\\n  \\n  .ant-btn {\\n    font-size: 12px;\\n    height: 32px;\\n    padding: 0 12px;\\n  }\\n}\\n\\n/* 暗色主题支持 */\\n.dark {\\n  .ant-layout-header {\\n    background: #1f1f1f !important;\\n    border-bottom-color: #303030;\\n  }\\n  \\n  .ant-layout-sider {\\n    background: #1f1f1f !important;\\n    border-right-color: #303030;\\n  }\\n  \\n  .ant-menu {\\n    background: transparent !important;\\n    color: #fff;\\n  }\\n  \\n  .ant-menu-item {\\n    color: #fff;\\n  }\\n  \\n  .ant-menu-item:hover {\\n    background-color: #262626 !important;\\n  }\\n  \\n  .ant-menu-item-selected {\\n    background-color: #1890ff !important;\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///1833\n\n}")},2031:Q=>{Q.exports="data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3cpath d=%27M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z%27/%3e%3c/svg%3e"},3569:Q=>{Q.exports="data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 16 16%27%3e%3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27M4 8h8%27/%3e%3c/svg%3e"},5270:Q=>{Q.exports="data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3ccircle cx=%278%27 cy=%278%27 r=%273%27/%3e%3c/svg%3e"},9662:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval('{// ESM COMPAT FLAG\n__webpack_require__.r(__webpack_exports__);\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  mount: () => (/* binding */ mount)\n});\n\n// EXTERNAL MODULE: ./node_modules/.pnpm/react@18.3.1/node_modules/react/jsx-runtime.js\nvar jsx_runtime = __webpack_require__(6070);\n// EXTERNAL MODULE: consume shared module (default) react@^18.2.0 (singleton) (fallback: ./node_modules/.pnpm/react@18.3.1/node_modules/react/index.js)\nvar index_js_ = __webpack_require__(212);\nvar index_js_default = /*#__PURE__*/__webpack_require__.n(index_js_);\n// EXTERNAL MODULE: ./node_modules/.pnpm/react-dom@18.3.1_react@18.3.1/node_modules/react-dom/client.js\nvar client = __webpack_require__(9576);\n// EXTERNAL MODULE: consume shared module (default) react-redux@^8.0.5 (singleton) (fallback: ./node_modules/.pnpm/react-redux@8.1.3_@types+re_73b06c7c4ad3ae407d9e4b600f05c33c/node_modules/react-redux/es/index.js)\nvar es_index_js_ = __webpack_require__(8037);\n// EXTERNAL MODULE: consume shared module (default) react-router-dom@^6.8.1 (singleton) (fallback: ./node_modules/.pnpm/react-router-dom@6.30.1_rea_32183f923d2f77881bbb08a88c6f2afc/node_modules/react-router-dom/dist/index.js)\nvar dist_index_js_ = __webpack_require__(1299);\n// EXTERNAL MODULE: consume shared module (default) antd@^5.2.2 (singleton) (fallback: ./node_modules/.pnpm/antd@5.26.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/index.js)\nvar antd_es_index_js_ = __webpack_require__(8076);\n// EXTERNAL MODULE: ./node_modules/.pnpm/antd@5.26.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/lib/locale/zh_CN.js\nvar zh_CN = __webpack_require__(2446);\n// EXTERNAL MODULE: ./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\nvar dayjs_min = __webpack_require__(6287);\nvar dayjs_min_default = /*#__PURE__*/__webpack_require__.n(dayjs_min);\n// EXTERNAL MODULE: ./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/zh-cn.js\nvar zh_cn = __webpack_require__(5843);\n// EXTERNAL MODULE: consume shared module (default) styled-components@^5.3.6 (singleton) (fallback: ./node_modules/.pnpm/styled-components@5.3.11_@b_f452b29879fd00c325fa107cb54fffa1/node_modules/styled-components/dist/styled-components.browser.esm.js)\nvar styled_components_browser_esm_js_ = __webpack_require__(3017);\nvar styled_components_browser_esm_js_default = /*#__PURE__*/__webpack_require__.n(styled_components_browser_esm_js_);\n// EXTERNAL MODULE: ./src/components/Layout/AppHeader.tsx\nvar AppHeader = __webpack_require__(5520);\n// EXTERNAL MODULE: ./src/components/Layout/AppSidebar.tsx\nvar AppSidebar = __webpack_require__(635);\n// EXTERNAL MODULE: ./src/components/Common/LoadingSpinner.tsx\nvar LoadingSpinner = __webpack_require__(3128);\n// EXTERNAL MODULE: ./src/hooks/useTheme.ts\nvar useTheme = __webpack_require__(5869);\n// EXTERNAL MODULE: ./src/utils/codeSplitting.ts\nvar codeSplitting = __webpack_require__(3043);\n// EXTERNAL MODULE: ./src/utils/performance.ts\nvar utils_performance = __webpack_require__(5642);\n;// ./src/App.tsx\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nconst { Content } = antd_es_index_js_.Layout;\r\nconst AppContainer = (styled_components_browser_esm_js_default()).div `\n  min-height: 100vh;\n  background-color: ${props => props.$isDark ? \'#141414\' : \'#f5f5f5\'};\n  \n  .ant-layout {\n    background-color: transparent;\n  }\n  \n  .ant-layout-content {\n    padding: 24px;\n    margin: 0;\n    min-height: calc(100vh - 64px);\n  }\n`;\r\nconst ContentWrapper = (styled_components_browser_esm_js_default()).div `\n  max-width: 1200px;\n  margin: 0 auto;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n`;\r\nconst App = () => {\r\n    const { isDark } = (0,useTheme/* useTheme */.D)();\r\n    const location = (0,dist_index_js_.useLocation)();\r\n    (0,index_js_.useEffect)(() => {\r\n        (0,utils_performance/* initPerformanceMonitoring */.H3)();\r\n        (0,codeSplitting/* initCodeSplitting */.wA)();\r\n    }, []);\r\n    (0,index_js_.useEffect)(() => {\r\n        utils_performance/* performanceCollector */.Y5.recordMetric(\'route.change\', performance.now());\r\n        codeSplitting/* preloadStrategy */.PE.preloadByUserBehavior(location.pathname);\r\n    }, [location.pathname]);\r\n    return ((0,jsx_runtime.jsx)(AppContainer, { "$isDark": isDark, id: "datasource-app", children: (0,jsx_runtime.jsxs)(antd_es_index_js_.Layout, { children: [(0,jsx_runtime.jsx)(AppHeader/* AppHeader */.j, {}), (0,jsx_runtime.jsxs)(antd_es_index_js_.Layout, { hasSider: true, children: [(0,jsx_runtime.jsx)(AppSidebar/* AppSidebar */.G, {}), (0,jsx_runtime.jsx)(antd_es_index_js_.Layout, { children: (0,jsx_runtime.jsx)(Content, { children: (0,jsx_runtime.jsx)(ContentWrapper, { children: (0,jsx_runtime.jsx)(index_js_.Suspense, { fallback: (0,jsx_runtime.jsx)(LoadingSpinner/* LoadingSpinner */.kt, {}), children: (0,jsx_runtime.jsxs)(dist_index_js_.Routes, { children: [(0,jsx_runtime.jsx)(dist_index_js_.Route, { path: "/", element: (0,jsx_runtime.jsx)(dist_index_js_.Navigate, { to: "/datasources", replace: true }) }), (0,jsx_runtime.jsx)(dist_index_js_.Route, { path: "/datasources", element: (0,jsx_runtime.jsx)(codeSplitting/* RouteComponents */.IQ.DatasourceList, {}) }), (0,jsx_runtime.jsx)(dist_index_js_.Route, { path: "/datasources/create", element: (0,jsx_runtime.jsx)(codeSplitting/* RouteComponents */.IQ.DatasourceCreate, {}) }), (0,jsx_runtime.jsx)(dist_index_js_.Route, { path: "/datasources/:id", element: (0,jsx_runtime.jsx)(codeSplitting/* RouteComponents */.IQ.DatasourceDetail, {}) }), (0,jsx_runtime.jsx)(dist_index_js_.Route, { path: "/datasources/:id/edit", element: (0,jsx_runtime.jsx)(codeSplitting/* RouteComponents */.IQ.DatasourceCreate, {}) }), (0,jsx_runtime.jsx)(dist_index_js_.Route, { path: "/query", element: (0,jsx_runtime.jsx)(codeSplitting/* RouteComponents */.IQ.QueryEditor, {}) }), (0,jsx_runtime.jsx)(dist_index_js_.Route, { path: "/query/:datasourceId", element: (0,jsx_runtime.jsx)(codeSplitting/* RouteComponents */.IQ.QueryEditor, {}) }), (0,jsx_runtime.jsx)(dist_index_js_.Route, { path: "/plugins", element: (0,jsx_runtime.jsx)(codeSplitting/* RouteComponents */.IQ.PluginManagement, {}) }), (0,jsx_runtime.jsx)(dist_index_js_.Route, { path: "*", element: (0,jsx_runtime.jsx)(dist_index_js_.Navigate, { to: "/datasources", replace: true }) })] }) }) }) }) })] })] }) }));\r\n};\r\n/* harmony default export */ const src_App = (App);\r\n\n// EXTERNAL MODULE: consume shared module (default) @reduxjs/toolkit@^1.9.3 (singleton) (fallback: ./node_modules/.pnpm/@reduxjs+toolkit@1.9.7_reac_0fc5dba30cc2904499673508dc2413c2/node_modules/@reduxjs/toolkit/dist/redux-toolkit.esm.js)\nvar redux_toolkit_esm_js_ = __webpack_require__(9235);\n// EXTERNAL MODULE: ./src/store/slices/datasourceSlice.ts\nvar datasourceSlice = __webpack_require__(2719);\n// EXTERNAL MODULE: ./src/store/slices/pluginSlice.ts\nvar pluginSlice = __webpack_require__(4629);\n// EXTERNAL MODULE: ./src/store/slices/querySlice.ts\nvar querySlice = __webpack_require__(7166);\n// EXTERNAL MODULE: ./src/store/slices/uiSlice.ts\nvar uiSlice = __webpack_require__(5478);\n;// ./src/store/index.ts\n\r\n\r\n\r\n\r\n\r\n\r\nconst store = (0,redux_toolkit_esm_js_.configureStore)({\r\n    reducer: {\r\n        datasource: datasourceSlice/* default */.Ay,\r\n        plugin: pluginSlice/* default */.Ay,\r\n        query: querySlice/* default */.Ay,\r\n        ui: uiSlice/* default */.Ay\r\n    },\r\n    middleware: (getDefaultMiddleware) => getDefaultMiddleware({\r\n        serializableCheck: {\r\n            ignoredActions: [\'persist/PERSIST\', \'persist/REHYDRATE\']\r\n        }\r\n    }),\r\n    devTools: "development" !== \'production\'\r\n});\r\nconst useAppDispatch = () => useDispatch();\r\nconst useAppSelector = (/* unused pure expression or super */ null && (useSelector));\r\n\n// EXTERNAL MODULE: ./src/providers/ThemeProvider.tsx\nvar ThemeProvider = __webpack_require__(241);\n;// ./src/components/ErrorBoundary/index.tsx\n\r\n\r\n\r\n\r\nconst ErrorContainer = (styled_components_browser_esm_js_default()).div `\n  min-height: 400px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 24px;\n`;\r\nconst ErrorDetails = (styled_components_browser_esm_js_default()).details `\n  margin-top: 16px;\n  padding: 16px;\n  background-color: #f5f5f5;\n  border-radius: 6px;\n  border: 1px solid #d9d9d9;\n  \n  summary {\n    cursor: pointer;\n    font-weight: 500;\n    margin-bottom: 8px;\n  }\n  \n  pre {\n    margin: 0;\n    font-size: 12px;\n    line-height: 1.4;\n    white-space: pre-wrap;\n    word-break: break-word;\n  }\n`;\r\nclass ErrorBoundary extends index_js_.Component {\r\n    constructor(props) {\r\n        super(props);\r\n        this.handleReload = () => {\r\n            window.location.reload();\r\n        };\r\n        this.handleReset = () => {\r\n            this.setState({ hasError: false, error: undefined, errorInfo: undefined });\r\n        };\r\n        this.state = { hasError: false };\r\n    }\r\n    static getDerivedStateFromError(error) {\r\n        return {\r\n            hasError: true,\r\n            error\r\n        };\r\n    }\r\n    componentDidCatch(error, errorInfo) {\r\n        this.setState({\r\n            error,\r\n            errorInfo\r\n        });\r\n        if (true) {\r\n            console.error(\'ErrorBoundary caught an error:\', error, errorInfo);\r\n        }\r\n        if (false) // removed by dead control flow\n{}\r\n    }\r\n    render() {\r\n        if (this.state.hasError) {\r\n            if (this.props.fallback) {\r\n                return this.props.fallback;\r\n            }\r\n            return ((0,jsx_runtime.jsx)(ErrorContainer, { children: (0,jsx_runtime.jsx)(antd_es_index_js_.Result, { status: "error", title: "\\u5E94\\u7528\\u7A0B\\u5E8F\\u51FA\\u73B0\\u9519\\u8BEF", subTitle: "\\u62B1\\u6B49\\uFF0C\\u5E94\\u7528\\u7A0B\\u5E8F\\u9047\\u5230\\u4E86\\u4E00\\u4E2A\\u610F\\u5916\\u9519\\u8BEF\\u3002\\u8BF7\\u5C1D\\u8BD5\\u5237\\u65B0\\u9875\\u9762\\u6216\\u8054\\u7CFB\\u6280\\u672F\\u652F\\u6301\\u3002", extra: [\r\n                        (0,jsx_runtime.jsx)(antd_es_index_js_.Button, { type: "primary", onClick: this.handleReload, children: "\\u5237\\u65B0\\u9875\\u9762" }, "reload"),\r\n                        (0,jsx_runtime.jsx)(antd_es_index_js_.Button, { onClick: this.handleReset, children: "\\u91CD\\u8BD5" }, "reset")\r\n                    ], children:  true && this.state.error && ((0,jsx_runtime.jsxs)(ErrorDetails, { children: [(0,jsx_runtime.jsx)("summary", { children: "\\u9519\\u8BEF\\u8BE6\\u60C5 (\\u4EC5\\u5F00\\u53D1\\u73AF\\u5883\\u663E\\u793A)" }), (0,jsx_runtime.jsxs)("div", { children: [(0,jsx_runtime.jsx)("strong", { children: "\\u9519\\u8BEF\\u4FE1\\u606F:" }), (0,jsx_runtime.jsx)("pre", { children: this.state.error.message })] }), (0,jsx_runtime.jsxs)("div", { children: [(0,jsx_runtime.jsx)("strong", { children: "\\u9519\\u8BEF\\u5806\\u6808:" }), (0,jsx_runtime.jsx)("pre", { children: this.state.error.stack })] }), this.state.errorInfo && ((0,jsx_runtime.jsxs)("div", { children: [(0,jsx_runtime.jsx)("strong", { children: "\\u7EC4\\u4EF6\\u5806\\u6808:" }), (0,jsx_runtime.jsx)("pre", { children: this.state.errorInfo.componentStack })] }))] })) }) }));\r\n        }\r\n        return this.props.children;\r\n    }\r\n}\r\n\n// EXTERNAL MODULE: ./node_modules/.pnpm/style-loader@3.3.4_webpack@5.100.2/node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\nvar injectStylesIntoStyleTag = __webpack_require__(1945);\nvar injectStylesIntoStyleTag_default = /*#__PURE__*/__webpack_require__.n(injectStylesIntoStyleTag);\n// EXTERNAL MODULE: ./node_modules/.pnpm/style-loader@3.3.4_webpack@5.100.2/node_modules/style-loader/dist/runtime/styleDomAPI.js\nvar styleDomAPI = __webpack_require__(9214);\nvar styleDomAPI_default = /*#__PURE__*/__webpack_require__.n(styleDomAPI);\n// EXTERNAL MODULE: ./node_modules/.pnpm/style-loader@3.3.4_webpack@5.100.2/node_modules/style-loader/dist/runtime/insertBySelector.js\nvar insertBySelector = __webpack_require__(8182);\nvar insertBySelector_default = /*#__PURE__*/__webpack_require__.n(insertBySelector);\n// EXTERNAL MODULE: ./node_modules/.pnpm/style-loader@3.3.4_webpack@5.100.2/node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\nvar setAttributesWithoutAttributes = __webpack_require__(7657);\nvar setAttributesWithoutAttributes_default = /*#__PURE__*/__webpack_require__.n(setAttributesWithoutAttributes);\n// EXTERNAL MODULE: ./node_modules/.pnpm/style-loader@3.3.4_webpack@5.100.2/node_modules/style-loader/dist/runtime/insertStyleElement.js\nvar insertStyleElement = __webpack_require__(7861);\nvar insertStyleElement_default = /*#__PURE__*/__webpack_require__.n(insertStyleElement);\n// EXTERNAL MODULE: ./node_modules/.pnpm/style-loader@3.3.4_webpack@5.100.2/node_modules/style-loader/dist/runtime/styleTagTransform.js\nvar styleTagTransform = __webpack_require__(6898);\nvar styleTagTransform_default = /*#__PURE__*/__webpack_require__.n(styleTagTransform);\n// EXTERNAL MODULE: ./node_modules/.pnpm/css-loader@6.11.0_webpack@5.100.2/node_modules/css-loader/dist/cjs.js!./node_modules/.pnpm/postcss-loader@7.3.4_postcs_1608672c51e13a052948026869017311/node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[2].use[2]!./src/styles/index.css\nvar styles = __webpack_require__(1833);\n;// ./src/styles/index.css\n\n      \n      \n      \n      \n      \n      \n      \n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = (styleTagTransform_default());\noptions.setAttributes = (setAttributesWithoutAttributes_default());\n\n      options.insert = insertBySelector_default().bind(null, "head");\n    \noptions.domAPI = (styleDomAPI_default());\noptions.insertStyleElement = (insertStyleElement_default());\n\nvar update = injectStylesIntoStyleTag_default()(styles/* default */.A, options);\n\n\n\n\n       /* harmony default export */ const src_styles = (styles/* default */.A && styles/* default */.A.locals ? styles/* default */.A.locals : undefined);\n\n;// ./src/bootstrap.tsx\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\ndayjs_min_default().locale(\'zh-cn\');\r\nconst mount = (element, options = {}) => {\r\n    const { basename = \'/datasource\', onNavigate, initialPath } = options;\r\n    const root = (0,client/* createRoot */.H)(element);\r\n    root.render((0,jsx_runtime.jsx)((index_js_default()).StrictMode, { children: (0,jsx_runtime.jsx)(ErrorBoundary, { children: (0,jsx_runtime.jsx)(es_index_js_.Provider, { store: store, children: (0,jsx_runtime.jsx)(dist_index_js_.BrowserRouter, { basename: basename, children: (0,jsx_runtime.jsx)(antd_es_index_js_.ConfigProvider, { locale: zh_CN/* default */.A, theme: {\r\n                            token: {\r\n                                colorPrimary: \'#1890ff\',\r\n                                borderRadius: 6,\r\n                                fontSize: 14\r\n                            }\r\n                        }, children: (0,jsx_runtime.jsx)(ThemeProvider/* ThemeProvider */.N, { children: (0,jsx_runtime.jsx)(src_App, {}) }) }) }) }) }) }));\r\n    if (onNavigate) {\r\n        window.addEventListener(\'popstate\', () => {\r\n            onNavigate(window.location);\r\n        });\r\n    }\r\n    if (initialPath && initialPath !== window.location.pathname) {\r\n        window.history.pushState(null, \'\', initialPath);\r\n    }\r\n    return {\r\n        unmount: () => {\r\n            root.unmount();\r\n        },\r\n        update: (newOptions) => {\r\n            console.log(\'Updating datasource app with options:\', newOptions);\r\n        }\r\n    };\r\n};\r\nif (!window.__POWERED_BY_QIANKUN__) {\r\n    const container = document.getElementById(\'root\');\r\n    if (container) {\r\n        mount(container);\r\n    }\r\n}\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///9662\n\n}')}}]);