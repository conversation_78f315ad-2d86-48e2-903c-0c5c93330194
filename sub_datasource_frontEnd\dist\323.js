"use strict";(self.webpackChunkpageplug_datasource_frontend=self.webpackChunkpageplug_datasource_frontend||[]).push([[323],{3128:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval('{/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   kt: () => (/* binding */ LoadingSpinner)\n/* harmony export */ });\n/* unused harmony exports PageLoading, ContentLoading, ButtonLoading, TableLoading */\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(6070);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(212);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(8076);\n/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(antd__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(7107);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(3017);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_4__);\n\r\n\r\n\r\n\r\n\r\nconst rotate = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__.keyframes) `\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n`;\r\nconst pulse = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__.keyframes) `\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.5;\n  }\n`;\r\nconst LoadingContainer = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div) `\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 24px;\n  min-height: ${props => props.$minHeight || \'200px\'};\n  \n  ${props => props.$fullScreen && `\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-color: rgba(255, 255, 255, 0.8);\n    backdrop-filter: blur(4px);\n    z-index: 9999;\n    min-height: 100vh;\n  `}\n`;\r\nconst CustomLoadingIcon = styled_components__WEBPACK_IMPORTED_MODULE_4___default()((0,_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)) `\n  font-size: ${props => props.$size || 24}px !important;\n  color: ${props => props.$color || props.theme.colors.primary} !important;\n  animation: ${rotate} 1s linear infinite;\n`;\r\nconst LoadingText = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div) `\n  margin-top: 12px;\n  color: ${props => props.$color || props.theme.colors.text.secondary};\n  font-size: 14px;\n  animation: ${pulse} 2s ease-in-out infinite;\n`;\r\nconst DotLoader = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div) `\n  display: flex;\n  gap: 4px;\n  \n  .dot {\n    width: 8px;\n    height: 8px;\n    border-radius: 50%;\n    background-color: ${props => props.theme.colors.primary};\n    animation: ${pulse} 1.4s ease-in-out infinite both;\n    \n    &:nth-child(1) { animation-delay: -0.32s; }\n    &:nth-child(2) { animation-delay: -0.16s; }\n    &:nth-child(3) { animation-delay: 0s; }\n  }\n`;\r\nconst SkeletonLoader = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div) `\n  .skeleton-line {\n    height: 16px;\n    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n    background-size: 200% 100%;\n    animation: loading 1.5s infinite;\n    border-radius: 4px;\n    margin-bottom: 8px;\n    \n    &:last-child {\n      margin-bottom: 0;\n    }\n  }\n  \n  @keyframes loading {\n    0% {\n      background-position: 200% 0;\n    }\n    100% {\n      background-position: -200% 0;\n    }\n  }\n`;\r\nconst LoadingSpinner = ({ text = \'加载中...\', fullScreen = false, minHeight, variant = \'spin\', iconSize = 24, iconColor, textColor, skeletonLines = 3, ...spinProps }) => {\r\n    const renderLoader = () => {\r\n        switch (variant) {\r\n            case \'dots\':\r\n                return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(DotLoader, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "dot" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "dot" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "dot" })] }), text && (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(LoadingText, { "$color": textColor, children: text })] }));\r\n            case \'skeleton\':\r\n                return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SkeletonLoader, { children: Array.from({ length: skeletonLines }, (_, index) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "skeleton-line", style: {\r\n                            width: `${Math.random() * 40 + 60}%`\r\n                        } }, index))) }));\r\n            default:\r\n                return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Spin, { indicator: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(CustomLoadingIcon, { "$size": iconSize, "$color": iconColor }), tip: text, ...spinProps }));\r\n        }\r\n    };\r\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(LoadingContainer, { "$fullScreen": fullScreen, "$minHeight": minHeight, children: renderLoader() }));\r\n};\r\nconst PageLoading = ({ text = \'页面加载中...\' }) => (_jsx(LoadingSpinner, { fullScreen: true, text: text, iconSize: 32 }));\r\nconst ContentLoading = ({ text = \'内容加载中...\', minHeight = \'300px\' }) => (_jsx(LoadingSpinner, { text: text, minHeight: minHeight }));\r\nconst ButtonLoading = () => (_jsx(LoadingSpinner, { variant: "spin", iconSize: 14, text: "" }));\r\nconst TableLoading = () => (_jsx(LoadingSpinner, { variant: "skeleton", skeletonLines: 5 }));\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///3128\n\n}')},5323:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppRoutes: () => (/* binding */ AppRoutes),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getBreadcrumbFromPath: () => (/* binding */ getBreadcrumbFromPath),\n/* harmony export */   getMenuItems: () => (/* binding */ getMenuItems),\n/* harmony export */   getTitleFromPath: () => (/* binding */ getTitleFromPath),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission),\n/* harmony export */   requiresAuth: () => (/* binding */ requiresAuth),\n/* harmony export */   routeConfig: () => (/* binding */ routeConfig)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(6070);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(212);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1299);\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_router_dom__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(3128);\n\r\n\r\n\r\n\r\nconst DatasourceList = react__WEBPACK_IMPORTED_MODULE_1___default().lazy(() => Promise.all(/* import() */[__webpack_require__.e(96), __webpack_require__.e(876), __webpack_require__.e(561)]).then(__webpack_require__.bind(__webpack_require__, 1561)));\r\nconst DatasourceDetail = react__WEBPACK_IMPORTED_MODULE_1___default().lazy(() => Promise.all(/* import() */[__webpack_require__.e(96), __webpack_require__.e(876), __webpack_require__.e(358)]).then(__webpack_require__.bind(__webpack_require__, 1358)));\r\nconst DatasourceCreate = react__WEBPACK_IMPORTED_MODULE_1___default().lazy(() => Promise.all(/* import() */[__webpack_require__.e(96), __webpack_require__.e(876), __webpack_require__.e(437)]).then(__webpack_require__.bind(__webpack_require__, 437)));\r\nconst QueryEditor = react__WEBPACK_IMPORTED_MODULE_1___default().lazy(() => Promise.all(/* import() */[__webpack_require__.e(96), __webpack_require__.e(876), __webpack_require__.e(781)]).then(__webpack_require__.bind(__webpack_require__, 3781)));\r\nconst PluginManagement = react__WEBPACK_IMPORTED_MODULE_1___default().lazy(() => Promise.all(/* import() */[__webpack_require__.e(96), __webpack_require__.e(876), __webpack_require__.e(106)]).then(__webpack_require__.bind(__webpack_require__, 4106)));\r\nconst routeConfig = [\r\n    {\r\n        path: '/',\r\n        element: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_2__.Navigate, { to: \"/datasources\", replace: true }),\r\n        title: '首页'\r\n    },\r\n    {\r\n        path: '/datasources',\r\n        element: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DatasourceList, {}),\r\n        title: '数据源管理',\r\n        icon: 'DatabaseOutlined',\r\n        children: [\r\n            {\r\n                path: '/datasources',\r\n                element: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DatasourceList, {}),\r\n                title: '数据源列表'\r\n            },\r\n            {\r\n                path: '/datasources/create',\r\n                element: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DatasourceCreate, {}),\r\n                title: '新建数据源',\r\n                meta: {\r\n                    hideInMenu: true\r\n                }\r\n            },\r\n            {\r\n                path: '/datasources/:id',\r\n                element: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DatasourceDetail, {}),\r\n                title: '数据源详情',\r\n                meta: {\r\n                    hideInMenu: true\r\n                }\r\n            },\r\n            {\r\n                path: '/datasources/:id/edit',\r\n                element: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DatasourceCreate, {}),\r\n                title: '编辑数据源',\r\n                meta: {\r\n                    hideInMenu: true\r\n                }\r\n            }\r\n        ]\r\n    },\r\n    {\r\n        path: '/query',\r\n        element: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(QueryEditor, {}),\r\n        title: '查询编辑器',\r\n        icon: 'CodeOutlined',\r\n        children: [\r\n            {\r\n                path: '/query',\r\n                element: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(QueryEditor, {}),\r\n                title: '查询编辑器'\r\n            },\r\n            {\r\n                path: '/query/:datasourceId',\r\n                element: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(QueryEditor, {}),\r\n                title: '查询编辑器',\r\n                meta: {\r\n                    hideInMenu: true\r\n                }\r\n            }\r\n        ]\r\n    },\r\n    {\r\n        path: '/plugins',\r\n        element: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(PluginManagement, {}),\r\n        title: '插件管理',\r\n        icon: 'AppstoreOutlined'\r\n    }\r\n];\r\nconst AppRoutes = () => {\r\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, { fallback: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_Common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__/* .LoadingSpinner */ .kt, { text: \"\\u9875\\u9762\\u52A0\\u8F7D\\u4E2D...\" }), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_router_dom__WEBPACK_IMPORTED_MODULE_2__.Routes, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_2__.Route, { path: \"/\", element: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_2__.Navigate, { to: \"/datasources\", replace: true }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_2__.Route, { path: \"/datasources\", element: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DatasourceList, {}) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_2__.Route, { path: \"/datasources/create\", element: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DatasourceCreate, {}) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_2__.Route, { path: \"/datasources/:id\", element: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DatasourceDetail, {}) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_2__.Route, { path: \"/datasources/:id/edit\", element: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DatasourceCreate, {}) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_2__.Route, { path: \"/query\", element: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(QueryEditor, {}) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_2__.Route, { path: \"/query/:datasourceId\", element: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(QueryEditor, {}) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_2__.Route, { path: \"/plugins\", element: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(PluginManagement, {}) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_2__.Route, { path: \"*\", element: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_2__.Navigate, { to: \"/datasources\", replace: true }) })] }) }));\r\n};\r\nconst getBreadcrumbFromPath = (pathname) => {\r\n    const segments = pathname.split('/').filter(Boolean);\r\n    const breadcrumb = [];\r\n    if (segments.length === 0) {\r\n        return [{ title: '首页', path: '/' }];\r\n    }\r\n    if (segments[0] === 'datasources') {\r\n        breadcrumb.push({ title: '数据源管理', path: '/datasources' });\r\n        if (segments.length === 1) {\r\n            breadcrumb.push({ title: '数据源列表' });\r\n        }\r\n        else if (segments[1] === 'create') {\r\n            breadcrumb.push({ title: '新建数据源' });\r\n        }\r\n        else if (segments.length === 2) {\r\n            breadcrumb.push({ title: '数据源详情' });\r\n        }\r\n        else if (segments[2] === 'edit') {\r\n            breadcrumb.push({ title: '数据源详情', path: `/datasources/${segments[1]}` });\r\n            breadcrumb.push({ title: '编辑数据源' });\r\n        }\r\n    }\r\n    else if (segments[0] === 'query') {\r\n        breadcrumb.push({ title: '查询编辑器', path: '/query' });\r\n        if (segments.length === 2) {\r\n            breadcrumb.push({ title: '数据源查询' });\r\n        }\r\n    }\r\n    else if (segments[0] === 'plugins') {\r\n        breadcrumb.push({ title: '插件管理' });\r\n    }\r\n    else {\r\n        breadcrumb.push({ title: '未知页面' });\r\n    }\r\n    return breadcrumb;\r\n};\r\nconst getTitleFromPath = (pathname) => {\r\n    const breadcrumb = getBreadcrumbFromPath(pathname);\r\n    return breadcrumb[breadcrumb.length - 1]?.title || 'PagePlug 数据源管理';\r\n};\r\nconst requiresAuth = (pathname) => {\r\n    return false;\r\n};\r\nconst hasPermission = (pathname, userRoles = []) => {\r\n    return true;\r\n};\r\nconst getMenuItems = () => {\r\n    return routeConfig\r\n        .filter(route => !route.meta?.hideInMenu && route.path !== '/')\r\n        .map(route => ({\r\n        key: route.path,\r\n        label: route.title,\r\n        icon: route.icon,\r\n        children: route.children\r\n            ?.filter(child => !child.meta?.hideInMenu)\r\n            ?.map(child => ({\r\n            key: child.path,\r\n            label: child.title\r\n        }))\r\n    }));\r\n};\r\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AppRoutes);\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///5323\n\n}")}}]);