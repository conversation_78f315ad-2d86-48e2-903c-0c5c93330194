@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\andy\rebuildPagePlug\sub_datasource_frontEnd\node_modules\.pnpm\ts-jest@29.4.0_@babel+core@_60dbc32bc28146c9ce7438d2801c8554\node_modules\ts-jest\node_modules;D:\andy\rebuildPagePlug\sub_datasource_frontEnd\node_modules\.pnpm\ts-jest@29.4.0_@babel+core@_60dbc32bc28146c9ce7438d2801c8554\node_modules;D:\andy\rebuildPagePlug\sub_datasource_frontEnd\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\andy\rebuildPagePlug\sub_datasource_frontEnd\node_modules\.pnpm\ts-jest@29.4.0_@babel+core@_60dbc32bc28146c9ce7438d2801c8554\node_modules\ts-jest\node_modules;D:\andy\rebuildPagePlug\sub_datasource_frontEnd\node_modules\.pnpm\ts-jest@29.4.0_@babel+core@_60dbc32bc28146c9ce7438d2801c8554\node_modules;D:\andy\rebuildPagePlug\sub_datasource_frontEnd\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\cli.js" %*
)
