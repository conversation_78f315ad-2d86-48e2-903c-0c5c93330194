name: Appsmith Performance Test Workflow

on:
  # This line enables manual triggering of this workflow.
  workflow_dispatch:
  workflow_call:
    inputs:
      pr:
        description: "This is the PR number in case the workflow is being called in a pull request"
        required: false
        type: number

# Change the working directory for all the jobs in this workflow
defaults:
  run:
    working-directory: app/client
    shell: bash

jobs:
  perf-test:
    runs-on: ubuntu-latest-4-cores
    # Only run this workflow for internally triggered events
    if: |
      github.event.pull_request.head.repo.full_name == github.repository ||
      github.event_name == 'push' ||
      github.event_name == 'workflow_dispatch' ||
      github.event_name == 'repository_dispatch'

    strategy:
      fail-fast: false
      matrix:
        job: [0, 1]

    # Service containers to run with this job. Required for running tests
    services:
      # Label used to access the service container
      redis:
        # Docker Hub image for Redis
        image: redis
        ports:
          # Opens tcp port 6379 on the host and service container
          - 6379:6379
      mongo:
        image: mongo
        ports:
          - 27017:27017
    steps:
      - name: Checkout the merged commit from PR and base branch
        if: inputs.pr != 0
        uses: actions/checkout@v4
        with:
          ref: refs/pull/${{ inputs.pr }}/merge

      # Checkout the code in the current branch in case the workflow is called because of a branch push event
      - name: Checkout the head commit of the branch
        if: inputs.pr == 0
        uses: actions/checkout@v4

      - name: Figure out the PR number
        run: echo ${{ inputs.pr }}

      - name: Print the Github event
        run: echo ${{ github.event_name }}

      # Timestamp will be used to create cache key
      - id: timestamp
        run: echo "timestamp=$(date +'%Y-%m-%dT%H:%M:%S')" >> $GITHUB_OUTPUT

      # In case this is second attempt try restoring status of the prior attempt from cache
      - name: Restore the previous run result
        uses: actions/cache@v3
        with:
          path: |
            ~/run_result
          key: ${{ github.run_id }}-${{ github.job }}-${{ steps.timestamp.outputs.timestamp }}
          restore-keys: |
            ${{ github.run_id }}-${{ github.job }}-

      # Fetch prior run result
      - name: Get the previous run result
        id: run_result
        run: cat ~/run_result 2>/dev/null || echo 'default'

      # In case of prior failure run the job
      - if: steps.run_result.outputs.run_result != 'success'
        run: echo "I'm alive!" && exit 0

      # Set status = success
      - name: Save the status of the run
        run: echo "run_result=success" >> $GITHUB_OUTPUT > ~/run_result

      # Set up Node.js cache yarn dependencies.
      # actions/setup-node@v3 now support caching of dependencies using actions/cache under the hood
      # https://github.com/actions/setup-node#caching-global-packages-data
      - name: Set up Node.js
        if: steps.run_result.outputs.run_result != 'success'
        uses: actions/setup-node@v3
        with:
          node-version-file: app/client/package.json

      - name: Download Docker image artifact
        uses: actions/download-artifact@v3
        with:
          name: cicontainer

      - name: Load Docker image from tar file
        working-directory: "."
        run: |
          gunzip cicontainer.tar.gz
          docker load -i cicontainer.tar

      - name: Create folder
        if: steps.run_result.outputs.run_result != 'success'
        env:
          APPSMITH_LICENSE_KEY: ${{ secrets.APPSMITH_LICENSE_KEY }}
        working-directory: "."
        run: |
          mkdir -p cicontainerlocal/stacks/configuration/

      - name: Run docker image
        if: steps.run_result.outputs.run_result != 'success'
        env:
          APPSMITH_LICENSE_KEY: ${{ secrets.APPSMITH_LICENSE_KEY }}
        working-directory: "."
        run: |
          mkdir -p ~/git-server/keys
          mkdir -p ~/git-server/repos
          docker run --name test-event-driver -d -p 2222:22 -p 5001:5001 -p 3306:3306 \
          -p 5432:5432 -p 28017:27017 -p 25:25 -p 5000:5000 -p 3000:3000 --privileged --pid=host --ipc=host --volume /:/host -v ~/git-server/keys:/git-server/keys \
          -v ~/git-server/repos:/git-server/repos  appsmith/test-event-driver:latest
          cd cicontainerlocal
          docker run -d --name appsmith -p 80:80 \
            -v "$PWD/stacks:/appsmith-stacks" -e APPSMITH_LICENSE_KEY=$APPSMITH_LICENSE_KEY \
            -e APPSMITH_DISABLE_TELEMETRY=true \
            -e APPSMITH_CLOUD_SERVICES_BASE_URL=http://host.docker.internal:5001 \
            --add-host=host.docker.internal:host-gateway \
            cicontainer

      # Start rts
      - name: Start RTS Server
        if: steps.run_result.outputs.run_result != 'success'
        working-directory: ./app/client/packages/rts
        run: |
          cp .env.example .env
          ./start-server.sh &

      - name: Setting up the perf tests
        if: steps.run_result.outputs.run_result != 'success'
        shell: bash
        env:
          APPSMITH_SSL_CERTIFICATE: ${{ secrets.APPSMITH_SSL_CERTIFICATE }}
          APPSMITH_SSL_KEY: ${{ secrets.APPSMITH_SSL_KEY }}
          CYPRESS_URL: ${{ secrets.CYPRESS_URL }}
          CYPRESS_USERNAME: ${{ secrets.CYPRESS_USERNAME }}
          CYPRESS_PASSWORD: ${{ secrets.CYPRESS_PASSWORD }}
          CYPRESS_TESTUSERNAME1: ${{ secrets.CYPRESS_TESTUSERNAME1 }}
          CYPRESS_TESTPASSWORD1: ${{ secrets.CYPRESS_TESTPASSWORD1 }}
          CYPRESS_TESTUSERNAME2: ${{ secrets.CYPRESS_TESTUSERNAME2 }}
          CYPRESS_TESTPASSWORD2: ${{ secrets.CYPRESS_TESTPASSWORD1 }}
          CYPRESS_TESTUSERNAME3: ${{ secrets.CYPRESS_TESTUSERNAME3 }}
          CYPRESS_TESTPASSWORD3: ${{ secrets.CYPRESS_TESTPASSWORD3 }}
          CYPRESS_TESTUSERNAME4: ${{ secrets.CYPRESS_TESTUSERNAME4 }}
          CYPRESS_TESTPASSWORD4: ${{ secrets.CYPRESS_TESTPASSWORD4 }}
          CYPRESS_S3_ACCESS_KEY: ${{ secrets.CYPRESS_S3_ACCESS_KEY }}
          CYPRESS_S3_SECRET_KEY: ${{ secrets.CYPRESS_S3_SECRET_KEY }}
          CYPRESS_APPSMITH_OAUTH2_GOOGLE_CLIENT_ID: ${{ secrets.CYPRESS_APPSMITH_OAUTH2_GOOGLE_CLIENT_ID }}
          CYPRESS_APPSMITH_OAUTH2_GOOGLE_CLIENT_SECRET: ${{ secrets.CYPRESS_APPSMITH_OAUTH2_GOOGLE_CLIENT_SECRET }}
          CYPRESS_APPSMITH_OAUTH2_GITHUB_CLIENT_ID: ${{ secrets.CYPRESS_APPSMITH_OAUTH2_GITHUB_CLIENT_ID }}
          CYPRESS_APPSMITH_OAUTH2_GITHUB_CLIENT_SECRET: ${{ secrets.CYPRESS_APPSMITH_OAUTH2_GITHUB_CLIENT_SECRET }}
          APPSMITH_DISABLE_TELEMETRY: true
          APPSMITH_GOOGLE_MAPS_API_KEY: ${{ secrets.APPSMITH_GOOGLE_MAPS_API_KEY }}
          POSTGRES_PASSWORD: postgres
        run: |
          chmod a+x ./perf/setup-perf-test.sh
          ./perf/setup-perf-test.sh

      - name: Get the performance-infra branch/ref
        id: perf_infra_ref
        run: |
          ref=${{ github.event.client_payload.slash_command.args.named.ref }}
          if [[ -z "$ref" ]]; then ref="v2"; fi
          echo ref=$ref >> $GITHUB_OUTPUT

      - name: Checkout Performance Infra code
        uses: actions/checkout@v4
        with:
          repository: appsmithorg/performance-infra
          token: ${{ secrets.APPSMITH_PERF_INFRA_REPO_PAT }}
          ref: ${{ steps.perf_infra_ref.outputs.ref }}
          path: perf

      - name: Installing performance tests dependencies
        if: steps.run_result.outputs.run_result != 'success'
        working-directory: perf
        shell: bash
        run: yarn install --immutable

      - name: Change test script permissions
        if: steps.run_result.outputs.run_result != 'success'
        working-directory: perf
        run: chmod +x ./start-test.sh

      - name: Run performance tests
        if: steps.run_result.outputs.run_result != 'success'
        working-directory: perf
        shell: bash
        env:
          APPSMITH_SSL_CERTIFICATE: ${{ secrets.APPSMITH_SSL_CERTIFICATE }}
          APPSMITH_SSL_KEY: ${{ secrets.APPSMITH_SSL_KEY }}
          CYPRESS_TESTUSERNAME1: ${{ secrets.CYPRESS_TESTUSERNAME9 }}
          CYPRESS_TESTPASSWORD1: ${{ secrets.CYPRESS_TESTPASSWORD9 }}
          APPSMITH_PERFORMANCE_DB_CONFIG: ${{ secrets.APPSMITH_PERFORMANCE_DB_CONFIG }}
          APPSMITH_PERFORMANCE_S3_KEY: ${{secrets.APPSMITH_PERFORMANCE_S3_KEY}}
          APPSMITH_PERFORMANCE_S3_SECRET: ${{secrets.APPSMITH_PERFORMANCE_S3_SECRET}}
          APPSMITH_PERFORMANCE_DB_PASSWORD: ${{secrets.APPSMITH_PERFORMANCE_DB_PASSWORD}}
          APPSMITH_PERFORMANCE_DB_HOST: ${{secrets.APPSMITH_PERFORMANCE_DB_HOST}}
          APPSMITH_PERFORMANCE_DB_V2_CONFIG: ${{ secrets.APPSMITH_PERFORMANCE_DB_V2_CONFIG }}
          PERF_GITHUB_PAT: ${{ secrets.APPSMITH_PERF_INFRA_REPO_PAT }}
          APPSMITH_DISABLE_TELEMETRY: true
          POSTGRES_PASSWORD: postgres
          NODE_TLS_REJECT_UNAUTHORIZED: "0"
          MACHINE: ubuntu-latest-4-cores
          JOB: ${{ matrix.job }}

        run: ./start-test.sh

      # Restore the previous built bundle if present. If not push the newly built into the cache
      - name: Restore the previous bundle
        uses: actions/cache@v3
        with:
          path: |
            perf/traces
          key: ${{ github.run_id }}-${{ github.job }}-${{ steps.timestamp.outputs.timestamp }}
          restore-keys: |
            ${{ github.run_id }}-${{ github.job }}

      - uses: actions/upload-artifact@v3
        with:
          name: performance-test-logs
          path: perf/traces/reports

      # Set status = success
      - name: Save the status of the run
        run: echo "run_result=success" >> $GITHUB_OUTPUT > ~/run_result
