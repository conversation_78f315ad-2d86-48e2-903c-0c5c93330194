---
name: <PERSON>i Packer Build

on:
  # This line enables manual triggering of this workflow.
  workflow_dispatch:

jobs:
  packer:
    runs-on: ubuntu-latest
    name: ami-packer-build

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-south-1

      - name: Install python
        uses: actions/setup-python@v3
        with:
          python-version: "3.8"

      - name: Install ansible
        run: pip install ansible

      - name: Install Packer
        run: |
          sudo apt-get update; sudo apt-get install -y curl gnupg software-properties-common ;
          curl -fsSL https://apt.releases.hashicorp.com/gpg | sudo apt-key add - ;
          sudo apt-add-repository -y "deb [arch=amd64] https://apt.releases.hashicorp.com $(lsb_release -cs) main" ;
          sudo apt-get update && sudo apt-get install -y packer;

      - name: Build artifacts
        run: |
          cd deploy/packer;
          packer build -var vpc_id=${{ secrets.APPSMITH_AWS_VPC_PROD }} -var subnet_id=${{ secrets.APPSMITH_AWS_PROD_SUBNET }} -var token=${{ secrets.DO_TOKEN }} template.json.pkr.hcl
