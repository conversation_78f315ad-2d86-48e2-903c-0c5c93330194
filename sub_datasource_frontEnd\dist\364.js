"use strict";(self.webpackChunkpageplug_datasource_frontend=self.webpackChunkpageplug_datasource_frontend||[]).push([[364],{5364:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval('{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(6070);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(212);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(8076);\n/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(antd__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(649);\n\r\n\r\n\r\n\r\nconst DataVisualization = ({ data, type = \'bar\' }) => {\r\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Card, { title: "\\u6570\\u636E\\u53EF\\u89C6\\u5316", extra: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A, {}), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Empty, { description: "\\u6570\\u636E\\u53EF\\u89C6\\u5316\\u7EC4\\u4EF6\\u5F00\\u53D1\\u4E2D...", image: antd__WEBPACK_IMPORTED_MODULE_2__.Empty.PRESENTED_IMAGE_SIMPLE }) }));\r\n};\r\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DataVisualization);\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTM2NC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUEwQjtBQUNTO0FBQ2tCO0FBT3JELE1BQU0saUJBQWlCLEdBQXFDLENBQUMsRUFBRSxJQUFJLEVBQUUsSUFBSSxHQUFHLEtBQUssRUFBRSxFQUFFLEVBQUU7SUFDckYsT0FBTyxDQUNMLHVEQUFDLHNDQUFJLElBQUMsS0FBSyxFQUFDLGdDQUFPLEVBQUMsS0FBSyxFQUFFLHVEQUFDLGtFQUFnQixLQUFHLFlBQzdDLHVEQUFDLHVDQUFLLElBQ0osV0FBVyxFQUFDLGlFQUFlLEVBQzNCLEtBQUssRUFBRSx1Q0FBSyxDQUFDLHNCQUFzQixHQUNuQyxHQUNHLENBQ1IsQ0FBQztBQUNKLENBQUMsQ0FBQztBQUVGLGlFQUFlLGlCQUFpQixFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGFnZXBsdWctZGF0YXNvdXJjZS1mcm9udGVuZC8uL3NyYy9jb21wb25lbnRzL0RhdGFWaXN1YWxpemF0aW9uL2luZGV4LnRzeD82NGZmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBDYXJkLCBFbXB0eSB9IGZyb20gJ2FudGQnO1xuaW1wb3J0IHsgQmFyQ2hhcnRPdXRsaW5lZCB9IGZyb20gJ0BhbnQtZGVzaWduL2ljb25zJztcblxuaW50ZXJmYWNlIERhdGFWaXN1YWxpemF0aW9uUHJvcHMge1xuICBkYXRhPzogYW55W107XG4gIHR5cGU/OiAnYmFyJyB8ICdsaW5lJyB8ICdwaWUnO1xufVxuXG5jb25zdCBEYXRhVmlzdWFsaXphdGlvbjogUmVhY3QuRkM8RGF0YVZpc3VhbGl6YXRpb25Qcm9wcz4gPSAoeyBkYXRhLCB0eXBlID0gJ2JhcicgfSkgPT4ge1xuICByZXR1cm4gKFxuICAgIDxDYXJkIHRpdGxlPVwi5pWw5o2u5Y+v6KeG5YyWXCIgZXh0cmE9ezxCYXJDaGFydE91dGxpbmVkIC8+fT5cbiAgICAgIDxFbXB0eVxuICAgICAgICBkZXNjcmlwdGlvbj1cIuaVsOaNruWPr+inhuWMlue7hOS7tuW8gOWPkeS4rS4uLlwiXG4gICAgICAgIGltYWdlPXtFbXB0eS5QUkVTRU5URURfSU1BR0VfU0lNUExFfVxuICAgICAgLz5cbiAgICA8L0NhcmQ+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBEYXRhVmlzdWFsaXphdGlvbjtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///5364\n\n}')}}]);