name: Performance Tests Workflow

on:
  # This workflow is only triggered by the ok to test command dispatch
  repository_dispatch:
    types: [perf-test-command]

jobs:
  notify:
    runs-on: ubuntu-latest
    steps:
      # This step creates a comment on the PR with a link to this workflow run.
      - name: Add a comment on the PR with link to workflow run
        uses: peter-evans/create-or-update-comment@v3
        with:
          issue-number: ${{ github.event.client_payload.pull_request.number }}
          body: |
            Tests running at: <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}>.
            Workflow: `${{ github.workflow }}`.
            Commit: `${{ github.event.client_payload.slash_command.args.named.sha }}`.
            PR: ${{ github.event.client_payload.pull_request.number }}.
            Perf tests will be available at <https://app.appsmith.com/app/performance-infra-dashboard/pr-details-638dd7cd2913ba43778b915e?pr=${{ github.event.client_payload.pull_request.number }}&runId=${{ github.run_id }}_${{github.run_attempt}}>
  server-build:
    name: server-build
    uses: ./.github/workflows/server-build.yml
    secrets: inherit
    with:
      pr: ${{ github.event.client_payload.pull_request.number }}

  client-build:
    name: client-build
    uses: ./.github/workflows/client-build.yml
    secrets: inherit
    with:
      pr: ${{ github.event.client_payload.pull_request.number }}

  rts-build:
    name: rts-build
    uses: ./.github/workflows/rts-build.yml
    secrets: inherit
    with:
      pr: ${{ github.event.client_payload.pull_request.number }}

  build-docker-image:
    needs: [client-build, server-build, rts-build]
    # Only run if the build step is successful
    if: success()
    name: build-docker-image
    uses: ./.github/workflows/build-docker-image.yml
    secrets: inherit
    with:
      pr: ${{ github.event.client_payload.pull_request.number }}

  perf-test:
    needs: [build-docker-image]
    # Only run if the build step is successful
    if: success()
    name: perf-test
    uses: ./.github/workflows/perf-test.yml
    secrets: inherit
    with:
      pr: ${{ github.event.client_payload.pull_request.number }}

  perf-test-v2:
    needs: [build-docker-image]
    # Only run if the build step is successful
    if: success()
    name: perf-test-v2
    uses: ./.github/workflows/perf-test-v2.yml
    secrets: inherit
    with:
      pr: ${{ github.event.client_payload.pull_request.number }}
