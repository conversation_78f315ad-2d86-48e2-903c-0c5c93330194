"use strict";(self.webpackChunkpageplug_datasource_frontend=self.webpackChunkpageplug_datasource_frontend||[]).push([[187],{241:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval("{/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   D: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* unused harmony export ThemeProvider */\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(6070);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(212);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(3017);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_2__);\n\r\n\r\n\r\nconst lightTheme = {\r\n    colors: {\r\n        primary: '#1890ff',\r\n        secondary: '#6c757d',\r\n        success: '#52c41a',\r\n        warning: '#faad14',\r\n        error: '#ff4d4f',\r\n        info: '#1890ff',\r\n        text: {\r\n            primary: '#262626',\r\n            secondary: '#595959',\r\n            disabled: '#bfbfbf'\r\n        },\r\n        background: {\r\n            primary: '#ffffff',\r\n            secondary: '#fafafa',\r\n            tertiary: '#f5f5f5'\r\n        },\r\n        border: {\r\n            primary: '#d9d9d9',\r\n            secondary: '#f0f0f0'\r\n        }\r\n    },\r\n    spacing: {\r\n        xs: '4px',\r\n        sm: '8px',\r\n        md: '16px',\r\n        lg: '24px',\r\n        xl: '32px',\r\n        xxl: '48px'\r\n    },\r\n    borderRadius: {\r\n        sm: '4px',\r\n        md: '6px',\r\n        lg: '8px'\r\n    },\r\n    shadows: {\r\n        sm: '0 2px 4px rgba(0, 0, 0, 0.06)',\r\n        md: '0 4px 12px rgba(0, 0, 0, 0.1)',\r\n        lg: '0 8px 24px rgba(0, 0, 0, 0.15)'\r\n    },\r\n    typography: {\r\n        fontFamily: \"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif\",\r\n        fontSize: {\r\n            xs: '12px',\r\n            sm: '14px',\r\n            md: '16px',\r\n            lg: '18px',\r\n            xl: '20px'\r\n        },\r\n        fontWeight: {\r\n            normal: 400,\r\n            medium: 500,\r\n            semibold: 600,\r\n            bold: 700\r\n        }\r\n    }\r\n};\r\nconst darkTheme = {\r\n    ...lightTheme,\r\n    colors: {\r\n        ...lightTheme.colors,\r\n        text: {\r\n            primary: '#ffffff',\r\n            secondary: '#d9d9d9',\r\n            disabled: '#595959'\r\n        },\r\n        background: {\r\n            primary: '#1f1f1f',\r\n            secondary: '#262626',\r\n            tertiary: '#141414'\r\n        },\r\n        border: {\r\n            primary: '#434343',\r\n            secondary: '#303030'\r\n        }\r\n    }\r\n};\r\nconst ThemeContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\r\nconst ThemeProvider = ({ children }) => {\r\n    const [isDark, setIsDark] = useState(() => {\r\n        const saved = localStorage.getItem('datasource-app-theme');\r\n        if (saved) {\r\n            return saved === 'dark';\r\n        }\r\n        return window.matchMedia('(prefers-color-scheme: dark)').matches;\r\n    });\r\n    const theme = isDark ? darkTheme : lightTheme;\r\n    const toggleTheme = () => {\r\n        setIsDark(prev => {\r\n            const newValue = !prev;\r\n            localStorage.setItem('datasource-app-theme', newValue ? 'dark' : 'light');\r\n            return newValue;\r\n        });\r\n    };\r\n    useEffect(() => {\r\n        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\r\n        const handleChange = (e) => {\r\n            const saved = localStorage.getItem('datasource-app-theme');\r\n            if (!saved) {\r\n                setIsDark(e.matches);\r\n            }\r\n        };\r\n        mediaQuery.addEventListener('change', handleChange);\r\n        return () => mediaQuery.removeEventListener('change', handleChange);\r\n    }, []);\r\n    useEffect(() => {\r\n        const root = document.documentElement;\r\n        if (isDark) {\r\n            root.classList.add('dark');\r\n        }\r\n        else {\r\n            root.classList.remove('dark');\r\n        }\r\n    }, [isDark]);\r\n    const contextValue = {\r\n        theme,\r\n        isDark,\r\n        toggleTheme\r\n    };\r\n    return (_jsx(ThemeContext.Provider, { value: contextValue, children: _jsx(StyledThemeProvider, { theme: theme, children: children }) }));\r\n};\r\nconst useTheme = () => {\r\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\r\n    if (context === undefined) {\r\n        throw new Error('useTheme must be used within a ThemeProvider');\r\n    }\r\n    return context;\r\n};\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///241\n\n}")},3627:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval('{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(6070);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(212);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1299);\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_router_dom__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(8076);\n/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(antd__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(3017);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_Layout_AppHeader__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(5520);\n/* harmony import */ var _components_Layout_AppSidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(635);\n/* harmony import */ var _components_Common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(3128);\n/* harmony import */ var _hooks_useTheme__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(5869);\n/* harmony import */ var _utils_codeSplitting__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(3043);\n/* harmony import */ var _utils_performance__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(5642);\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nconst { Content } = antd__WEBPACK_IMPORTED_MODULE_3__.Layout;\r\nconst AppContainer = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div) `\n  min-height: 100vh;\n  background-color: ${props => props.$isDark ? \'#141414\' : \'#f5f5f5\'};\n  \n  .ant-layout {\n    background-color: transparent;\n  }\n  \n  .ant-layout-content {\n    padding: 24px;\n    margin: 0;\n    min-height: calc(100vh - 64px);\n  }\n`;\r\nconst ContentWrapper = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div) `\n  max-width: 1200px;\n  margin: 0 auto;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n`;\r\nconst App = () => {\r\n    const { isDark } = (0,_hooks_useTheme__WEBPACK_IMPORTED_MODULE_8__/* .useTheme */ .D)();\r\n    const location = (0,react_router_dom__WEBPACK_IMPORTED_MODULE_2__.useLocation)();\r\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\r\n        (0,_utils_performance__WEBPACK_IMPORTED_MODULE_10__/* .initPerformanceMonitoring */ .H3)();\r\n        (0,_utils_codeSplitting__WEBPACK_IMPORTED_MODULE_9__/* .initCodeSplitting */ .wA)();\r\n    }, []);\r\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\r\n        _utils_performance__WEBPACK_IMPORTED_MODULE_10__/* .performanceCollector */ .Y5.recordMetric(\'route.change\', performance.now());\r\n        _utils_codeSplitting__WEBPACK_IMPORTED_MODULE_9__/* .preloadStrategy */ .PE.preloadByUserBehavior(location.pathname);\r\n    }, [location.pathname]);\r\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(AppContainer, { "$isDark": isDark, id: "datasource-app", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_3__.Layout, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_Layout_AppHeader__WEBPACK_IMPORTED_MODULE_5__/* .AppHeader */ .j, {}), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_3__.Layout, { hasSider: true, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_Layout_AppSidebar__WEBPACK_IMPORTED_MODULE_6__/* .AppSidebar */ .G, {}), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_3__.Layout, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Content, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(ContentWrapper, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, { fallback: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_Common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_7__/* .LoadingSpinner */ .kt, {}), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_router_dom__WEBPACK_IMPORTED_MODULE_2__.Routes, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_2__.Route, { path: "/", element: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_2__.Navigate, { to: "/datasources", replace: true }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_2__.Route, { path: "/datasources", element: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_utils_codeSplitting__WEBPACK_IMPORTED_MODULE_9__/* .RouteComponents */ .IQ.DatasourceList, {}) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_2__.Route, { path: "/datasources/create", element: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_utils_codeSplitting__WEBPACK_IMPORTED_MODULE_9__/* .RouteComponents */ .IQ.DatasourceCreate, {}) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_2__.Route, { path: "/datasources/:id", element: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_utils_codeSplitting__WEBPACK_IMPORTED_MODULE_9__/* .RouteComponents */ .IQ.DatasourceDetail, {}) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_2__.Route, { path: "/datasources/:id/edit", element: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_utils_codeSplitting__WEBPACK_IMPORTED_MODULE_9__/* .RouteComponents */ .IQ.DatasourceCreate, {}) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_2__.Route, { path: "/query", element: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_utils_codeSplitting__WEBPACK_IMPORTED_MODULE_9__/* .RouteComponents */ .IQ.QueryEditor, {}) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_2__.Route, { path: "/query/:datasourceId", element: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_utils_codeSplitting__WEBPACK_IMPORTED_MODULE_9__/* .RouteComponents */ .IQ.QueryEditor, {}) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_2__.Route, { path: "/plugins", element: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_utils_codeSplitting__WEBPACK_IMPORTED_MODULE_9__/* .RouteComponents */ .IQ.PluginManagement, {}) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_2__.Route, { path: "*", element: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_router_dom__WEBPACK_IMPORTED_MODULE_2__.Navigate, { to: "/datasources", replace: true }) })] }) }) }) }) })] })] }) }));\r\n};\r\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (App);\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///3627\n\n}')}}]);