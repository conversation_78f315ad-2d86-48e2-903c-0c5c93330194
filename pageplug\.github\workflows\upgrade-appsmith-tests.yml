name: Upgrade A<PERSON>mith Test Workflow

on:
  # This line enables manual triggering of this workflow.
  workflow_dispatch:

jobs:
  server-build:
    name: server-build
    uses: ./.github/workflows/server-build.yml
    secrets: inherit
    with:
      pr: 0

  client-build:
    name: client-build
    uses: ./.github/workflows/client-build.yml
    secrets: inherit
    with:
      pr: 0

  rts-build:
    name: rts-build
    uses: ./.github/workflows/rts-build.yml
    secrets: inherit
    with:
      pr: 0

  build-docker-image:
    needs: [ client-build, server-build, rts-build ]
    # Only run if the build step is successful
    if: success()
    name: build-docker-image
    uses: ./.github/workflows/build-docker-image.yml
    secrets: inherit
    with:
      pr: 0

  ci-test:
    needs: [ build-docker-image ]
    # Only run if the build step is successful
    if: success()
    name: ci-test
    uses: ./.github/workflows/ci-test.yml
    secrets: inherit
    with:
      pr: 0

  ci-test-result:
    needs: [ci-test]
    # Only run if the ci-test with matrices step is successful
    if: always()
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: bash
    steps:
      - name: Dump the client payload context
        env:
          PAYLOAD_CONTEXT: ${{ toJson(github.event.client_payload) }}
        run: echo "$PAYLOAD_CONTEXT"

      # Deleting the existing dir's if any
      - name: Delete existing directories
        if: needs.ci-test.result != 'success'
        run: |
          rm -f ~/failed_spec_ci
          rm -f ~/combined_failed_spec_ci

      # Download failed_spec list for all jobs
      - uses: actions/download-artifact@v3
        if: needs.ci-test.result != 'success'
        id: download_ci
        with:
          name: failed-spec-ci
          path: ~/failed_spec_ci
          
      # In case for any ci job failure, create combined failed spec
      - name: "combine all specs for CI"
        if: needs.ci-test.result != 'success'
        run: |
          echo "Debugging: failed specs in ~/failed_spec_ci/failed_spec_ci*"
          cat ~/failed_spec_ci/failed_spec_ci*
          cat ~/failed_spec_ci/failed_spec_ci* | sort -u >> ~/combined_failed_spec_ci

      # Force save the CI failed spec list into a cache
      - name: Store the combined run result for CI
        if: needs.ci-test.result != 'success'
        uses: martijnhols/actions-cache/save@v3
        with:
          path: |
            ~/combined_failed_spec_ci
          key: ${{ github.run_id }}-"ci-test-result"
          restore-keys: |
            ${{ github.run_id }}-${{ github.job }}
            
      # Upload combined failed CI spec list to a file
      # This is done for debugging.
      - name: upload combined failed spec
        if: needs.ci-test.result != 'success'
        uses: actions/upload-artifact@v3
        with:
          name: combined_failed_spec_ci
          path: ~/combined_failed_spec_ci

      - name: Get Latest flaky Tests
        shell: bash
        run: |
          curl --request POST --url https://yatin-s-workspace-jk8ru5.us-east-1.xata.sh/db/CypressKnownFailures:main/tables/CypressKnownFailuires/query --header 'Authorization: Bearer ${{ secrets.XATA_TOKEN }}' --header 'Content-Type: application/json'|jq -r |grep Spec|cut -d ':' -f 2 2> /dev/null|sed 's/"//g'|sed 's/,//g' >  ~/knownfailures

     # Verify CI test failures against known failures
      - name: Verify CI test failures against known failures
        if: needs.ci-test.result != 'success'
        shell: bash
        run: |
          new_failed_spec_env="<ol>$(comm -1 -3 <(sort ~/knownfailures) <(sort -u ~/combined_failed_spec_ci) | sed 's/|cypress|cypress/\n/g' | sed 's/^/<li>/')</ol>"
          echo "$new_failed_spec_env"
          echo "new_failed_spec_env<<EOF" >> $GITHUB_ENV
          echo "$new_failed_spec_env" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV

      - name: Check ci-test set status
        if: needs.ci-test.result != 'success'
        run: exit 1