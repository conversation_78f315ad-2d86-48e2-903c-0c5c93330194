"use strict";(self.webpackChunkpageplug_datasource_frontend=self.webpackChunkpageplug_datasource_frontend||[]).push([[162],{3781:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(6070);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(212);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(8076);\n/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(antd__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(2691);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(5469);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(5378);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(1957);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(516);\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(1299);\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(react_router_dom__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(3017);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _services_apiClient__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(8876);\n/* harmony import */ var _components_Common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(3128);\n/* harmony import */ var _hooks_useTheme__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(5869);\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nconst { Sider, Content } = antd__WEBPACK_IMPORTED_MODULE_2__.Layout;\r\nconst { Option } = antd__WEBPACK_IMPORTED_MODULE_2__.Select;\r\nconst { TextArea } = antd__WEBPACK_IMPORTED_MODULE_2__.Input;\r\nconst { TabPane } = antd__WEBPACK_IMPORTED_MODULE_2__.Tabs;\r\nconst { Text } = antd__WEBPACK_IMPORTED_MODULE_2__.Typography;\r\nconst QueryEditorContainer = (styled_components__WEBPACK_IMPORTED_MODULE_9___default().div) `\n  height: calc(100vh - 64px);\n  display: flex;\n  flex-direction: column;\n`;\r\nconst EditorHeader = (styled_components__WEBPACK_IMPORTED_MODULE_9___default().div) `\n  padding: 16px 24px;\n  border-bottom: 1px solid #f0f0f0;\n  background: white;\n`;\r\nconst EditorLayout = styled_components__WEBPACK_IMPORTED_MODULE_9___default()((0,antd__WEBPACK_IMPORTED_MODULE_2__.Layout)) `\n  flex: 1;\n  background: white;\n`;\r\nconst EditorSider = styled_components__WEBPACK_IMPORTED_MODULE_9___default()(Sider) `\n  background: ${props => props.$isDark ? '#1f1f1f' : '#fafafa'} !important;\n  border-right: 1px solid ${props => props.$isDark ? '#303030' : '#f0f0f0'};\n`;\r\nconst EditorContent = styled_components__WEBPACK_IMPORTED_MODULE_9___default()(Content) `\n  display: flex;\n  flex-direction: column;\n`;\r\nconst QueryInputArea = (styled_components__WEBPACK_IMPORTED_MODULE_9___default().div) `\n  padding: 16px;\n  border-bottom: 1px solid #f0f0f0;\n`;\r\nconst QueryTextArea = styled_components__WEBPACK_IMPORTED_MODULE_9___default()(TextArea) `\n  font-family: 'JetBrains Mono', 'Consolas', monospace;\n  font-size: 14px;\n  line-height: 1.5;\n`;\r\nconst ResultArea = (styled_components__WEBPACK_IMPORTED_MODULE_9___default().div) `\n  flex: 1;\n  padding: 16px;\n  overflow: auto;\n`;\r\nconst ExecutionInfo = (styled_components__WEBPACK_IMPORTED_MODULE_9___default().div) `\n  padding: 8px 16px;\n  background: ${props => props.$isDark ? '#262626' : '#f5f5f5'};\n  border-bottom: 1px solid ${props => props.$isDark ? '#303030' : '#e8e8e8'};\n  font-size: 12px;\n  color: ${props => props.$isDark ? '#d9d9d9' : '#666'};\n`;\r\nconst QueryEditor = () => {\r\n    const { datasourceId } = (0,react_router_dom__WEBPACK_IMPORTED_MODULE_8__.useParams)();\r\n    const { isDark } = (0,_hooks_useTheme__WEBPACK_IMPORTED_MODULE_12__/* .useTheme */ .D)();\r\n    const [datasources, setDatasources] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\r\n    const [selectedDatasource, setSelectedDatasource] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(datasourceId || '');\r\n    const [queryText, setQueryText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\r\n    const [executing, setExecuting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\r\n    const [executionResult, setExecutionResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\r\n    const [savedQueries, setSavedQueries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\r\n    const [saveModalVisible, setSaveModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\r\n    const [saveForm] = antd__WEBPACK_IMPORTED_MODULE_2__.Form.useForm();\r\n    const loadDatasources = async () => {\r\n        try {\r\n            const response = await _services_apiClient__WEBPACK_IMPORTED_MODULE_10__/* .apiClient */ .u.get('/datasources', {\r\n                params: { workspaceId: 'workspace-123' }\r\n            });\r\n            setDatasources(response.data.content);\r\n        }\r\n        catch (error) {\r\n            antd__WEBPACK_IMPORTED_MODULE_2__.message.error('加载数据源列表失败');\r\n        }\r\n    };\r\n    const loadSavedQueries = async () => {\r\n        try {\r\n            const response = await _services_apiClient__WEBPACK_IMPORTED_MODULE_10__/* .apiClient */ .u.get('/queries', {\r\n                params: { datasourceId: selectedDatasource }\r\n            });\r\n            setSavedQueries(response.data);\r\n        }\r\n        catch (error) {\r\n            console.error('Load saved queries error:', error);\r\n        }\r\n    };\r\n    const executeQuery = async () => {\r\n        if (!selectedDatasource) {\r\n            antd__WEBPACK_IMPORTED_MODULE_2__.message.error('请先选择数据源');\r\n            return;\r\n        }\r\n        if (!queryText.trim()) {\r\n            antd__WEBPACK_IMPORTED_MODULE_2__.message.error('请输入查询语句');\r\n            return;\r\n        }\r\n        try {\r\n            setExecuting(true);\r\n            setExecutionResult(null);\r\n            const response = await _services_apiClient__WEBPACK_IMPORTED_MODULE_10__/* .apiClient */ .u.post(`/datasources/${selectedDatasource}/execute`, {\r\n                query: queryText,\r\n                parameters: []\r\n            });\r\n            setExecutionResult(response.data);\r\n            if (response.data.isExecutionSuccess) {\r\n                antd__WEBPACK_IMPORTED_MODULE_2__.message.success(`查询执行成功，返回 ${response.data.rowsAffected} 行数据`);\r\n            }\r\n            else {\r\n                antd__WEBPACK_IMPORTED_MODULE_2__.message.error(`查询执行失败: ${response.data.error?.message}`);\r\n            }\r\n        }\r\n        catch (error) {\r\n            antd__WEBPACK_IMPORTED_MODULE_2__.message.error('查询执行失败');\r\n            setExecutionResult({\r\n                isExecutionSuccess: false,\r\n                body: [],\r\n                headers: {},\r\n                statusCode: '500',\r\n                executionTime: 0,\r\n                rowsAffected: 0,\r\n                error: {\r\n                    message: '查询执行失败',\r\n                    code: 'EXECUTION_ERROR'\r\n                }\r\n            });\r\n        }\r\n        finally {\r\n            setExecuting(false);\r\n        }\r\n    };\r\n    const saveQuery = async (values) => {\r\n        try {\r\n            await _services_apiClient__WEBPACK_IMPORTED_MODULE_10__/* .apiClient */ .u.post('/queries', {\r\n                name: values.name,\r\n                datasourceId: selectedDatasource,\r\n                query: queryText,\r\n                parameters: [],\r\n                tags: values.tags ? values.tags.split(',').map((tag) => tag.trim()) : []\r\n            });\r\n            antd__WEBPACK_IMPORTED_MODULE_2__.message.success('查询保存成功');\r\n            setSaveModalVisible(false);\r\n            saveForm.resetFields();\r\n            loadSavedQueries();\r\n        }\r\n        catch (error) {\r\n            antd__WEBPACK_IMPORTED_MODULE_2__.message.error('保存查询失败');\r\n        }\r\n    };\r\n    const loadQuery = (query) => {\r\n        setQueryText(query.query);\r\n        antd__WEBPACK_IMPORTED_MODULE_2__.message.success(`已加载查询: ${query.name}`);\r\n    };\r\n    const exportResults = () => {\r\n        if (!executionResult?.body.length) {\r\n            antd__WEBPACK_IMPORTED_MODULE_2__.message.error('没有可导出的数据');\r\n            return;\r\n        }\r\n        const csv = convertToCSV(executionResult.body);\r\n        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });\r\n        const link = document.createElement('a');\r\n        const url = URL.createObjectURL(blob);\r\n        link.setAttribute('href', url);\r\n        link.setAttribute('download', `query_result_${Date.now()}.csv`);\r\n        link.style.visibility = 'hidden';\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n    };\r\n    const convertToCSV = (data) => {\r\n        if (!data.length)\r\n            return '';\r\n        const headers = Object.keys(data[0]);\r\n        const csvHeaders = headers.join(',');\r\n        const csvRows = data.map(row => headers.map(header => {\r\n            const value = row[header];\r\n            return typeof value === 'string' && value.includes(',')\r\n                ? `\"${value}\"`\r\n                : value;\r\n        }).join(','));\r\n        return [csvHeaders, ...csvRows].join('\\n');\r\n    };\r\n    const buildResultColumns = () => {\r\n        if (!executionResult?.body.length)\r\n            return [];\r\n        const firstRow = executionResult.body[0];\r\n        return Object.keys(firstRow).map(key => ({\r\n            title: key,\r\n            dataIndex: key,\r\n            key,\r\n            width: 150,\r\n            render: (value) => {\r\n                if (value === null)\r\n                    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Text, { type: \"secondary\", children: \"NULL\" });\r\n                if (typeof value === 'object')\r\n                    return JSON.stringify(value);\r\n                return String(value);\r\n            }\r\n        }));\r\n    };\r\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\r\n        loadDatasources();\r\n    }, []);\r\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\r\n        if (selectedDatasource) {\r\n            loadSavedQueries();\r\n        }\r\n    }, [selectedDatasource]);\r\n    const queryTemplates = [\r\n        {\r\n            name: '查询所有记录',\r\n            query: 'SELECT * FROM table_name LIMIT 10;'\r\n        },\r\n        {\r\n            name: '统计记录数',\r\n            query: 'SELECT COUNT(*) as total FROM table_name;'\r\n        },\r\n        {\r\n            name: '按条件查询',\r\n            query: 'SELECT * FROM table_name WHERE column_name = \\'value\\' LIMIT 10;'\r\n        }\r\n    ];\r\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(QueryEditorContainer, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(EditorHeader, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Space, { split: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Divider, { type: \"vertical\" }), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Space, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Text, { strong: true, children: \"\\u6570\\u636E\\u6E90:\" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Select, { style: { width: 200 }, placeholder: \"\\u9009\\u62E9\\u6570\\u636E\\u6E90\", value: selectedDatasource, onChange: setSelectedDatasource, children: datasources.map(ds => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Option, { value: ds.id, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Space, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* [\"default\"] */ .A, {}), ds.name] }) }, ds.id))) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Space, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Button, { type: \"primary\", icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* [\"default\"] */ .A, {}), loading: executing, onClick: executeQuery, disabled: !selectedDatasource, children: \"\\u6267\\u884C\\u67E5\\u8BE2\" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Button, { icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* [\"default\"] */ .A, {}), onClick: () => setSaveModalVisible(true), disabled: !queryText.trim(), children: \"\\u4FDD\\u5B58\\u67E5\\u8BE2\" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Button, { icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* [\"default\"] */ .A, {}), onClick: exportResults, disabled: !executionResult?.body.length, children: \"\\u5BFC\\u51FA\\u7ED3\\u679C\" })] })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(EditorLayout, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(EditorSider, { \"$isDark\": isDark, width: 300, collapsible: true, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Tabs, { defaultActiveKey: \"saved\", size: \"small\", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(TabPane, { tab: \"\\u4FDD\\u5B58\\u7684\\u67E5\\u8BE2\", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { style: { padding: '8px' }, children: savedQueries.map(query => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Card, { size: \"small\", style: { marginBottom: 8, cursor: 'pointer' }, onClick: () => loadQuery(query), hoverable: true, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { style: { marginBottom: 4 }, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Text, { strong: true, children: query.name }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { style: { marginBottom: 4 }, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(Text, { type: \"secondary\", style: { fontSize: '12px' }, children: [query.query.substring(0, 50), \"...\"] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { children: query.tags.map(tag => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Tag, { size: \"small\", children: tag }, tag))) })] }, query.id))) }) }, \"saved\"), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(TabPane, { tab: \"\\u67E5\\u8BE2\\u6A21\\u677F\", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { style: { padding: '8px' }, children: queryTemplates.map((template, index) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Card, { size: \"small\", style: { marginBottom: 8, cursor: 'pointer' }, onClick: () => setQueryText(template.query), hoverable: true, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { style: { marginBottom: 4 }, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Text, { strong: true, children: template.name }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Text, { type: \"secondary\", style: { fontSize: '12px' }, children: template.query })] }, index))) }) }, \"templates\")] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(EditorContent, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(QueryInputArea, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(QueryTextArea, { value: queryText, onChange: (e) => setQueryText(e.target.value), placeholder: \"\\u8BF7\\u8F93\\u5165SQL\\u67E5\\u8BE2\\u8BED\\u53E5...\", rows: 8, style: { resize: 'vertical' } }) }), executionResult && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(ExecutionInfo, { \"$isDark\": isDark, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Space, { split: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Divider, { type: \"vertical\" }), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"span\", { children: [\"\\u72B6\\u6001: \", executionResult.isExecutionSuccess ? '成功' : '失败'] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"span\", { children: [\"\\u6267\\u884C\\u65F6\\u95F4: \", executionResult.executionTime, \"ms\"] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"span\", { children: [\"\\u5F71\\u54CD\\u884C\\u6570: \", executionResult.rowsAffected] }), executionResult.error && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"span\", { style: { color: '#ff4d4f' }, children: [\"\\u9519\\u8BEF: \", executionResult.error.message] }))] }) })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(ResultArea, { children: executing ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_Common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_11__/* .LoadingSpinner */ .kt, { text: \"\\u6B63\\u5728\\u6267\\u884C\\u67E5\\u8BE2...\" })) : executionResult ? (executionResult.isExecutionSuccess ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Table, { columns: buildResultColumns(), dataSource: executionResult.body, rowKey: (record, index) => index?.toString() || '0', scroll: { x: true, y: 400 }, pagination: {\r\n                                        showSizeChanger: true,\r\n                                        showQuickJumper: true,\r\n                                        showTotal: (total) => `共 ${total} 条记录`\r\n                                    }, size: \"small\" })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Card, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", { style: { textAlign: 'center', padding: '40px 0' }, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Text, { type: \"danger\", style: { fontSize: '16px' }, children: \"\\u67E5\\u8BE2\\u6267\\u884C\\u5931\\u8D25\" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { style: { marginTop: '16px' }, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Text, { type: \"secondary\", children: executionResult.error?.message }) })] }) }))) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Card, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", { style: { textAlign: 'center', padding: '40px 0' }, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* [\"default\"] */ .A, { style: { fontSize: '48px', color: '#d9d9d9', marginBottom: '16px' } }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Text, { type: \"secondary\", children: \"\\u8BF7\\u8F93\\u5165\\u67E5\\u8BE2\\u8BED\\u53E5\\u5E76\\u70B9\\u51FB\\u6267\\u884C\" }) })] }) })) })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Modal, { title: \"\\u4FDD\\u5B58\\u67E5\\u8BE2\", open: saveModalVisible, onCancel: () => setSaveModalVisible(false), onOk: () => saveForm.submit(), okText: \"\\u4FDD\\u5B58\", cancelText: \"\\u53D6\\u6D88\", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Form, { form: saveForm, onFinish: saveQuery, layout: \"vertical\", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Form.Item, { label: \"\\u67E5\\u8BE2\\u540D\\u79F0\", name: \"name\", rules: [{ required: true, message: '请输入查询名称' }], children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Input, { placeholder: \"\\u8BF7\\u8F93\\u5165\\u67E5\\u8BE2\\u540D\\u79F0\" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Form.Item, { label: \"\\u6807\\u7B7E\", name: \"tags\", help: \"\\u591A\\u4E2A\\u6807\\u7B7E\\u7528\\u9017\\u53F7\\u5206\\u9694\", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Input, { placeholder: \"\\u4F8B\\u5982: users, statistics\" }) })] }) })] }));\r\n};\r\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QueryEditor);\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMzc4MS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQW1EO0FBaUJyQztBQVdhO0FBQ2tCO0FBQ047QUFHYztBQUNtQjtBQUN4QjtBQUVoRCxNQUFNLEVBQUUsS0FBSyxFQUFFLE9BQU8sRUFBRSxHQUFHLHdDQUFNLENBQUM7QUFDbEMsTUFBTSxFQUFFLE1BQU0sRUFBRSxHQUFHLHdDQUFNLENBQUM7QUFDMUIsTUFBTSxFQUFFLFFBQVEsRUFBRSxHQUFHLHVDQUFLLENBQUM7QUFDM0IsTUFBTSxFQUFFLE9BQU8sRUFBRSxHQUFHLHNDQUFJLENBQUM7QUFDekIsTUFBTSxFQUFFLElBQUksRUFBRSxHQUFHLDRDQUFVLENBQUM7QUFHNUIsTUFBTSxvQkFBb0IsR0FBRyw4REFBVTs7OztDQUl0QyxDQUFDO0FBRUYsTUFBTSxZQUFZLEdBQUcsOERBQVU7Ozs7Q0FJOUIsQ0FBQztBQUVGLE1BQU0sWUFBWSxHQUFHLHdEQUFNLENBQUMsNENBQU0sQ0FBQzs7O0NBR2xDLENBQUM7QUFFRixNQUFNLFdBQVcsR0FBRyx3REFBTSxDQUFDLEtBQUssQ0FBQyxDQUFzQjtnQkFDdkMsS0FBSyxDQUFDLEVBQUUsQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLFNBQVM7NEJBQ2xDLEtBQUssQ0FBQyxFQUFFLENBQUMsS0FBSyxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxTQUFTO0NBQ3pFLENBQUM7QUFFRixNQUFNLGFBQWEsR0FBRyx3REFBTSxDQUFDLE9BQU8sQ0FBQzs7O0NBR3BDLENBQUM7QUFFRixNQUFNLGNBQWMsR0FBRyw4REFBVTs7O0NBR2hDLENBQUM7QUFFRixNQUFNLGFBQWEsR0FBRyx3REFBTSxDQUFDLFFBQVEsQ0FBQzs7OztDQUlyQyxDQUFDO0FBRUYsTUFBTSxVQUFVLEdBQUcsOERBQVU7Ozs7Q0FJNUIsQ0FBQztBQUVGLE1BQU0sYUFBYSxHQUFHLDhEQUFVLENBQXNCOztnQkFFdEMsS0FBSyxDQUFDLEVBQUUsQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLFNBQVM7NkJBQ2pDLEtBQUssQ0FBQyxFQUFFLENBQUMsS0FBSyxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxTQUFTOztXQUVoRSxLQUFLLENBQUMsRUFBRSxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsTUFBTTtDQUNyRCxDQUFDO0FBSUYsTUFBTSxXQUFXLEdBQStCLEdBQUcsRUFBRTtJQUNuRCxNQUFNLEVBQUUsWUFBWSxFQUFFLEdBQUcsMkRBQVMsRUFBNEIsQ0FBQztJQUMvRCxNQUFNLEVBQUUsTUFBTSxFQUFFLEdBQUcsbUVBQVEsRUFBRSxDQUFDO0lBRTlCLE1BQU0sQ0FBQyxXQUFXLEVBQUUsY0FBYyxDQUFDLEdBQUcsK0NBQVEsQ0FBZSxFQUFFLENBQUMsQ0FBQztJQUNqRSxNQUFNLENBQUMsa0JBQWtCLEVBQUUscUJBQXFCLENBQUMsR0FBRywrQ0FBUSxDQUFTLFlBQVksSUFBSSxFQUFFLENBQUMsQ0FBQztJQUN6RixNQUFNLENBQUMsU0FBUyxFQUFFLFlBQVksQ0FBQyxHQUFHLCtDQUFRLENBQUMsRUFBRSxDQUFDLENBQUM7SUFDL0MsTUFBTSxDQUFDLFNBQVMsRUFBRSxZQUFZLENBQUMsR0FBRywrQ0FBUSxDQUFDLEtBQUssQ0FBQyxDQUFDO0lBQ2xELE1BQU0sQ0FBQyxlQUFlLEVBQUUsa0JBQWtCLENBQUMsR0FBRywrQ0FBUSxDQUE4QixJQUFJLENBQUMsQ0FBQztJQUMxRixNQUFNLENBQUMsWUFBWSxFQUFFLGVBQWUsQ0FBQyxHQUFHLCtDQUFRLENBQVUsRUFBRSxDQUFDLENBQUM7SUFDOUQsTUFBTSxDQUFDLGdCQUFnQixFQUFFLG1CQUFtQixDQUFDLEdBQUcsK0NBQVEsQ0FBQyxLQUFLLENBQUMsQ0FBQztJQUNoRSxNQUFNLENBQUMsUUFBUSxDQUFDLEdBQUcsc0NBQUksQ0FBQyxPQUFPLEVBQUUsQ0FBQztJQUdsQyxNQUFNLGVBQWUsR0FBRyxLQUFLLElBQUksRUFBRTtRQUNqQyxJQUFJO1lBQ0YsTUFBTSxRQUFRLEdBQUcsTUFBTSxvRUFBUyxDQUFDLEdBQUcsQ0FBQyxjQUFjLEVBQUU7Z0JBQ25ELE1BQU0sRUFBRSxFQUFFLFdBQVcsRUFBRSxlQUFlLEVBQUU7YUFDekMsQ0FBQyxDQUFDO1lBQ0gsY0FBYyxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUM7U0FDdkM7UUFBQyxPQUFPLEtBQUssRUFBRTtZQUNkLHlDQUFPLENBQUMsS0FBSyxDQUFDLFdBQVcsQ0FBQyxDQUFDO1NBQzVCO0lBQ0gsQ0FBQyxDQUFDO0lBR0YsTUFBTSxnQkFBZ0IsR0FBRyxLQUFLLElBQUksRUFBRTtRQUNsQyxJQUFJO1lBQ0YsTUFBTSxRQUFRLEdBQUcsTUFBTSxvRUFBUyxDQUFDLEdBQUcsQ0FBQyxVQUFVLEVBQUU7Z0JBQy9DLE1BQU0sRUFBRSxFQUFFLFlBQVksRUFBRSxrQkFBa0IsRUFBRTthQUM3QyxDQUFDLENBQUM7WUFDSCxlQUFlLENBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxDQUFDO1NBQ2hDO1FBQUMsT0FBTyxLQUFLLEVBQUU7WUFDZCxPQUFPLENBQUMsS0FBSyxDQUFDLDJCQUEyQixFQUFFLEtBQUssQ0FBQyxDQUFDO1NBQ25EO0lBQ0gsQ0FBQyxDQUFDO0lBR0YsTUFBTSxZQUFZLEdBQUcsS0FBSyxJQUFJLEVBQUU7UUFDOUIsSUFBSSxDQUFDLGtCQUFrQixFQUFFO1lBQ3ZCLHlDQUFPLENBQUMsS0FBSyxDQUFDLFNBQVMsQ0FBQyxDQUFDO1lBQ3pCLE9BQU87U0FDUjtRQUVELElBQUksQ0FBQyxTQUFTLENBQUMsSUFBSSxFQUFFLEVBQUU7WUFDckIseUNBQU8sQ0FBQyxLQUFLLENBQUMsU0FBUyxDQUFDLENBQUM7WUFDekIsT0FBTztTQUNSO1FBRUQsSUFBSTtZQUNGLFlBQVksQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUNuQixrQkFBa0IsQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUV6QixNQUFNLFFBQVEsR0FBRyxNQUFNLG9FQUFTLENBQUMsSUFBSSxDQUFDLGdCQUFnQixrQkFBa0IsVUFBVSxFQUFFO2dCQUNsRixLQUFLLEVBQUUsU0FBUztnQkFDaEIsVUFBVSxFQUFFLEVBQUU7YUFDZixDQUFDLENBQUM7WUFFSCxrQkFBa0IsQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLENBQUM7WUFFbEMsSUFBSSxRQUFRLENBQUMsSUFBSSxDQUFDLGtCQUFrQixFQUFFO2dCQUNwQyx5Q0FBTyxDQUFDLE9BQU8sQ0FBQyxhQUFhLFFBQVEsQ0FBQyxJQUFJLENBQUMsWUFBWSxNQUFNLENBQUMsQ0FBQzthQUNoRTtpQkFBTTtnQkFDTCx5Q0FBTyxDQUFDLEtBQUssQ0FBQyxXQUFXLFFBQVEsQ0FBQyxJQUFJLENBQUMsS0FBSyxFQUFFLE9BQU8sRUFBRSxDQUFDLENBQUM7YUFDMUQ7U0FDRjtRQUFDLE9BQU8sS0FBSyxFQUFFO1lBQ2QseUNBQU8sQ0FBQyxLQUFLLENBQUMsUUFBUSxDQUFDLENBQUM7WUFDeEIsa0JBQWtCLENBQUM7Z0JBQ2pCLGtCQUFrQixFQUFFLEtBQUs7Z0JBQ3pCLElBQUksRUFBRSxFQUFFO2dCQUNSLE9BQU8sRUFBRSxFQUFFO2dCQUNYLFVBQVUsRUFBRSxLQUFLO2dCQUNqQixhQUFhLEVBQUUsQ0FBQztnQkFDaEIsWUFBWSxFQUFFLENBQUM7Z0JBQ2YsS0FBSyxFQUFFO29CQUNMLE9BQU8sRUFBRSxRQUFRO29CQUNqQixJQUFJLEVBQUUsaUJBQWlCO2lCQUN4QjthQUNGLENBQUMsQ0FBQztTQUNKO2dCQUFTO1lBQ1IsWUFBWSxDQUFDLEtBQUssQ0FBQyxDQUFDO1NBQ3JCO0lBQ0gsQ0FBQyxDQUFDO0lBR0YsTUFBTSxTQUFTLEdBQUcsS0FBSyxFQUFFLE1BQVcsRUFBRSxFQUFFO1FBQ3RDLElBQUk7WUFDRixNQUFNLG9FQUFTLENBQUMsSUFBSSxDQUFDLFVBQVUsRUFBRTtnQkFDL0IsSUFBSSxFQUFFLE1BQU0sQ0FBQyxJQUFJO2dCQUNqQixZQUFZLEVBQUUsa0JBQWtCO2dCQUNoQyxLQUFLLEVBQUUsU0FBUztnQkFDaEIsVUFBVSxFQUFFLEVBQUU7Z0JBQ2QsSUFBSSxFQUFFLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLEdBQVcsRUFBRSxFQUFFLENBQUMsR0FBRyxDQUFDLElBQUksRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUU7YUFDakYsQ0FBQyxDQUFDO1lBRUgseUNBQU8sQ0FBQyxPQUFPLENBQUMsUUFBUSxDQUFDLENBQUM7WUFDMUIsbUJBQW1CLENBQUMsS0FBSyxDQUFDLENBQUM7WUFDM0IsUUFBUSxDQUFDLFdBQVcsRUFBRSxDQUFDO1lBQ3ZCLGdCQUFnQixFQUFFLENBQUM7U0FDcEI7UUFBQyxPQUFPLEtBQUssRUFBRTtZQUNkLHlDQUFPLENBQUMsS0FBSyxDQUFDLFFBQVEsQ0FBQyxDQUFDO1NBQ3pCO0lBQ0gsQ0FBQyxDQUFDO0lBR0YsTUFBTSxTQUFTLEdBQUcsQ0FBQyxLQUFZLEVBQUUsRUFBRTtRQUNqQyxZQUFZLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxDQUFDO1FBQzFCLHlDQUFPLENBQUMsT0FBTyxDQUFDLFVBQVUsS0FBSyxDQUFDLElBQUksRUFBRSxDQUFDLENBQUM7SUFDMUMsQ0FBQyxDQUFDO0lBR0YsTUFBTSxhQUFhLEdBQUcsR0FBRyxFQUFFO1FBQ3pCLElBQUksQ0FBQyxlQUFlLEVBQUUsSUFBSSxDQUFDLE1BQU0sRUFBRTtZQUNqQyx5Q0FBTyxDQUFDLEtBQUssQ0FBQyxVQUFVLENBQUMsQ0FBQztZQUMxQixPQUFPO1NBQ1I7UUFFRCxNQUFNLEdBQUcsR0FBRyxZQUFZLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxDQUFDO1FBQy9DLE1BQU0sSUFBSSxHQUFHLElBQUksSUFBSSxDQUFDLENBQUMsR0FBRyxDQUFDLEVBQUUsRUFBRSxJQUFJLEVBQUUseUJBQXlCLEVBQUUsQ0FBQyxDQUFDO1FBQ2xFLE1BQU0sSUFBSSxHQUFHLFFBQVEsQ0FBQyxhQUFhLENBQUMsR0FBRyxDQUFDLENBQUM7UUFDekMsTUFBTSxHQUFHLEdBQUcsR0FBRyxDQUFDLGVBQWUsQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUN0QyxJQUFJLENBQUMsWUFBWSxDQUFDLE1BQU0sRUFBRSxHQUFHLENBQUMsQ0FBQztRQUMvQixJQUFJLENBQUMsWUFBWSxDQUFDLFVBQVUsRUFBRSxnQkFBZ0IsSUFBSSxDQUFDLEdBQUcsRUFBRSxNQUFNLENBQUMsQ0FBQztRQUNoRSxJQUFJLENBQUMsS0FBSyxDQUFDLFVBQVUsR0FBRyxRQUFRLENBQUM7UUFDakMsUUFBUSxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLENBQUM7UUFDaEMsSUFBSSxDQUFDLEtBQUssRUFBRSxDQUFDO1FBQ2IsUUFBUSxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLENBQUM7SUFDbEMsQ0FBQyxDQUFDO0lBR0YsTUFBTSxZQUFZLEdBQUcsQ0FBQyxJQUFXLEVBQUUsRUFBRTtRQUNuQyxJQUFJLENBQUMsSUFBSSxDQUFDLE1BQU07WUFBRSxPQUFPLEVBQUUsQ0FBQztRQUU1QixNQUFNLE9BQU8sR0FBRyxNQUFNLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ3JDLE1BQU0sVUFBVSxHQUFHLE9BQU8sQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUM7UUFDckMsTUFBTSxPQUFPLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUM3QixPQUFPLENBQUMsR0FBRyxDQUFDLE1BQU0sQ0FBQyxFQUFFO1lBQ25CLE1BQU0sS0FBSyxHQUFHLEdBQUcsQ0FBQyxNQUFNLENBQUMsQ0FBQztZQUMxQixPQUFPLE9BQU8sS0FBSyxLQUFLLFFBQVEsSUFBSSxLQUFLLENBQUMsUUFBUSxDQUFDLEdBQUcsQ0FBQztnQkFDckQsQ0FBQyxDQUFDLElBQUksS0FBSyxHQUFHO2dCQUNkLENBQUMsQ0FBQyxLQUFLLENBQUM7UUFDWixDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQ2IsQ0FBQztRQUVGLE9BQU8sQ0FBQyxVQUFVLEVBQUUsR0FBRyxPQUFPLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7SUFDN0MsQ0FBQyxDQUFDO0lBR0YsTUFBTSxrQkFBa0IsR0FBRyxHQUFHLEVBQUU7UUFDOUIsSUFBSSxDQUFDLGVBQWUsRUFBRSxJQUFJLENBQUMsTUFBTTtZQUFFLE9BQU8sRUFBRSxDQUFDO1FBRTdDLE1BQU0sUUFBUSxHQUFHLGVBQWUsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDekMsT0FBTyxNQUFNLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLENBQUM7WUFDdkMsS0FBSyxFQUFFLEdBQUc7WUFDVixTQUFTLEVBQUUsR0FBRztZQUNkLEdBQUc7WUFDSCxLQUFLLEVBQUUsR0FBRztZQUNWLE1BQU0sRUFBRSxDQUFDLEtBQVUsRUFBRSxFQUFFO2dCQUNyQixJQUFJLEtBQUssS0FBSyxJQUFJO29CQUFFLE9BQU8sdURBQUMsSUFBSSxJQUFDLElBQUksRUFBQyxXQUFXLHFCQUFZLENBQUM7Z0JBQzlELElBQUksT0FBTyxLQUFLLEtBQUssUUFBUTtvQkFBRSxPQUFPLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLENBQUM7Z0JBQzVELE9BQU8sTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDO1lBQ3ZCLENBQUM7U0FDRixDQUFDLENBQUMsQ0FBQztJQUNOLENBQUMsQ0FBQztJQUVGLGdEQUFTLENBQUMsR0FBRyxFQUFFO1FBQ2IsZUFBZSxFQUFFLENBQUM7SUFDcEIsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxDQUFDO0lBRVAsZ0RBQVMsQ0FBQyxHQUFHLEVBQUU7UUFDYixJQUFJLGtCQUFrQixFQUFFO1lBQ3RCLGdCQUFnQixFQUFFLENBQUM7U0FDcEI7SUFDSCxDQUFDLEVBQUUsQ0FBQyxrQkFBa0IsQ0FBQyxDQUFDLENBQUM7SUFHekIsTUFBTSxjQUFjLEdBQUc7UUFDckI7WUFDRSxJQUFJLEVBQUUsUUFBUTtZQUNkLEtBQUssRUFBRSxvQ0FBb0M7U0FDNUM7UUFDRDtZQUNFLElBQUksRUFBRSxPQUFPO1lBQ2IsS0FBSyxFQUFFLDJDQUEyQztTQUNuRDtRQUNEO1lBQ0UsSUFBSSxFQUFFLE9BQU87WUFDYixLQUFLLEVBQUUsa0VBQWtFO1NBQzFFO0tBQ0YsQ0FBQztJQUVGLE9BQU8sQ0FDTCx3REFBQyxvQkFBb0IsZUFFbkIsdURBQUMsWUFBWSxjQUNYLHdEQUFDLHVDQUFLLElBQUMsS0FBSyxFQUFFLHVEQUFDLHlDQUFPLElBQUMsSUFBSSxFQUFDLFVBQVUsR0FBRyxhQUN2Qyx3REFBQyx1Q0FBSyxlQUNKLHVEQUFDLElBQUksSUFBQyxNQUFNLDBDQUFZLEVBQ3hCLHVEQUFDLHdDQUFNLElBQ0wsS0FBSyxFQUFFLEVBQUUsS0FBSyxFQUFFLEdBQUcsRUFBRSxFQUNyQixXQUFXLEVBQUMsZ0NBQU8sRUFDbkIsS0FBSyxFQUFFLGtCQUFrQixFQUN6QixRQUFRLEVBQUUscUJBQXFCLFlBRTlCLFdBQVcsQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUNyQix1REFBQyxNQUFNLElBQWEsS0FBSyxFQUFFLEVBQUUsQ0FBQyxFQUFFLFlBQzlCLHdEQUFDLHVDQUFLLGVBQ0osdURBQUMsa0VBQWdCLEtBQUcsRUFDbkIsRUFBRSxDQUFDLElBQUksSUFDRixJQUpHLEVBQUUsQ0FBQyxFQUFFLENBS1QsQ0FDVixDQUFDLEdBQ0ssSUFDSCxFQUVSLHdEQUFDLHVDQUFLLGVBQ0osdURBQUMsd0NBQU0sSUFDTCxJQUFJLEVBQUMsU0FBUyxFQUNkLElBQUksRUFBRSx1REFBQyxrRUFBa0IsS0FBRyxFQUM1QixPQUFPLEVBQUUsU0FBUyxFQUNsQixPQUFPLEVBQUUsWUFBWSxFQUNyQixRQUFRLEVBQUUsQ0FBQyxrQkFBa0IseUNBR3RCLEVBQ1QsdURBQUMsd0NBQU0sSUFDTCxJQUFJLEVBQUUsdURBQUMsa0VBQVksS0FBRyxFQUN0QixPQUFPLEVBQUUsR0FBRyxFQUFFLENBQUMsbUJBQW1CLENBQUMsSUFBSSxDQUFDLEVBQ3hDLFFBQVEsRUFBRSxDQUFDLFNBQVMsQ0FBQyxJQUFJLEVBQUUseUNBR3BCLEVBQ1QsdURBQUMsd0NBQU0sSUFDTCxJQUFJLEVBQUUsdURBQUMsa0VBQWdCLEtBQUcsRUFDMUIsT0FBTyxFQUFFLGFBQWEsRUFDdEIsUUFBUSxFQUFFLENBQUMsZUFBZSxFQUFFLElBQUksQ0FBQyxNQUFNLHlDQUdoQyxJQUNILElBQ0YsR0FDSyxFQUdmLHdEQUFDLFlBQVksZUFFWCx1REFBQyxXQUFXLGVBQVUsTUFBTSxFQUFFLEtBQUssRUFBRSxHQUFHLEVBQUUsV0FBVyxrQkFDbkQsd0RBQUMsc0NBQUksSUFBQyxnQkFBZ0IsRUFBQyxPQUFPLEVBQUMsSUFBSSxFQUFDLE9BQU8sYUFDekMsdURBQUMsT0FBTyxJQUFDLEdBQUcsRUFBQyxnQ0FBTyxZQUNsQixnRUFBSyxLQUFLLEVBQUUsRUFBRSxPQUFPLEVBQUUsS0FBSyxFQUFFLFlBQzNCLFlBQVksQ0FBQyxHQUFHLENBQUMsS0FBSyxDQUFDLEVBQUUsQ0FBQyxDQUN6Qix3REFBQyxzQ0FBSSxJQUVILElBQUksRUFBQyxPQUFPLEVBQ1osS0FBSyxFQUFFLEVBQUUsWUFBWSxFQUFFLENBQUMsRUFBRSxNQUFNLEVBQUUsU0FBUyxFQUFFLEVBQzdDLE9BQU8sRUFBRSxHQUFHLEVBQUUsQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLEVBQy9CLFNBQVMsbUJBRVQsZ0VBQUssS0FBSyxFQUFFLEVBQUUsWUFBWSxFQUFFLENBQUMsRUFBRSxZQUM3Qix1REFBQyxJQUFJLElBQUMsTUFBTSxrQkFBRSxLQUFLLENBQUMsSUFBSSxHQUFRLEdBQzVCLEVBQ04sZ0VBQUssS0FBSyxFQUFFLEVBQUUsWUFBWSxFQUFFLENBQUMsRUFBRSxZQUM3Qix3REFBQyxJQUFJLElBQUMsSUFBSSxFQUFDLFdBQVcsRUFBQyxLQUFLLEVBQUUsRUFBRSxRQUFRLEVBQUUsTUFBTSxFQUFFLGFBQy9DLEtBQUssQ0FBQyxLQUFLLENBQUMsU0FBUyxDQUFDLENBQUMsRUFBRSxFQUFFLENBQUMsV0FDeEIsR0FDSCxFQUNOLDBFQUNHLEtBQUssQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsQ0FDckIsdURBQUMscUNBQUcsSUFBVyxJQUFJLEVBQUMsT0FBTyxZQUFFLEdBQUcsSUFBdEIsR0FBRyxDQUEwQixDQUN4QyxDQUFDLEdBQ0UsS0FsQkQsS0FBSyxDQUFDLEVBQUUsQ0FtQlIsQ0FDUixDQUFDLEdBQ0UsSUF6QmlCLE9BQU8sQ0EwQnRCLEVBQ1YsdURBQUMsT0FBTyxJQUFDLEdBQUcsRUFBQywwQkFBTSxZQUNqQixnRUFBSyxLQUFLLEVBQUUsRUFBRSxPQUFPLEVBQUUsS0FBSyxFQUFFLFlBQzNCLGNBQWMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxRQUFRLEVBQUUsS0FBSyxFQUFFLEVBQUUsQ0FBQyxDQUN2Qyx3REFBQyxzQ0FBSSxJQUVILElBQUksRUFBQyxPQUFPLEVBQ1osS0FBSyxFQUFFLEVBQUUsWUFBWSxFQUFFLENBQUMsRUFBRSxNQUFNLEVBQUUsU0FBUyxFQUFFLEVBQzdDLE9BQU8sRUFBRSxHQUFHLEVBQUUsQ0FBQyxZQUFZLENBQUMsUUFBUSxDQUFDLEtBQUssQ0FBQyxFQUMzQyxTQUFTLG1CQUVULGdFQUFLLEtBQUssRUFBRSxFQUFFLFlBQVksRUFBRSxDQUFDLEVBQUUsWUFDN0IsdURBQUMsSUFBSSxJQUFDLE1BQU0sa0JBQUUsUUFBUSxDQUFDLElBQUksR0FBUSxHQUMvQixFQUNOLHVEQUFDLElBQUksSUFBQyxJQUFJLEVBQUMsV0FBVyxFQUFDLEtBQUssRUFBRSxFQUFFLFFBQVEsRUFBRSxNQUFNLEVBQUUsWUFDL0MsUUFBUSxDQUFDLEtBQUssR0FDVixLQVhGLEtBQUssQ0FZTCxDQUNSLENBQUMsR0FDRSxJQWxCZ0IsV0FBVyxDQW1CekIsSUFDTCxHQUNLLEVBR2Qsd0RBQUMsYUFBYSxlQUVaLHVEQUFDLGNBQWMsY0FDYix1REFBQyxhQUFhLElBQ1osS0FBSyxFQUFFLFNBQVMsRUFDaEIsUUFBUSxFQUFFLENBQUMsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsRUFDN0MsV0FBVyxFQUFDLGtEQUFlLEVBQzNCLElBQUksRUFBRSxDQUFDLEVBQ1AsS0FBSyxFQUFFLEVBQUUsTUFBTSxFQUFFLFVBQVUsRUFBRSxHQUM3QixHQUNhLEVBR2hCLGVBQWUsSUFBSSxDQUNsQix1REFBQyxhQUFhLGVBQVUsTUFBTSxZQUM1Qix3REFBQyx1Q0FBSyxJQUFDLEtBQUssRUFBRSx1REFBQyx5Q0FBTyxJQUFDLElBQUksRUFBQyxVQUFVLEdBQUcsYUFDdkMsK0ZBQ08sZUFBZSxDQUFDLGtCQUFrQixDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLElBQUksSUFDaEQsRUFDUCwyR0FBYSxlQUFlLENBQUMsYUFBYSxVQUFVLEVBQ3BELDJHQUFhLGVBQWUsQ0FBQyxZQUFZLElBQVEsRUFDaEQsZUFBZSxDQUFDLEtBQUssSUFBSSxDQUN4QixrRUFBTSxLQUFLLEVBQUUsRUFBRSxLQUFLLEVBQUUsU0FBUyxFQUFFLCtCQUMxQixlQUFlLENBQUMsS0FBSyxDQUFDLE9BQU8sSUFDN0IsQ0FDUixJQUNLLEdBQ00sQ0FDakIsRUFHRCx1REFBQyxVQUFVLGNBQ1IsU0FBUyxDQUFDLENBQUMsQ0FBQyxDQUNYLHVEQUFDLHdGQUFjLElBQUMsSUFBSSxFQUFDLHlDQUFXLEdBQUcsQ0FDcEMsQ0FBQyxDQUFDLENBQUMsZUFBZSxDQUFDLENBQUMsQ0FBQyxDQUNwQixlQUFlLENBQUMsa0JBQWtCLENBQUMsQ0FBQyxDQUFDLENBQ25DLHVEQUFDLHVDQUFLLElBQ0osT0FBTyxFQUFFLGtCQUFrQixFQUFFLEVBQzdCLFVBQVUsRUFBRSxlQUFlLENBQUMsSUFBSSxFQUNoQyxNQUFNLEVBQUUsQ0FBQyxNQUFNLEVBQUUsS0FBSyxFQUFFLEVBQUUsQ0FBQyxLQUFLLEVBQUUsUUFBUSxFQUFFLElBQUksR0FBRyxFQUNuRCxNQUFNLEVBQUUsRUFBRSxDQUFDLEVBQUUsSUFBSSxFQUFFLENBQUMsRUFBRSxHQUFHLEVBQUUsRUFDM0IsVUFBVSxFQUFFO3dDQUNWLGVBQWUsRUFBRSxJQUFJO3dDQUNyQixlQUFlLEVBQUUsSUFBSTt3Q0FDckIsU0FBUyxFQUFFLENBQUMsS0FBSyxFQUFFLEVBQUUsQ0FBQyxLQUFLLEtBQUssTUFBTTtxQ0FDdkMsRUFDRCxJQUFJLEVBQUMsT0FBTyxHQUNaLENBQ0gsQ0FBQyxDQUFDLENBQUMsQ0FDRix1REFBQyxzQ0FBSSxjQUNILGlFQUFLLEtBQUssRUFBRSxFQUFFLFNBQVMsRUFBRSxRQUFRLEVBQUUsT0FBTyxFQUFFLFFBQVEsRUFBRSxhQUNwRCx1REFBQyxJQUFJLElBQUMsSUFBSSxFQUFDLFFBQVEsRUFBQyxLQUFLLEVBQUUsRUFBRSxRQUFRLEVBQUUsTUFBTSxFQUFFLHFEQUV4QyxFQUNQLGdFQUFLLEtBQUssRUFBRSxFQUFFLFNBQVMsRUFBRSxNQUFNLEVBQUUsWUFDL0IsdURBQUMsSUFBSSxJQUFDLElBQUksRUFBQyxXQUFXLFlBQ25CLGVBQWUsQ0FBQyxLQUFLLEVBQUUsT0FBTyxHQUMxQixHQUNILElBQ0YsR0FDRCxDQUNSLENBQ0YsQ0FBQyxDQUFDLENBQUMsQ0FDRix1REFBQyxzQ0FBSSxjQUNILGlFQUFLLEtBQUssRUFBRSxFQUFFLFNBQVMsRUFBRSxRQUFRLEVBQUUsT0FBTyxFQUFFLFFBQVEsRUFBRSxhQUNwRCx1REFBQyxrRUFBWSxJQUFDLEtBQUssRUFBRSxFQUFFLFFBQVEsRUFBRSxNQUFNLEVBQUUsS0FBSyxFQUFFLFNBQVMsRUFBRSxZQUFZLEVBQUUsTUFBTSxFQUFFLEdBQUksRUFDckYsMEVBQ0UsdURBQUMsSUFBSSxJQUFDLElBQUksRUFBQyxXQUFXLHlGQUFvQixHQUN0QyxJQUNGLEdBQ0QsQ0FDUixHQUNVLElBQ0MsSUFDSCxFQUdmLHVEQUFDLHVDQUFLLElBQ0osS0FBSyxFQUFDLDBCQUFNLEVBQ1osSUFBSSxFQUFFLGdCQUFnQixFQUN0QixRQUFRLEVBQUUsR0FBRyxFQUFFLENBQUMsbUJBQW1CLENBQUMsS0FBSyxDQUFDLEVBQzFDLElBQUksRUFBRSxHQUFHLEVBQUUsQ0FBQyxRQUFRLENBQUMsTUFBTSxFQUFFLEVBQzdCLE1BQU0sRUFBQyxjQUFJLEVBQ1gsVUFBVSxFQUFDLGNBQUksWUFFZix3REFBQyxzQ0FBSSxJQUFDLElBQUksRUFBRSxRQUFRLEVBQUUsUUFBUSxFQUFFLFNBQVMsRUFBRSxNQUFNLEVBQUMsVUFBVSxhQUMxRCx1REFBQyxzQ0FBSSxDQUFDLElBQUksSUFDUixLQUFLLEVBQUMsMEJBQU0sRUFDWixJQUFJLEVBQUMsTUFBTSxFQUNYLEtBQUssRUFBRSxDQUFDLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxPQUFPLEVBQUUsU0FBUyxFQUFFLENBQUMsWUFFL0MsdURBQUMsdUNBQUssSUFBQyxXQUFXLEVBQUMsNENBQVMsR0FBRyxHQUNyQixFQUNaLHVEQUFDLHNDQUFJLENBQUMsSUFBSSxJQUNSLEtBQUssRUFBQyxjQUFJLEVBQ1YsSUFBSSxFQUFDLE1BQU0sRUFDWCxJQUFJLEVBQUMsd0RBQVcsWUFFaEIsdURBQUMsdUNBQUssSUFBQyxXQUFXLEVBQUMsaUNBQXVCLEdBQUcsR0FDbkMsSUFDUCxHQUNELElBQ2EsQ0FDeEIsQ0FBQztBQUNKLENBQUMsQ0FBQztBQUVGLGlFQUFlLFdBQVcsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3BhZ2VwbHVnLWRhdGFzb3VyY2UtZnJvbnRlbmQvLi9zcmMvcGFnZXMvUXVlcnlFZGl0b3IvaW5kZXgudHN4PzBmMGIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQge1xuICBMYXlvdXQsXG4gIENhcmQsXG4gIEJ1dHRvbixcbiAgU2VsZWN0LFxuICBJbnB1dCxcbiAgVGFibGUsXG4gIFRhYnMsXG4gIFNwYWNlLFxuICBUb29sdGlwLFxuICBtZXNzYWdlLFxuICBNb2RhbCxcbiAgRm9ybSxcbiAgVGFnLFxuICBEaXZpZGVyLFxuICBUeXBvZ3JhcGh5XG59IGZyb20gJ2FudGQnO1xuaW1wb3J0IHtcbiAgUGxheUNpcmNsZU91dGxpbmVkLFxuICBTYXZlT3V0bGluZWQsXG4gIEhpc3RvcnlPdXRsaW5lZCxcbiAgRGF0YWJhc2VPdXRsaW5lZCxcbiAgVGFibGVPdXRsaW5lZCxcbiAgQ29kZU91dGxpbmVkLFxuICBEb3dubG9hZE91dGxpbmVkLFxuICBGdWxsc2NyZWVuT3V0bGluZWQsXG4gIFNldHRpbmdPdXRsaW5lZFxufSBmcm9tICdAYW50LWRlc2lnbi9pY29ucyc7XG5pbXBvcnQgeyB1c2VQYXJhbXMgfSBmcm9tICdyZWFjdC1yb3V0ZXItZG9tJztcbmltcG9ydCBzdHlsZWQgZnJvbSAnc3R5bGVkLWNvbXBvbmVudHMnO1xuXG5pbXBvcnQgeyBEYXRhc291cmNlLCBRdWVyeUV4ZWN1dGlvblJlc3VsdCwgUXVlcnkgfSBmcm9tICcuLi8uLi90eXBlcy9hcGknO1xuaW1wb3J0IHsgYXBpQ2xpZW50IH0gZnJvbSAnLi4vLi4vc2VydmljZXMvYXBpQ2xpZW50JztcbmltcG9ydCB7IExvYWRpbmdTcGlubmVyIH0gZnJvbSAnLi4vLi4vY29tcG9uZW50cy9Db21tb24vTG9hZGluZ1NwaW5uZXInO1xuaW1wb3J0IHsgdXNlVGhlbWUgfSBmcm9tICcuLi8uLi9ob29rcy91c2VUaGVtZSc7XG5cbmNvbnN0IHsgU2lkZXIsIENvbnRlbnQgfSA9IExheW91dDtcbmNvbnN0IHsgT3B0aW9uIH0gPSBTZWxlY3Q7XG5jb25zdCB7IFRleHRBcmVhIH0gPSBJbnB1dDtcbmNvbnN0IHsgVGFiUGFuZSB9ID0gVGFicztcbmNvbnN0IHsgVGV4dCB9ID0gVHlwb2dyYXBoeTtcblxuLy8g5qC35byP5YyW57uE5Lu2XG5jb25zdCBRdWVyeUVkaXRvckNvbnRhaW5lciA9IHN0eWxlZC5kaXZgXG4gIGhlaWdodDogY2FsYygxMDB2aCAtIDY0cHgpO1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuYDtcblxuY29uc3QgRWRpdG9ySGVhZGVyID0gc3R5bGVkLmRpdmBcbiAgcGFkZGluZzogMTZweCAyNHB4O1xuICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2YwZjBmMDtcbiAgYmFja2dyb3VuZDogd2hpdGU7XG5gO1xuXG5jb25zdCBFZGl0b3JMYXlvdXQgPSBzdHlsZWQoTGF5b3V0KWBcbiAgZmxleDogMTtcbiAgYmFja2dyb3VuZDogd2hpdGU7XG5gO1xuXG5jb25zdCBFZGl0b3JTaWRlciA9IHN0eWxlZChTaWRlcik8eyAkaXNEYXJrOiBib29sZWFuIH0+YFxuICBiYWNrZ3JvdW5kOiAke3Byb3BzID0+IHByb3BzLiRpc0RhcmsgPyAnIzFmMWYxZicgOiAnI2ZhZmFmYSd9ICFpbXBvcnRhbnQ7XG4gIGJvcmRlci1yaWdodDogMXB4IHNvbGlkICR7cHJvcHMgPT4gcHJvcHMuJGlzRGFyayA/ICcjMzAzMDMwJyA6ICcjZjBmMGYwJ307XG5gO1xuXG5jb25zdCBFZGl0b3JDb250ZW50ID0gc3R5bGVkKENvbnRlbnQpYFxuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuYDtcblxuY29uc3QgUXVlcnlJbnB1dEFyZWEgPSBzdHlsZWQuZGl2YFxuICBwYWRkaW5nOiAxNnB4O1xuICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2YwZjBmMDtcbmA7XG5cbmNvbnN0IFF1ZXJ5VGV4dEFyZWEgPSBzdHlsZWQoVGV4dEFyZWEpYFxuICBmb250LWZhbWlseTogJ0pldEJyYWlucyBNb25vJywgJ0NvbnNvbGFzJywgbW9ub3NwYWNlO1xuICBmb250LXNpemU6IDE0cHg7XG4gIGxpbmUtaGVpZ2h0OiAxLjU7XG5gO1xuXG5jb25zdCBSZXN1bHRBcmVhID0gc3R5bGVkLmRpdmBcbiAgZmxleDogMTtcbiAgcGFkZGluZzogMTZweDtcbiAgb3ZlcmZsb3c6IGF1dG87XG5gO1xuXG5jb25zdCBFeGVjdXRpb25JbmZvID0gc3R5bGVkLmRpdjx7ICRpc0Rhcms6IGJvb2xlYW4gfT5gXG4gIHBhZGRpbmc6IDhweCAxNnB4O1xuICBiYWNrZ3JvdW5kOiAke3Byb3BzID0+IHByb3BzLiRpc0RhcmsgPyAnIzI2MjYyNicgOiAnI2Y1ZjVmNSd9O1xuICBib3JkZXItYm90dG9tOiAxcHggc29saWQgJHtwcm9wcyA9PiBwcm9wcy4kaXNEYXJrID8gJyMzMDMwMzAnIDogJyNlOGU4ZTgnfTtcbiAgZm9udC1zaXplOiAxMnB4O1xuICBjb2xvcjogJHtwcm9wcyA9PiBwcm9wcy4kaXNEYXJrID8gJyNkOWQ5ZDknIDogJyM2NjYnfTtcbmA7XG5cbmludGVyZmFjZSBRdWVyeUVkaXRvclByb3BzIHt9XG5cbmNvbnN0IFF1ZXJ5RWRpdG9yOiBSZWFjdC5GQzxRdWVyeUVkaXRvclByb3BzPiA9ICgpID0+IHtcbiAgY29uc3QgeyBkYXRhc291cmNlSWQgfSA9IHVzZVBhcmFtczx7IGRhdGFzb3VyY2VJZDogc3RyaW5nIH0+KCk7XG4gIGNvbnN0IHsgaXNEYXJrIH0gPSB1c2VUaGVtZSgpO1xuXG4gIGNvbnN0IFtkYXRhc291cmNlcywgc2V0RGF0YXNvdXJjZXNdID0gdXNlU3RhdGU8RGF0YXNvdXJjZVtdPihbXSk7XG4gIGNvbnN0IFtzZWxlY3RlZERhdGFzb3VyY2UsIHNldFNlbGVjdGVkRGF0YXNvdXJjZV0gPSB1c2VTdGF0ZTxzdHJpbmc+KGRhdGFzb3VyY2VJZCB8fCAnJyk7XG4gIGNvbnN0IFtxdWVyeVRleHQsIHNldFF1ZXJ5VGV4dF0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtleGVjdXRpbmcsIHNldEV4ZWN1dGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtleGVjdXRpb25SZXN1bHQsIHNldEV4ZWN1dGlvblJlc3VsdF0gPSB1c2VTdGF0ZTxRdWVyeUV4ZWN1dGlvblJlc3VsdCB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbc2F2ZWRRdWVyaWVzLCBzZXRTYXZlZFF1ZXJpZXNdID0gdXNlU3RhdGU8UXVlcnlbXT4oW10pO1xuICBjb25zdCBbc2F2ZU1vZGFsVmlzaWJsZSwgc2V0U2F2ZU1vZGFsVmlzaWJsZV0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzYXZlRm9ybV0gPSBGb3JtLnVzZUZvcm0oKTtcblxuICAvLyDliqDovb3mlbDmja7mupDliJfooahcbiAgY29uc3QgbG9hZERhdGFzb3VyY2VzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5nZXQoJy9kYXRhc291cmNlcycsIHtcbiAgICAgICAgcGFyYW1zOiB7IHdvcmtzcGFjZUlkOiAnd29ya3NwYWNlLTEyMycgfVxuICAgICAgfSk7XG4gICAgICBzZXREYXRhc291cmNlcyhyZXNwb25zZS5kYXRhLmNvbnRlbnQpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBtZXNzYWdlLmVycm9yKCfliqDovb3mlbDmja7mupDliJfooajlpLHotKUnKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g5Yqg6L295L+d5a2Y55qE5p+l6K+iXG4gIGNvbnN0IGxvYWRTYXZlZFF1ZXJpZXMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LmdldCgnL3F1ZXJpZXMnLCB7XG4gICAgICAgIHBhcmFtczogeyBkYXRhc291cmNlSWQ6IHNlbGVjdGVkRGF0YXNvdXJjZSB9XG4gICAgICB9KTtcbiAgICAgIHNldFNhdmVkUXVlcmllcyhyZXNwb25zZS5kYXRhKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignTG9hZCBzYXZlZCBxdWVyaWVzIGVycm9yOicsIGVycm9yKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g5omn6KGM5p+l6K+iXG4gIGNvbnN0IGV4ZWN1dGVRdWVyeSA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIXNlbGVjdGVkRGF0YXNvdXJjZSkge1xuICAgICAgbWVzc2FnZS5lcnJvcign6K+35YWI6YCJ5oup5pWw5o2u5rqQJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgaWYgKCFxdWVyeVRleHQudHJpbSgpKSB7XG4gICAgICBtZXNzYWdlLmVycm9yKCfor7fovpPlhaXmn6Xor6Lor63lj6UnKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgc2V0RXhlY3V0aW5nKHRydWUpO1xuICAgICAgc2V0RXhlY3V0aW9uUmVzdWx0KG51bGwpO1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5wb3N0KGAvZGF0YXNvdXJjZXMvJHtzZWxlY3RlZERhdGFzb3VyY2V9L2V4ZWN1dGVgLCB7XG4gICAgICAgIHF1ZXJ5OiBxdWVyeVRleHQsXG4gICAgICAgIHBhcmFtZXRlcnM6IFtdXG4gICAgICB9KTtcblxuICAgICAgc2V0RXhlY3V0aW9uUmVzdWx0KHJlc3BvbnNlLmRhdGEpO1xuXG4gICAgICBpZiAocmVzcG9uc2UuZGF0YS5pc0V4ZWN1dGlvblN1Y2Nlc3MpIHtcbiAgICAgICAgbWVzc2FnZS5zdWNjZXNzKGDmn6Xor6LmiafooYzmiJDlip/vvIzov5Tlm54gJHtyZXNwb25zZS5kYXRhLnJvd3NBZmZlY3RlZH0g6KGM5pWw5o2uYCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBtZXNzYWdlLmVycm9yKGDmn6Xor6LmiafooYzlpLHotKU6ICR7cmVzcG9uc2UuZGF0YS5lcnJvcj8ubWVzc2FnZX1gKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgbWVzc2FnZS5lcnJvcign5p+l6K+i5omn6KGM5aSx6LSlJyk7XG4gICAgICBzZXRFeGVjdXRpb25SZXN1bHQoe1xuICAgICAgICBpc0V4ZWN1dGlvblN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICBib2R5OiBbXSxcbiAgICAgICAgaGVhZGVyczoge30sXG4gICAgICAgIHN0YXR1c0NvZGU6ICc1MDAnLFxuICAgICAgICBleGVjdXRpb25UaW1lOiAwLFxuICAgICAgICByb3dzQWZmZWN0ZWQ6IDAsXG4gICAgICAgIGVycm9yOiB7XG4gICAgICAgICAgbWVzc2FnZTogJ+afpeivouaJp+ihjOWksei0pScsXG4gICAgICAgICAgY29kZTogJ0VYRUNVVElPTl9FUlJPUidcbiAgICAgICAgfVxuICAgICAgfSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldEV4ZWN1dGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOS/neWtmOafpeivolxuICBjb25zdCBzYXZlUXVlcnkgPSBhc3luYyAodmFsdWVzOiBhbnkpID0+IHtcbiAgICB0cnkge1xuICAgICAgYXdhaXQgYXBpQ2xpZW50LnBvc3QoJy9xdWVyaWVzJywge1xuICAgICAgICBuYW1lOiB2YWx1ZXMubmFtZSxcbiAgICAgICAgZGF0YXNvdXJjZUlkOiBzZWxlY3RlZERhdGFzb3VyY2UsXG4gICAgICAgIHF1ZXJ5OiBxdWVyeVRleHQsXG4gICAgICAgIHBhcmFtZXRlcnM6IFtdLFxuICAgICAgICB0YWdzOiB2YWx1ZXMudGFncyA/IHZhbHVlcy50YWdzLnNwbGl0KCcsJykubWFwKCh0YWc6IHN0cmluZykgPT4gdGFnLnRyaW0oKSkgOiBbXVxuICAgICAgfSk7XG5cbiAgICAgIG1lc3NhZ2Uuc3VjY2Vzcygn5p+l6K+i5L+d5a2Y5oiQ5YqfJyk7XG4gICAgICBzZXRTYXZlTW9kYWxWaXNpYmxlKGZhbHNlKTtcbiAgICAgIHNhdmVGb3JtLnJlc2V0RmllbGRzKCk7XG4gICAgICBsb2FkU2F2ZWRRdWVyaWVzKCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIG1lc3NhZ2UuZXJyb3IoJ+S/neWtmOafpeivouWksei0pScpO1xuICAgIH1cbiAgfTtcblxuICAvLyDliqDovb3mn6Xor6JcbiAgY29uc3QgbG9hZFF1ZXJ5ID0gKHF1ZXJ5OiBRdWVyeSkgPT4ge1xuICAgIHNldFF1ZXJ5VGV4dChxdWVyeS5xdWVyeSk7XG4gICAgbWVzc2FnZS5zdWNjZXNzKGDlt7LliqDovb3mn6Xor6I6ICR7cXVlcnkubmFtZX1gKTtcbiAgfTtcblxuICAvLyDlr7zlh7rnu5PmnpxcbiAgY29uc3QgZXhwb3J0UmVzdWx0cyA9ICgpID0+IHtcbiAgICBpZiAoIWV4ZWN1dGlvblJlc3VsdD8uYm9keS5sZW5ndGgpIHtcbiAgICAgIG1lc3NhZ2UuZXJyb3IoJ+ayoeacieWPr+WvvOWHuueahOaVsOaNricpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGNvbnN0IGNzdiA9IGNvbnZlcnRUb0NTVihleGVjdXRpb25SZXN1bHQuYm9keSk7XG4gICAgY29uc3QgYmxvYiA9IG5ldyBCbG9iKFtjc3ZdLCB7IHR5cGU6ICd0ZXh0L2NzdjtjaGFyc2V0PXV0Zi04OycgfSk7XG4gICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKTtcbiAgICBjb25zdCB1cmwgPSBVUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpO1xuICAgIGxpbmsuc2V0QXR0cmlidXRlKCdocmVmJywgdXJsKTtcbiAgICBsaW5rLnNldEF0dHJpYnV0ZSgnZG93bmxvYWQnLCBgcXVlcnlfcmVzdWx0XyR7RGF0ZS5ub3coKX0uY3N2YCk7XG4gICAgbGluay5zdHlsZS52aXNpYmlsaXR5ID0gJ2hpZGRlbic7XG4gICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChsaW5rKTtcbiAgICBsaW5rLmNsaWNrKCk7XG4gICAgZG9jdW1lbnQuYm9keS5yZW1vdmVDaGlsZChsaW5rKTtcbiAgfTtcblxuICAvLyDovazmjaLkuLpDU1bmoLzlvI9cbiAgY29uc3QgY29udmVydFRvQ1NWID0gKGRhdGE6IGFueVtdKSA9PiB7XG4gICAgaWYgKCFkYXRhLmxlbmd0aCkgcmV0dXJuICcnO1xuXG4gICAgY29uc3QgaGVhZGVycyA9IE9iamVjdC5rZXlzKGRhdGFbMF0pO1xuICAgIGNvbnN0IGNzdkhlYWRlcnMgPSBoZWFkZXJzLmpvaW4oJywnKTtcbiAgICBjb25zdCBjc3ZSb3dzID0gZGF0YS5tYXAocm93ID0+XG4gICAgICBoZWFkZXJzLm1hcChoZWFkZXIgPT4ge1xuICAgICAgICBjb25zdCB2YWx1ZSA9IHJvd1toZWFkZXJdO1xuICAgICAgICByZXR1cm4gdHlwZW9mIHZhbHVlID09PSAnc3RyaW5nJyAmJiB2YWx1ZS5pbmNsdWRlcygnLCcpXG4gICAgICAgICAgPyBgXCIke3ZhbHVlfVwiYFxuICAgICAgICAgIDogdmFsdWU7XG4gICAgICB9KS5qb2luKCcsJylcbiAgICApO1xuXG4gICAgcmV0dXJuIFtjc3ZIZWFkZXJzLCAuLi5jc3ZSb3dzXS5qb2luKCdcXG4nKTtcbiAgfTtcblxuICAvLyDmnoTlu7rnu5PmnpzooajmoLzliJdcbiAgY29uc3QgYnVpbGRSZXN1bHRDb2x1bW5zID0gKCkgPT4ge1xuICAgIGlmICghZXhlY3V0aW9uUmVzdWx0Py5ib2R5Lmxlbmd0aCkgcmV0dXJuIFtdO1xuXG4gICAgY29uc3QgZmlyc3RSb3cgPSBleGVjdXRpb25SZXN1bHQuYm9keVswXTtcbiAgICByZXR1cm4gT2JqZWN0LmtleXMoZmlyc3RSb3cpLm1hcChrZXkgPT4gKHtcbiAgICAgIHRpdGxlOiBrZXksXG4gICAgICBkYXRhSW5kZXg6IGtleSxcbiAgICAgIGtleSxcbiAgICAgIHdpZHRoOiAxNTAsXG4gICAgICByZW5kZXI6ICh2YWx1ZTogYW55KSA9PiB7XG4gICAgICAgIGlmICh2YWx1ZSA9PT0gbnVsbCkgcmV0dXJuIDxUZXh0IHR5cGU9XCJzZWNvbmRhcnlcIj5OVUxMPC9UZXh0PjtcbiAgICAgICAgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gJ29iamVjdCcpIHJldHVybiBKU09OLnN0cmluZ2lmeSh2YWx1ZSk7XG4gICAgICAgIHJldHVybiBTdHJpbmcodmFsdWUpO1xuICAgICAgfVxuICAgIH0pKTtcbiAgfTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGxvYWREYXRhc291cmNlcygpO1xuICB9LCBbXSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoc2VsZWN0ZWREYXRhc291cmNlKSB7XG4gICAgICBsb2FkU2F2ZWRRdWVyaWVzKCk7XG4gICAgfVxuICB9LCBbc2VsZWN0ZWREYXRhc291cmNlXSk7XG5cbiAgLy8g56S65L6L5p+l6K+i5qih5p2/XG4gIGNvbnN0IHF1ZXJ5VGVtcGxhdGVzID0gW1xuICAgIHtcbiAgICAgIG5hbWU6ICfmn6Xor6LmiYDmnInorrDlvZUnLFxuICAgICAgcXVlcnk6ICdTRUxFQ1QgKiBGUk9NIHRhYmxlX25hbWUgTElNSVQgMTA7J1xuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogJ+e7n+iuoeiusOW9leaVsCcsXG4gICAgICBxdWVyeTogJ1NFTEVDVCBDT1VOVCgqKSBhcyB0b3RhbCBGUk9NIHRhYmxlX25hbWU7J1xuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogJ+aMieadoeS7tuafpeivoicsXG4gICAgICBxdWVyeTogJ1NFTEVDVCAqIEZST00gdGFibGVfbmFtZSBXSEVSRSBjb2x1bW5fbmFtZSA9IFxcJ3ZhbHVlXFwnIExJTUlUIDEwOydcbiAgICB9XG4gIF07XG5cbiAgcmV0dXJuIChcbiAgICA8UXVlcnlFZGl0b3JDb250YWluZXI+XG4gICAgICB7Lyog57yW6L6R5Zmo5aS06YOoICovfVxuICAgICAgPEVkaXRvckhlYWRlcj5cbiAgICAgICAgPFNwYWNlIHNwbGl0PXs8RGl2aWRlciB0eXBlPVwidmVydGljYWxcIiAvPn0+XG4gICAgICAgICAgPFNwYWNlPlxuICAgICAgICAgICAgPFRleHQgc3Ryb25nPuaVsOaNrua6kDo8L1RleHQ+XG4gICAgICAgICAgICA8U2VsZWN0XG4gICAgICAgICAgICAgIHN0eWxlPXt7IHdpZHRoOiAyMDAgfX1cbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLpgInmi6nmlbDmja7mupBcIlxuICAgICAgICAgICAgICB2YWx1ZT17c2VsZWN0ZWREYXRhc291cmNlfVxuICAgICAgICAgICAgICBvbkNoYW5nZT17c2V0U2VsZWN0ZWREYXRhc291cmNlfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7ZGF0YXNvdXJjZXMubWFwKGRzID0+IChcbiAgICAgICAgICAgICAgICA8T3B0aW9uIGtleT17ZHMuaWR9IHZhbHVlPXtkcy5pZH0+XG4gICAgICAgICAgICAgICAgICA8U3BhY2U+XG4gICAgICAgICAgICAgICAgICAgIDxEYXRhYmFzZU91dGxpbmVkIC8+XG4gICAgICAgICAgICAgICAgICAgIHtkcy5uYW1lfVxuICAgICAgICAgICAgICAgICAgPC9TcGFjZT5cbiAgICAgICAgICAgICAgICA8L09wdGlvbj5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICA8L1NwYWNlPlxuXG4gICAgICAgICAgPFNwYWNlPlxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICB0eXBlPVwicHJpbWFyeVwiXG4gICAgICAgICAgICAgIGljb249ezxQbGF5Q2lyY2xlT3V0bGluZWQgLz59XG4gICAgICAgICAgICAgIGxvYWRpbmc9e2V4ZWN1dGluZ31cbiAgICAgICAgICAgICAgb25DbGljaz17ZXhlY3V0ZVF1ZXJ5fVxuICAgICAgICAgICAgICBkaXNhYmxlZD17IXNlbGVjdGVkRGF0YXNvdXJjZX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAg5omn6KGM5p+l6K+iXG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgaWNvbj17PFNhdmVPdXRsaW5lZCAvPn1cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2F2ZU1vZGFsVmlzaWJsZSh0cnVlKX1cbiAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFxdWVyeVRleHQudHJpbSgpfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICDkv53lrZjmn6Xor6JcbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICBpY29uPXs8RG93bmxvYWRPdXRsaW5lZCAvPn1cbiAgICAgICAgICAgICAgb25DbGljaz17ZXhwb3J0UmVzdWx0c31cbiAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFleGVjdXRpb25SZXN1bHQ/LmJvZHkubGVuZ3RofVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICDlr7zlh7rnu5PmnpxcbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDwvU3BhY2U+XG4gICAgICAgIDwvU3BhY2U+XG4gICAgICA8L0VkaXRvckhlYWRlcj5cblxuICAgICAgey8qIOe8lui+keWZqOS4u+S9kyAqL31cbiAgICAgIDxFZGl0b3JMYXlvdXQ+XG4gICAgICAgIHsvKiDkvqfovrnmoI8gKi99XG4gICAgICAgIDxFZGl0b3JTaWRlciAkaXNEYXJrPXtpc0Rhcmt9IHdpZHRoPXszMDB9IGNvbGxhcHNpYmxlPlxuICAgICAgICAgIDxUYWJzIGRlZmF1bHRBY3RpdmVLZXk9XCJzYXZlZFwiIHNpemU9XCJzbWFsbFwiPlxuICAgICAgICAgICAgPFRhYlBhbmUgdGFiPVwi5L+d5a2Y55qE5p+l6K+iXCIga2V5PVwic2F2ZWRcIj5cbiAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBwYWRkaW5nOiAnOHB4JyB9fT5cbiAgICAgICAgICAgICAgICB7c2F2ZWRRdWVyaWVzLm1hcChxdWVyeSA9PiAoXG4gICAgICAgICAgICAgICAgICA8Q2FyZFxuICAgICAgICAgICAgICAgICAgICBrZXk9e3F1ZXJ5LmlkfVxuICAgICAgICAgICAgICAgICAgICBzaXplPVwic21hbGxcIlxuICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyBtYXJnaW5Cb3R0b206IDgsIGN1cnNvcjogJ3BvaW50ZXInIH19XG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGxvYWRRdWVyeShxdWVyeSl9XG4gICAgICAgICAgICAgICAgICAgIGhvdmVyYWJsZVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IG1hcmdpbkJvdHRvbTogNCB9fT5cbiAgICAgICAgICAgICAgICAgICAgICA8VGV4dCBzdHJvbmc+e3F1ZXJ5Lm5hbWV9PC9UZXh0PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBtYXJnaW5Cb3R0b206IDQgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgPFRleHQgdHlwZT1cInNlY29uZGFyeVwiIHN0eWxlPXt7IGZvbnRTaXplOiAnMTJweCcgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgICB7cXVlcnkucXVlcnkuc3Vic3RyaW5nKDAsIDUwKX0uLi5cbiAgICAgICAgICAgICAgICAgICAgICA8L1RleHQ+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIHtxdWVyeS50YWdzLm1hcCh0YWcgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgPFRhZyBrZXk9e3RhZ30gc2l6ZT1cInNtYWxsXCI+e3RhZ308L1RhZz5cbiAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9UYWJQYW5lPlxuICAgICAgICAgICAgPFRhYlBhbmUgdGFiPVwi5p+l6K+i5qih5p2/XCIga2V5PVwidGVtcGxhdGVzXCI+XG4gICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgcGFkZGluZzogJzhweCcgfX0+XG4gICAgICAgICAgICAgICAge3F1ZXJ5VGVtcGxhdGVzLm1hcCgodGVtcGxhdGUsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICA8Q2FyZFxuICAgICAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICAgICAgICBzaXplPVwic21hbGxcIlxuICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyBtYXJnaW5Cb3R0b206IDgsIGN1cnNvcjogJ3BvaW50ZXInIH19XG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFF1ZXJ5VGV4dCh0ZW1wbGF0ZS5xdWVyeSl9XG4gICAgICAgICAgICAgICAgICAgIGhvdmVyYWJsZVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IG1hcmdpbkJvdHRvbTogNCB9fT5cbiAgICAgICAgICAgICAgICAgICAgICA8VGV4dCBzdHJvbmc+e3RlbXBsYXRlLm5hbWV9PC9UZXh0PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPFRleHQgdHlwZT1cInNlY29uZGFyeVwiIHN0eWxlPXt7IGZvbnRTaXplOiAnMTJweCcgfX0+XG4gICAgICAgICAgICAgICAgICAgICAge3RlbXBsYXRlLnF1ZXJ5fVxuICAgICAgICAgICAgICAgICAgICA8L1RleHQ+XG4gICAgICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9UYWJQYW5lPlxuICAgICAgICAgIDwvVGFicz5cbiAgICAgICAgPC9FZGl0b3JTaWRlcj5cblxuICAgICAgICB7Lyog5Li757yW6L6R5Yy65Z+fICovfVxuICAgICAgICA8RWRpdG9yQ29udGVudD5cbiAgICAgICAgICB7Lyog5p+l6K+i6L6T5YWl5Yy65Z+fICovfVxuICAgICAgICAgIDxRdWVyeUlucHV0QXJlYT5cbiAgICAgICAgICAgIDxRdWVyeVRleHRBcmVhXG4gICAgICAgICAgICAgIHZhbHVlPXtxdWVyeVRleHR9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0UXVlcnlUZXh0KGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLor7fovpPlhaVTUUzmn6Xor6Lor63lj6UuLi5cIlxuICAgICAgICAgICAgICByb3dzPXs4fVxuICAgICAgICAgICAgICBzdHlsZT17eyByZXNpemU6ICd2ZXJ0aWNhbCcgfX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9RdWVyeUlucHV0QXJlYT5cblxuICAgICAgICAgIHsvKiDmiafooYzkv6Hmga8gKi99XG4gICAgICAgICAge2V4ZWN1dGlvblJlc3VsdCAmJiAoXG4gICAgICAgICAgICA8RXhlY3V0aW9uSW5mbyAkaXNEYXJrPXtpc0Rhcmt9PlxuICAgICAgICAgICAgICA8U3BhY2Ugc3BsaXQ9ezxEaXZpZGVyIHR5cGU9XCJ2ZXJ0aWNhbFwiIC8+fT5cbiAgICAgICAgICAgICAgICA8c3Bhbj5cbiAgICAgICAgICAgICAgICAgIOeKtuaAgToge2V4ZWN1dGlvblJlc3VsdC5pc0V4ZWN1dGlvblN1Y2Nlc3MgPyAn5oiQ5YqfJyA6ICflpLHotKUnfVxuICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8c3Bhbj7miafooYzml7bpl7Q6IHtleGVjdXRpb25SZXN1bHQuZXhlY3V0aW9uVGltZX1tczwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8c3Bhbj7lvbHlk43ooYzmlbA6IHtleGVjdXRpb25SZXN1bHQucm93c0FmZmVjdGVkfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICB7ZXhlY3V0aW9uUmVzdWx0LmVycm9yICYmIChcbiAgICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPXt7IGNvbG9yOiAnI2ZmNGQ0ZicgfX0+XG4gICAgICAgICAgICAgICAgICAgIOmUmeivrzoge2V4ZWN1dGlvblJlc3VsdC5lcnJvci5tZXNzYWdlfVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvU3BhY2U+XG4gICAgICAgICAgICA8L0V4ZWN1dGlvbkluZm8+XG4gICAgICAgICAgKX1cblxuICAgICAgICAgIHsvKiDnu5PmnpzlsZXnpLrljLrln58gKi99XG4gICAgICAgICAgPFJlc3VsdEFyZWE+XG4gICAgICAgICAgICB7ZXhlY3V0aW5nID8gKFxuICAgICAgICAgICAgICA8TG9hZGluZ1NwaW5uZXIgdGV4dD1cIuato+WcqOaJp+ihjOafpeivoi4uLlwiIC8+XG4gICAgICAgICAgICApIDogZXhlY3V0aW9uUmVzdWx0ID8gKFxuICAgICAgICAgICAgICBleGVjdXRpb25SZXN1bHQuaXNFeGVjdXRpb25TdWNjZXNzID8gKFxuICAgICAgICAgICAgICAgIDxUYWJsZVxuICAgICAgICAgICAgICAgICAgY29sdW1ucz17YnVpbGRSZXN1bHRDb2x1bW5zKCl9XG4gICAgICAgICAgICAgICAgICBkYXRhU291cmNlPXtleGVjdXRpb25SZXN1bHQuYm9keX1cbiAgICAgICAgICAgICAgICAgIHJvd0tleT17KHJlY29yZCwgaW5kZXgpID0+IGluZGV4Py50b1N0cmluZygpIHx8ICcwJ31cbiAgICAgICAgICAgICAgICAgIHNjcm9sbD17eyB4OiB0cnVlLCB5OiA0MDAgfX1cbiAgICAgICAgICAgICAgICAgIHBhZ2luYXRpb249e3tcbiAgICAgICAgICAgICAgICAgICAgc2hvd1NpemVDaGFuZ2VyOiB0cnVlLFxuICAgICAgICAgICAgICAgICAgICBzaG93UXVpY2tKdW1wZXI6IHRydWUsXG4gICAgICAgICAgICAgICAgICAgIHNob3dUb3RhbDogKHRvdGFsKSA9PiBg5YWxICR7dG90YWx9IOadoeiusOW9lWBcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICBzaXplPVwic21hbGxcIlxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IHRleHRBbGlnbjogJ2NlbnRlcicsIHBhZGRpbmc6ICc0MHB4IDAnIH19PlxuICAgICAgICAgICAgICAgICAgICA8VGV4dCB0eXBlPVwiZGFuZ2VyXCIgc3R5bGU9e3sgZm9udFNpemU6ICcxNnB4JyB9fT5cbiAgICAgICAgICAgICAgICAgICAgICDmn6Xor6LmiafooYzlpLHotKVcbiAgICAgICAgICAgICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IG1hcmdpblRvcDogJzE2cHgnIH19PlxuICAgICAgICAgICAgICAgICAgICAgIDxUZXh0IHR5cGU9XCJzZWNvbmRhcnlcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtleGVjdXRpb25SZXN1bHQuZXJyb3I/Lm1lc3NhZ2V9XG4gICAgICAgICAgICAgICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyB0ZXh0QWxpZ246ICdjZW50ZXInLCBwYWRkaW5nOiAnNDBweCAwJyB9fT5cbiAgICAgICAgICAgICAgICAgIDxDb2RlT3V0bGluZWQgc3R5bGU9e3sgZm9udFNpemU6ICc0OHB4JywgY29sb3I6ICcjZDlkOWQ5JywgbWFyZ2luQm90dG9tOiAnMTZweCcgfX0gLz5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxUZXh0IHR5cGU9XCJzZWNvbmRhcnlcIj7or7fovpPlhaXmn6Xor6Lor63lj6Xlubbngrnlh7vmiafooYw8L1RleHQ+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L1Jlc3VsdEFyZWE+XG4gICAgICAgIDwvRWRpdG9yQ29udGVudD5cbiAgICAgIDwvRWRpdG9yTGF5b3V0PlxuXG4gICAgICB7Lyog5L+d5a2Y5p+l6K+i5a+56K+d5qGGICovfVxuICAgICAgPE1vZGFsXG4gICAgICAgIHRpdGxlPVwi5L+d5a2Y5p+l6K+iXCJcbiAgICAgICAgb3Blbj17c2F2ZU1vZGFsVmlzaWJsZX1cbiAgICAgICAgb25DYW5jZWw9eygpID0+IHNldFNhdmVNb2RhbFZpc2libGUoZmFsc2UpfVxuICAgICAgICBvbk9rPXsoKSA9PiBzYXZlRm9ybS5zdWJtaXQoKX1cbiAgICAgICAgb2tUZXh0PVwi5L+d5a2YXCJcbiAgICAgICAgY2FuY2VsVGV4dD1cIuWPlua2iFwiXG4gICAgICA+XG4gICAgICAgIDxGb3JtIGZvcm09e3NhdmVGb3JtfSBvbkZpbmlzaD17c2F2ZVF1ZXJ5fSBsYXlvdXQ9XCJ2ZXJ0aWNhbFwiPlxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIGxhYmVsPVwi5p+l6K+i5ZCN56ewXCJcbiAgICAgICAgICAgIG5hbWU9XCJuYW1lXCJcbiAgICAgICAgICAgIHJ1bGVzPXtbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeafpeivouWQjeensCcgfV19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPElucHV0IHBsYWNlaG9sZGVyPVwi6K+36L6T5YWl5p+l6K+i5ZCN56ewXCIgLz5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cbiAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICBsYWJlbD1cIuagh+etvlwiXG4gICAgICAgICAgICBuYW1lPVwidGFnc1wiXG4gICAgICAgICAgICBoZWxwPVwi5aSa5Liq5qCH562+55So6YCX5Y+35YiG6ZqUXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8SW5wdXQgcGxhY2Vob2xkZXI9XCLkvovlpoI6IHVzZXJzLCBzdGF0aXN0aWNzXCIgLz5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cbiAgICAgICAgPC9Gb3JtPlxuICAgICAgPC9Nb2RhbD5cbiAgICA8L1F1ZXJ5RWRpdG9yQ29udGFpbmVyPlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgUXVlcnlFZGl0b3I7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///3781\n\n}")}}]);