import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

import { Plugin } from '../../types/api';
import { apiClient } from '../../services/apiClient';

// 异步thunk actions
export const fetchPlugins = createAsyncThunk(
  'plugin/fetchPlugins',
  async (params: {
    category?: string;
    status?: string;
  } = {}) => {
    const response = await apiClient.get('/plugins', { params });
    return response.data;
  }
);

export const fetchPluginById = createAsyncThunk(
  'plugin/fetchPluginById',
  async (id: string) => {
    const response = await apiClient.get(`/plugins/${id}`);
    return response.data;
  }
);

// 状态接口
interface PluginState {
  // 插件列表
  plugins: Plugin[];
  
  // 当前选中的插件
  currentPlugin: Plugin | null;
  
  // 加载状态
  loading: {
    list: boolean;
    detail: boolean;
  };
  
  // 错误信息
  error: string | null;
  
  // 过滤条件
  filters: {
    category?: string;
    status?: string;
    searchText?: string;
  };
  
  // 统计信息
  stats: {
    total: number;
    byCategory: Record<string, number>;
    byStatus: Record<string, number>;
  };
}

// 初始状态
const initialState: PluginState = {
  plugins: [],
  currentPlugin: null,
  loading: {
    list: false,
    detail: false
  },
  error: null,
  filters: {},
  stats: {
    total: 0,
    byCategory: {},
    byStatus: {}
  }
};

// 计算统计信息
const calculateStats = (plugins: Plugin[]) => {
  const stats = {
    total: plugins.length,
    byCategory: {} as Record<string, number>,
    byStatus: {} as Record<string, number>
  };

  plugins.forEach(plugin => {
    // 按分类统计
    stats.byCategory[plugin.category] = (stats.byCategory[plugin.category] || 0) + 1;
    
    // 按状态统计
    stats.byStatus[plugin.status] = (stats.byStatus[plugin.status] || 0) + 1;
  });

  return stats;
};

// 创建slice
const pluginSlice = createSlice({
  name: 'plugin',
  initialState,
  reducers: {
    // 设置过滤条件
    setFilters: (state, action: PayloadAction<Partial<PluginState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    
    // 清除错误
    clearError: (state) => {
      state.error = null;
    },
    
    // 设置当前插件
    setCurrentPlugin: (state, action: PayloadAction<Plugin | null>) => {
      state.currentPlugin = action.payload;
    },
    
    // 重置过滤条件
    resetFilters: (state) => {
      state.filters = {};
    }
  },
  extraReducers: (builder) => {
    // 获取插件列表
    builder
      .addCase(fetchPlugins.pending, (state) => {
        state.loading.list = true;
        state.error = null;
      })
      .addCase(fetchPlugins.fulfilled, (state, action: PayloadAction<Plugin[]>) => {
        state.loading.list = false;
        state.plugins = action.payload;
        state.stats = calculateStats(action.payload);
      })
      .addCase(fetchPlugins.rejected, (state, action) => {
        state.loading.list = false;
        state.error = action.error.message || '获取插件列表失败';
      });

    // 获取插件详情
    builder
      .addCase(fetchPluginById.pending, (state) => {
        state.loading.detail = true;
        state.error = null;
      })
      .addCase(fetchPluginById.fulfilled, (state, action: PayloadAction<Plugin>) => {
        state.loading.detail = false;
        state.currentPlugin = action.payload;
        
        // 更新列表中的插件
        const index = state.plugins.findIndex(plugin => plugin.id === action.payload.id);
        if (index !== -1) {
          state.plugins[index] = action.payload;
        } else {
          state.plugins.push(action.payload);
        }
        
        // 重新计算统计信息
        state.stats = calculateStats(state.plugins);
      })
      .addCase(fetchPluginById.rejected, (state, action) => {
        state.loading.detail = false;
        state.error = action.error.message || '获取插件详情失败';
      });
  }
});

// 导出actions
export const {
  setFilters,
  clearError,
  setCurrentPlugin,
  resetFilters
} = pluginSlice.actions;

// 导出reducer
export default pluginSlice.reducer;

// 选择器
export const selectPlugins = (state: { plugin: PluginState }) => state.plugin.plugins;
export const selectCurrentPlugin = (state: { plugin: PluginState }) => state.plugin.currentPlugin;
export const selectPluginLoading = (state: { plugin: PluginState }) => state.plugin.loading;
export const selectPluginFilters = (state: { plugin: PluginState }) => state.plugin.filters;
export const selectPluginStats = (state: { plugin: PluginState }) => state.plugin.stats;
export const selectPluginError = (state: { plugin: PluginState }) => state.plugin.error;

// 过滤后的插件列表选择器
export const selectFilteredPlugins = (state: { plugin: PluginState }) => {
  const { plugins, filters } = state.plugin;
  
  return plugins.filter(plugin => {
    // 分类过滤
    if (filters.category && plugin.category !== filters.category) {
      return false;
    }
    
    // 状态过滤
    if (filters.status && plugin.status !== filters.status) {
      return false;
    }
    
    // 搜索文本过滤
    if (filters.searchText) {
      const searchText = filters.searchText.toLowerCase();
      return (
        plugin.name.toLowerCase().includes(searchText) ||
        plugin.displayName.toLowerCase().includes(searchText) ||
        plugin.description.toLowerCase().includes(searchText)
      );
    }
    
    return true;
  });
};

// 按分类获取插件选择器
export const selectPluginsByCategory = (category: string) => (state: { plugin: PluginState }) => {
  return state.plugin.plugins.filter(plugin => plugin.category === category);
};

// 按状态获取插件选择器
export const selectPluginsByStatus = (status: string) => (state: { plugin: PluginState }) => {
  return state.plugin.plugins.filter(plugin => plugin.status === status);
};

// 活跃插件选择器
export const selectActivePlugins = (state: { plugin: PluginState }) => {
  return state.plugin.plugins.filter(plugin => plugin.status === 'active');
};
