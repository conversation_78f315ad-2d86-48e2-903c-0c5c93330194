# Python 后端架构设计

## 1. 技术栈选择

### 1.1 核心框架
- **FastAPI 0.104+**: 现代、高性能的Web框架
- **Python 3.11+**: 最新稳定版本，性能优化
- **Pydantic 2.0+**: 数据验证和序列化
- **asyncio**: 原生异步编程支持

### 1.2 数据库和缓存
- **MongoDB**: 主数据库，使用Motor异步驱动
- **Redis**: 缓存和会话存储，使用aioredis
- **Beanie**: MongoDB ODM，基于Pydantic

### 1.3 HTTP客户端和工具
- **aiohttp**: 异步HTTP客户端
- **httpx**: 现代HTTP客户端库
- **uvicorn**: ASGI服务器
- **gunicorn**: 生产环境WSGI服务器

### 1.4 开发和测试工具
- **pytest**: 测试框架
- **pytest-asyncio**: 异步测试支持
- **black**: 代码格式化
- **mypy**: 类型检查
- **ruff**: 快速代码检查

## 2. 项目结构设计

### 2.1 目录结构
```
datasource-service/
├── app/
│   ├── __init__.py
│   ├── main.py                 # FastAPI应用入口
│   ├── config.py               # 配置管理
│   ├── dependencies.py         # 依赖注入
│   ├── api/                    # API路由
│   │   ├── __init__.py
│   │   ├── v1/
│   │   │   ├── __init__.py
│   │   │   ├── datasources.py  # 数据源API
│   │   │   ├── plugins.py      # 插件API
│   │   │   └── queries.py      # 查询API
│   │   └── deps.py             # API依赖
│   ├── core/                   # 核心模块
│   │   ├── __init__.py
│   │   ├── security.py         # 安全认证
│   │   ├── database.py         # 数据库连接
│   │   ├── cache.py            # 缓存管理
│   │   └── exceptions.py       # 异常定义
│   ├── models/                 # 数据模型
│   │   ├── __init__.py
│   │   ├── datasource.py       # 数据源模型
│   │   ├── plugin.py           # 插件模型
│   │   └── query.py            # 查询模型
│   ├── schemas/                # Pydantic模式
│   │   ├── __init__.py
│   │   ├── datasource.py       # 数据源模式
│   │   ├── plugin.py           # 插件模式
│   │   └── common.py           # 通用模式
│   ├── services/               # 业务服务
│   │   ├── __init__.py
│   │   ├── datasource_service.py
│   │   ├── plugin_service.py
│   │   └── query_service.py
│   ├── plugins/                # 插件系统
│   │   ├── __init__.py
│   │   ├── base.py             # 插件基类
│   │   ├── manager.py          # 插件管理器
│   │   ├── mysql/              # MySQL插件
│   │   ├── postgresql/         # PostgreSQL插件
│   │   ├── mongodb/            # MongoDB插件
│   │   └── rest_api/           # REST API插件
│   └── utils/                  # 工具函数
│       ├── __init__.py
│       ├── logger.py           # 日志工具
│       ├── validators.py       # 验证器
│       └── helpers.py          # 辅助函数
├── tests/                      # 测试代码
│   ├── __init__.py
│   ├── conftest.py             # 测试配置
│   ├── test_api/               # API测试
│   ├── test_services/          # 服务测试
│   └── test_plugins/           # 插件测试
├── migrations/                 # 数据迁移
├── scripts/                    # 脚本文件
├── docker/                     # Docker配置
├── requirements.txt            # 依赖管理
├── pyproject.toml             # 项目配置
└── README.md                  # 项目文档
```

### 2.2 FastAPI应用配置
```python
# app/main.py
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from app.api.v1 import datasources, plugins, queries
from app.core.config import settings
from app.core.database import init_database
from app.core.exceptions import AppException
from app.utils.logger import setup_logging

# 设置日志
setup_logging()

# 创建FastAPI应用
app = FastAPI(
    title="PagePlug Datasource Service",
    description="数据源管理微服务",
    version="1.0.0",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
)

# 中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS,
)

# 异常处理
@app.exception_handler(AppException)
async def app_exception_handler(request: Request, exc: AppException):
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "code": exc.status_code,
            "message": exc.message,
            "error": {
                "type": exc.error_type,
                "details": exc.details
            }
        }
    )

# 路由注册
app.include_router(datasources.router, prefix="/api/v1")
app.include_router(plugins.router, prefix="/api/v1")
app.include_router(queries.router, prefix="/api/v1")

# 启动事件
@app.on_event("startup")
async def startup_event():
    await init_database()

# 健康检查
@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "datasource-service"}

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info"
    )
```

## 3. 配置管理

### 3.1 配置类设计
```python
# app/config.py
from pydantic_settings import BaseSettings
from typing import List, Optional
import os

class Settings(BaseSettings):
    # 应用配置
    APP_NAME: str = "PagePlug Datasource Service"
    DEBUG: bool = False
    VERSION: str = "1.0.0"
    
    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    WORKERS: int = 1
    
    # 安全配置
    SECRET_KEY: str
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    ALLOWED_HOSTS: List[str] = ["*"]
    
    # 数据库配置
    MONGODB_URL: str
    MONGODB_DB_NAME: str = "datasource_service"
    MONGODB_MIN_CONNECTIONS: int = 10
    MONGODB_MAX_CONNECTIONS: int = 100
    
    # Redis配置
    REDIS_URL: str
    REDIS_DB: int = 0
    REDIS_MAX_CONNECTIONS: int = 20
    
    # 插件配置
    PLUGIN_DIR: str = "app/plugins"
    PLUGIN_TIMEOUT: int = 30
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"
    
    # 监控配置
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 9090
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# 全局配置实例
settings = Settings()
```

### 3.2 环境变量配置
```bash
# .env
SECRET_KEY=your-secret-key-here
MONGODB_URL=mongodb://localhost:27017
REDIS_URL=redis://localhost:6379
DEBUG=true
LOG_LEVEL=DEBUG
ALLOWED_HOSTS=["localhost", "127.0.0.1", "*.example.com"]
```

## 4. 数据模型设计

### 4.1 数据源模型
```python
# app/models/datasource.py
from beanie import Document, Indexed
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class AuthType(str, Enum):
    BASIC = "basic"
    OAUTH = "oauth"
    API_KEY = "apikey"
    BEARER = "bearer"

class DatasourceAuthentication(BaseModel):
    auth_type: AuthType
    username: Optional[str] = None
    password: Optional[str] = None
    bearer_token: Optional[str] = None
    api_key: Optional[str] = None
    header_prefix: Optional[str] = None
    add_to: Optional[str] = None
    is_authorized: bool = False
    scope_string: Optional[str] = None

class DatasourceConnection(BaseModel):
    ssl_enabled: bool = False
    ssl_mode: Optional[str] = None
    certificate_file: Optional[str] = None
    key_file: Optional[str] = None
    ca_certificate_file: Optional[str] = None

class DatasourceConfiguration(BaseModel):
    url: str
    database_name: Optional[str] = None
    authentication: Optional[DatasourceAuthentication] = None
    connection: Optional[DatasourceConnection] = None
    properties: Optional[List[Dict[str, Any]]] = []
    headers: Optional[List[Dict[str, Any]]] = []
    query_parameters: Optional[List[Dict[str, Any]]] = []

class DatasourceStructure(BaseModel):
    tables: Optional[List[Dict[str, Any]]] = []
    error: Optional[Dict[str, Any]] = None
    last_updated: Optional[datetime] = None

class Datasource(Document):
    name: Indexed(str)
    plugin_id: Indexed(str)
    workspace_id: Indexed(str)
    datasource_configuration: DatasourceConfiguration
    structure: Optional[DatasourceStructure] = None
    is_valid: bool = False
    is_configured: bool = False
    invalids: Optional[List[str]] = []
    messages: Optional[List[str]] = []
    toast_message: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    class Settings:
        name = "datasources"
        indexes = [
            [("workspace_id", 1), ("name", 1)],  # 复合索引
            [("plugin_id", 1)],
            [("is_valid", 1)],
        ]
```

### 4.2 插件模型
```python
# app/models/plugin.py
from beanie import Document, Indexed
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class PluginType(str, Enum):
    DATABASE = "database"
    API = "api"
    CLOUD = "cloud"
    AI = "ai"
    OTHER = "other"

class PluginStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"

class PluginMetadata(BaseModel):
    display_name: str
    description: str
    version: str
    provider: str
    license: str
    icon_url: Optional[str] = None
    documentation_url: Optional[str] = None
    supported_operations: List[str] = []
    templates: Optional[Dict[str, str]] = {}

class Plugin(Document):
    plugin_id: Indexed(str, unique=True)
    name: str
    plugin_type: PluginType
    status: PluginStatus = PluginStatus.ACTIVE
    metadata: PluginMetadata
    form_config: Optional[List[Dict[str, Any]]] = []
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    class Settings:
        name = "plugins"
        indexes = [
            [("plugin_type", 1)],
            [("status", 1)],
        ]
```

## 5. 业务服务层

### 5.1 数据源服务
```python
# app/services/datasource_service.py
from typing import List, Optional
from app.models.datasource import Datasource, DatasourceConfiguration
from app.schemas.datasource import DatasourceCreate, DatasourceUpdate
from app.services.plugin_service import PluginService
from app.core.exceptions import DatasourceNotFound, DatasourceConnectionFailed
from app.core.cache import cache_manager
import logging

logger = logging.getLogger(__name__)

class DatasourceService:
    def __init__(self, plugin_service: PluginService):
        self.plugin_service = plugin_service
    
    async def create(self, datasource_data: DatasourceCreate) -> Datasource:
        """创建数据源"""
        # 验证插件存在
        plugin = await self.plugin_service.get_by_id(datasource_data.plugin_id)
        if not plugin:
            raise ValueError(f"Plugin {datasource_data.plugin_id} not found")
        
        # 创建数据源实例
        datasource = Datasource(**datasource_data.dict())
        
        # 验证配置
        await self._validate_configuration(datasource)
        
        # 保存到数据库
        await datasource.save()
        
        # 异步测试连接
        await self._test_connection_async(datasource)
        
        logger.info(f"Created datasource: {datasource.name}")
        return datasource
    
    async def get_by_id(self, datasource_id: str) -> Datasource:
        """根据ID获取数据源"""
        datasource = await Datasource.get(datasource_id)
        if not datasource:
            raise DatasourceNotFound(f"Datasource {datasource_id} not found")
        return datasource
    
    async def list_by_workspace(self, workspace_id: str, 
                               plugin_id: Optional[str] = None) -> List[Datasource]:
        """获取工作空间的数据源列表"""
        query = {"workspace_id": workspace_id}
        if plugin_id:
            query["plugin_id"] = plugin_id
        
        return await Datasource.find(query).to_list()
    
    async def update(self, datasource_id: str, 
                    update_data: DatasourceUpdate) -> Datasource:
        """更新数据源"""
        datasource = await self.get_by_id(datasource_id)
        
        # 更新字段
        update_dict = update_data.dict(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(datasource, field, value)
        
        # 如果配置发生变化，重新验证
        if 'datasource_configuration' in update_dict:
            await self._validate_configuration(datasource)
            # 清除结构缓存
            await cache_manager.delete(f"structure:{datasource_id}")
        
        datasource.updated_at = datetime.utcnow()
        await datasource.save()
        
        logger.info(f"Updated datasource: {datasource.name}")
        return datasource
    
    async def delete(self, datasource_id: str) -> bool:
        """删除数据源"""
        datasource = await self.get_by_id(datasource_id)
        await datasource.delete()
        
        # 清除相关缓存
        await cache_manager.delete(f"structure:{datasource_id}")
        await cache_manager.delete(f"test_result:{datasource_id}")
        
        logger.info(f"Deleted datasource: {datasource.name}")
        return True
    
    async def test_connection(self, datasource_id: str) -> Dict[str, Any]:
        """测试数据源连接"""
        # 先检查缓存
        cache_key = f"test_result:{datasource_id}"
        cached_result = await cache_manager.get(cache_key)
        if cached_result:
            return cached_result
        
        datasource = await self.get_by_id(datasource_id)
        plugin_executor = await self.plugin_service.get_executor(datasource.plugin_id)
        
        try:
            result = await plugin_executor.test_connection(
                datasource.datasource_configuration.dict()
            )
            
            # 更新数据源状态
            datasource.is_valid = result.get('success', False)
            await datasource.save()
            
            # 缓存结果（5分钟）
            await cache_manager.set(cache_key, result, expire=300)
            
            return result
            
        except Exception as e:
            logger.error(f"Connection test failed for {datasource_id}: {str(e)}")
            raise DatasourceConnectionFailed(str(e))
    
    async def get_structure(self, datasource_id: str) -> Dict[str, Any]:
        """获取数据源结构"""
        # 检查缓存
        cache_key = f"structure:{datasource_id}"
        cached_structure = await cache_manager.get(cache_key)
        if cached_structure:
            return cached_structure
        
        datasource = await self.get_by_id(datasource_id)
        plugin_executor = await self.plugin_service.get_executor(datasource.plugin_id)
        
        try:
            # 创建连接
            connection = await plugin_executor.create_connection(
                datasource.datasource_configuration.dict()
            )
            
            # 获取结构
            structure = await plugin_executor.get_structure(
                connection, datasource.datasource_configuration.dict()
            )
            
            # 销毁连接
            await plugin_executor.destroy_connection(connection)
            
            # 更新数据源结构
            datasource.structure = DatasourceStructure(**structure)
            await datasource.save()
            
            # 缓存结构（1小时）
            await cache_manager.set(cache_key, structure, expire=3600)
            
            return structure
            
        except Exception as e:
            logger.error(f"Failed to get structure for {datasource_id}: {str(e)}")
            raise
    
    async def _validate_configuration(self, datasource: Datasource) -> None:
        """验证数据源配置"""
        plugin_executor = await self.plugin_service.get_executor(datasource.plugin_id)
        
        errors = await plugin_executor.validate_configuration(
            datasource.datasource_configuration.dict()
        )
        
        if errors:
            datasource.invalids = list(errors)
            datasource.is_configured = False
        else:
            datasource.invalids = []
            datasource.is_configured = True
    
    async def _test_connection_async(self, datasource: Datasource) -> None:
        """异步测试连接"""
        try:
            await self.test_connection(datasource.id)
        except Exception as e:
            logger.warning(f"Async connection test failed: {str(e)}")
```

## 6. 插件系统设计

### 6.1 插件基类
```python
# app/plugins/base.py
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Set
import asyncio

class PluginExecutor(ABC):
    """插件执行器基类"""
    
    @abstractmethod
    async def create_connection(self, config: Dict[str, Any]) -> Any:
        """创建数据源连接"""
        pass
    
    @abstractmethod
    async def destroy_connection(self, connection: Any) -> None:
        """销毁数据源连接"""
        pass
    
    @abstractmethod
    async def execute_query(self, connection: Any, query: str, 
                           params: Optional[List] = None) -> Dict[str, Any]:
        """执行查询"""
        pass
    
    async def test_connection(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """测试连接"""
        try:
            connection = await self.create_connection(config)
            await self.destroy_connection(connection)
            return {
                "success": True,
                "message": "Connection successful",
                "response_time": 0
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"Connection failed: {str(e)}",
                "error": str(e)
            }
    
    async def get_structure(self, connection: Any, 
                           config: Dict[str, Any]) -> Dict[str, Any]:
        """获取数据源结构"""
        return {"tables": []}
    
    async def validate_configuration(self, config: Dict[str, Any]) -> Set[str]:
        """验证配置"""
        errors = set()
        
        # 基本验证
        if not config.get('url'):
            errors.add("URL is required")
        
        return errors
    
    def get_supported_operations(self) -> List[str]:
        """获取支持的操作类型"""
        return ["READ", "WRITE"]
    
    def get_query_templates(self) -> Dict[str, str]:
        """获取查询模板"""
        return {}
```

### 6.2 插件管理器
```python
# app/plugins/manager.py
from typing import Dict, Type, Optional
import importlib
import logging
from app.plugins.base import PluginExecutor
from app.core.exceptions import PluginNotFound, PluginLoadError

logger = logging.getLogger(__name__)

class PluginManager:
    """插件管理器"""
    
    def __init__(self):
        self._executors: Dict[str, PluginExecutor] = {}
        self._plugin_classes: Dict[str, Type[PluginExecutor]] = {}
    
    async def load_plugin(self, plugin_id: str) -> PluginExecutor:
        """加载插件"""
        if plugin_id in self._executors:
            return self._executors[plugin_id]
        
        try:
            # 动态导入插件模块
            module_name = f"app.plugins.{plugin_id}.executor"
            module = importlib.import_module(module_name)
            
            # 获取执行器类
            executor_class = getattr(module, "Executor")
            
            # 创建执行器实例
            executor = executor_class()
            
            # 缓存执行器
            self._executors[plugin_id] = executor
            self._plugin_classes[plugin_id] = executor_class
            
            logger.info(f"Loaded plugin: {plugin_id}")
            return executor
            
        except ImportError as e:
            logger.error(f"Failed to import plugin {plugin_id}: {str(e)}")
            raise PluginLoadError(f"Plugin {plugin_id} not found")
        except AttributeError as e:
            logger.error(f"Plugin {plugin_id} missing Executor class: {str(e)}")
            raise PluginLoadError(f"Invalid plugin {plugin_id}")
        except Exception as e:
            logger.error(f"Failed to load plugin {plugin_id}: {str(e)}")
            raise PluginLoadError(f"Plugin load error: {str(e)}")
    
    async def get_executor(self, plugin_id: str) -> PluginExecutor:
        """获取插件执行器"""
        if plugin_id not in self._executors:
            await self.load_plugin(plugin_id)
        
        executor = self._executors.get(plugin_id)
        if not executor:
            raise PluginNotFound(f"Plugin {plugin_id} not found")
        
        return executor
    
    def unload_plugin(self, plugin_id: str) -> bool:
        """卸载插件"""
        if plugin_id in self._executors:
            del self._executors[plugin_id]
            del self._plugin_classes[plugin_id]
            logger.info(f"Unloaded plugin: {plugin_id}")
            return True
        return False
    
    def list_loaded_plugins(self) -> List[str]:
        """列出已加载的插件"""
        return list(self._executors.keys())
    
    async def reload_plugin(self, plugin_id: str) -> PluginExecutor:
        """重新加载插件"""
        self.unload_plugin(plugin_id)
        return await self.load_plugin(plugin_id)

# 全局插件管理器实例
plugin_manager = PluginManager()
```

## 7. 缓存和性能优化

### 7.1 缓存管理
```python
# app/core/cache.py
import aioredis
import json
import pickle
from typing import Any, Optional, Union
from app.core.config import settings

class CacheManager:
    def __init__(self):
        self.redis: Optional[aioredis.Redis] = None
    
    async def init(self):
        """初始化Redis连接"""
        self.redis = aioredis.from_url(
            settings.REDIS_URL,
            db=settings.REDIS_DB,
            max_connections=settings.REDIS_MAX_CONNECTIONS,
            decode_responses=True
        )
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if not self.redis:
            return None
        
        try:
            value = await self.redis.get(key)
            if value:
                return json.loads(value)
        except Exception:
            # 如果JSON解析失败，尝试pickle
            try:
                raw_value = await self.redis.get(key)
                if raw_value:
                    return pickle.loads(raw_value)
            except Exception:
                pass
        
        return None
    
    async def set(self, key: str, value: Any, expire: int = 3600) -> bool:
        """设置缓存值"""
        if not self.redis:
            return False
        
        try:
            # 尝试JSON序列化
            serialized_value = json.dumps(value, default=str)
            await self.redis.setex(key, expire, serialized_value)
            return True
        except (TypeError, ValueError):
            # JSON序列化失败，使用pickle
            try:
                serialized_value = pickle.dumps(value)
                await self.redis.setex(key, expire, serialized_value)
                return True
            except Exception:
                return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        if not self.redis:
            return False
        
        result = await self.redis.delete(key)
        return result > 0
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        if not self.redis:
            return False
        
        return await self.redis.exists(key) > 0
    
    async def clear_pattern(self, pattern: str) -> int:
        """清除匹配模式的键"""
        if not self.redis:
            return 0
        
        keys = await self.redis.keys(pattern)
        if keys:
            return await self.redis.delete(*keys)
        return 0

# 全局缓存管理器实例
cache_manager = CacheManager()
```

### 7.2 连接池管理
```python
# app/core/connection_pool.py
import asyncio
from typing import Dict, Any, Optional
from contextlib import asynccontextmanager
import logging

logger = logging.getLogger(__name__)

class ConnectionPool:
    """连接池管理器"""
    
    def __init__(self, max_size: int = 20):
        self.max_size = max_size
        self.pools: Dict[str, Any] = {}
        self.locks: Dict[str, asyncio.Lock] = {}
    
    async def get_connection(self, datasource_id: str, 
                           plugin_executor, config: Dict[str, Any]):
        """获取连接"""
        if datasource_id not in self.locks:
            self.locks[datasource_id] = asyncio.Lock()
        
        async with self.locks[datasource_id]:
            if datasource_id not in self.pools:
                # 创建新的连接池
                self.pools[datasource_id] = await self._create_pool(
                    plugin_executor, config
                )
            
            return await self._get_connection_from_pool(datasource_id)
    
    async def _create_pool(self, plugin_executor, config: Dict[str, Any]):
        """创建连接池"""
        # 这里可以根据不同的插件类型创建不同的连接池
        # 目前简化为单个连接
        return await plugin_executor.create_connection(config)
    
    async def _get_connection_from_pool(self, datasource_id: str):
        """从连接池获取连接"""
        return self.pools.get(datasource_id)
    
    async def release_connection(self, datasource_id: str, connection):
        """释放连接"""
        # 简化实现，实际应该将连接放回池中
        pass
    
    async def close_pool(self, datasource_id: str):
        """关闭连接池"""
        if datasource_id in self.pools:
            connection = self.pools.pop(datasource_id)
            # 这里应该调用插件的destroy_connection方法
            # 但需要知道对应的plugin_executor
            pass
    
    @asynccontextmanager
    async def get_connection_context(self, datasource_id: str, 
                                   plugin_executor, config: Dict[str, Any]):
        """连接上下文管理器"""
        connection = await self.get_connection(datasource_id, plugin_executor, config)
        try:
            yield connection
        finally:
            await self.release_connection(datasource_id, connection)

# 全局连接池实例
connection_pool = ConnectionPool()
```
