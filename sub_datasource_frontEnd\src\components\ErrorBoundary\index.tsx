import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Result, Button } from 'antd';
import styled from 'styled-components';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

const ErrorContainer = styled.div`
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
`;

const ErrorDetails = styled.details`
  margin-top: 16px;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  
  summary {
    cursor: pointer;
    font-weight: 500;
    margin-bottom: 8px;
  }
  
  pre {
    margin: 0;
    font-size: 12px;
    line-height: 1.4;
    white-space: pre-wrap;
    word-break: break-word;
  }
`;

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo
    });

    // 在开发环境下打印错误信息
    if (process.env.NODE_ENV === 'development') {
      console.error('ErrorBoundary caught an error:', error, errorInfo);
    }

    // 在生产环境下可以发送错误报告到监控服务
    if (process.env.NODE_ENV === 'production') {
      // TODO: 发送错误报告到监控服务
      // reportError(error, errorInfo);
    }
  }

  handleReload = () => {
    window.location.reload();
  };

  handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 默认错误UI
      return (
        <ErrorContainer>
          <Result
            status="error"
            title="应用程序出现错误"
            subTitle="抱歉，应用程序遇到了一个意外错误。请尝试刷新页面或联系技术支持。"
            extra={[
              <Button type="primary" key="reload" onClick={this.handleReload}>
                刷新页面
              </Button>,
              <Button key="reset" onClick={this.handleReset}>
                重试
              </Button>
            ]}
          >
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <ErrorDetails>
                <summary>错误详情 (仅开发环境显示)</summary>
                <div>
                  <strong>错误信息:</strong>
                  <pre>{this.state.error.message}</pre>
                </div>
                <div>
                  <strong>错误堆栈:</strong>
                  <pre>{this.state.error.stack}</pre>
                </div>
                {this.state.errorInfo && (
                  <div>
                    <strong>组件堆栈:</strong>
                    <pre>{this.state.errorInfo.componentStack}</pre>
                  </div>
                )}
              </ErrorDetails>
            )}
          </Result>
        </ErrorContainer>
      );
    }

    return this.props.children;
  }
}
