# PagePlug 架构概览

## 1. 整体架构设计

### 1.1 分层架构
PagePlug 采用经典的分层架构模式，从上到下分为：

```
┌─────────────────────────────────────────────────────────────┐
│                     用户界面层                               │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │  Web编辑器   │  │  移动端应用  │  │  数据源管理  │        │
│  │   (React)   │  │   (Taro)    │  │   (React18)    │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
├─────────────────────────────────────────────────────────────┤
│                     网关层                                   │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              Nginx 反向代理 + 路由                       │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                     应用服务层                               │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │  应用管理    │  │  数据源服务  │  │  用户服务    │        │
│  │   服务      │  │             │  │             │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
├─────────────────────────────────────────────────────────────┤
│                     插件层                                   │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │  数据库插件  │  │  API插件     │  │  AI插件      │        │
│  │             │  │             │  │             │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
├─────────────────────────────────────────────────────────────┤
│                     数据存储层                               │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   MongoDB   │  │    Redis    │  │  外部数据源  │        │
│  │  (主数据库)  │  │   (缓存)    │  │             │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 微服务架构特征
虽然当前是单体应用，但具备微服务化的基础：
- **模块化设计**: 各功能模块相对独立
- **插件化架构**: 数据源插件可独立部署
- **API驱动**: 前后端通过RESTful API通信
- **无状态设计**: 服务层无状态，便于水平扩展

## 2. 前端架构详解

### 2.1 React 主应用架构
```
app/client/src/
├── components/          # 通用组件
│   ├── ads/            # 设计系统组件
│   ├── editorComponents/ # 编辑器专用组件
│   └── propertyControls/ # 属性控制组件
├── pages/              # 页面组件
│   ├── Editor/         # 编辑器页面
│   ├── Applications/   # 应用管理页面
│   └── UserAuth/       # 用户认证页面
├── widgets/            # 可拖拽组件
│   ├── ButtonWidget/   # 按钮组件
│   ├── TableWidget/    # 表格组件
│   └── ChartWidget/    # 图表组件
├── sagas/              # Redux-Saga 异步处理
├── reducers/           # Redux 状态管理
├── selectors/          # 状态选择器
├── actions/            # Action 定义
└── utils/              # 工具函数
```

### 2.2 状态管理架构
采用 Redux + Redux-Saga 模式：

```javascript
// 状态树结构
{
  entities: {
    applications: {},    // 应用数据
    datasources: {},     // 数据源数据
    actions: {},         // 动作数据
    widgets: {},         // 组件数据
    plugins: {}          // 插件数据
  },
  ui: {
    editor: {},          // 编辑器UI状态
    propertyPane: {},    // 属性面板状态
    debugger: {},        // 调试器状态
    explorer: {}         // 资源管理器状态
  },
  evaluations: {},       // 表达式计算结果
  errors: {}             // 错误信息
}
```

### 2.3 组件系统架构
基于高阶组件(HOC)和组合模式：

```typescript
// 组件基类
abstract class BaseWidget extends Component {
  // 通用属性和方法
}

// 组件工厂
class WidgetFactory {
  static create(type: string, props: any): ReactNode
}

// 属性控制系统
interface PropertyControl {
  controlType: string;
  propertyName: string;
  validation?: ValidationConfig;
}
```


## 3. 后端架构详解

### 3.1 Spring Boot 应用架构
```
app/server/appsmith-server/src/main/java/com/appsmith/server/
├── controllers/        # REST控制器
│   ├── DatasourceController.java
│   ├── ApplicationController.java
│   └── ActionController.java
├── services/          # 业务服务层
│   ├── DatasourceService.java
│   ├── ApplicationService.java
│   └── ActionService.java
├── repositories/      # 数据访问层
│   ├── DatasourceRepository.java
│   └── ApplicationRepository.java
├── domains/           # 领域模型
│   ├── Datasource.java
│   ├── Application.java
│   └── Action.java
├── dtos/              # 数据传输对象
├── configurations/    # 配置类
└── exceptions/        # 异常处理
```

### 3.2 响应式编程模型
基于 Project Reactor 的响应式编程：

```java
// 服务层示例
@Service
public class DatasourceService {

    public Mono<Datasource> create(Datasource datasource) {
        return validateDatasource(datasource)
            .flatMap(this::saveDatasource)
            .flatMap(this::testConnection)
            .doOnSuccess(this::cacheResult)
            .doOnError(this::handleError);
    }

    public Flux<Datasource> findByWorkspace(String workspaceId) {
        return repository.findByWorkspaceId(workspaceId)
            .filter(this::isAccessible)
            .map(this::enrichWithMetadata);
    }
}
```

### 3.3 插件系统架构
基于 PF4J 框架的插件系统：

```
app/server/appsmith-plugins/
├── mysqlPlugin/        # MySQL插件
├── postgresPlugin/     # PostgreSQL插件
├── mongoPlugin/        # MongoDB插件
├── restApiPlugin/      # REST API插件
└── ...                # 其他插件

// 插件接口定义
public interface PluginExecutor<T> {
    Mono<T> datasourceCreate(DatasourceConfiguration config);
    void datasourceDestroy(T connection);
    Mono<ActionExecutionResult> execute(T connection,
                                       DatasourceConfiguration datasourceConfig,
                                       ActionConfiguration actionConfig);
    Set<String> validateDatasource(DatasourceConfiguration config);
}
```

## 4. 数据架构

### 4.1 MongoDB 数据模型
```javascript
// 应用集合
applications: {
  _id: ObjectId,
  name: String,
  workspaceId: ObjectId,
  pages: [ObjectId],
  datasources: [ObjectId],
  publishedPages: [ObjectId],
  createdAt: Date,
  updatedAt: Date
}

// 数据源集合
datasources: {
  _id: ObjectId,
  name: String,
  pluginId: ObjectId,
  workspaceId: ObjectId,
  datasourceConfiguration: {
    url: String,
    authentication: Object,
    properties: [Object]
  },
  isValid: Boolean,
  structure: Object
}

// 页面集合
pages: {
  _id: ObjectId,
  name: String,
  applicationId: ObjectId,
  layouts: [Object],
  actions: [ObjectId]
}
```

### 4.2 Redis 缓存策略
```
缓存键命名规范:
- datasource:structure:{datasourceId}  # 数据源结构缓存
- user:session:{userId}                # 用户会话缓存
- application:config:{appId}           # 应用配置缓存
- plugin:metadata:{pluginId}           # 插件元数据缓存

缓存过期策略:
- 数据源结构: 1小时
- 用户会话: 24小时
- 应用配置: 30分钟
- 插件元数据: 永不过期(手动刷新)
```

## 5. 安全架构

### 5.1 认证授权体系
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   用户登录   │───▶│  JWT Token  │───▶│  权限验证    │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       ▼                   ▼                   ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  OAuth集成   │    │  Token刷新   │    │  RBAC控制   │
└─────────────┘    └─────────────┘    └─────────────┘
```

### 5.2 数据安全措施
- **传输加密**: HTTPS/TLS 1.3
- **存储加密**: 敏感数据AES-256加密
- **访问控制**: 基于角色的权限控制
- **审计日志**: 操作日志记录和分析

### 5.3 API安全
- **请求签名**: HMAC-SHA256签名验证
- **频率限制**: 基于用户和IP的限流
- **输入验证**: 严格的参数校验
- **SQL注入防护**: 参数化查询

## 6. 性能架构

### 6.1 前端性能优化
- **代码分割**: 基于路由的懒加载
- **组件缓存**: React.memo 和 useMemo
- **虚拟滚动**: 大列表性能优化
- **图片优化**: WebP格式和懒加载

### 6.2 后端性能优化
- **连接池**: HikariCP数据库连接池
- **查询优化**: MongoDB索引优化
- **缓存策略**: 多级缓存体系
- **异步处理**: 非阻塞I/O操作

### 6.3 网络性能优化
- **CDN加速**: 静态资源CDN分发
- **Gzip压缩**: HTTP响应压缩
- **HTTP/2**: 多路复用支持
- **缓存策略**: 浏览器和代理缓存

## 7. 可扩展性设计

### 7.1 水平扩展能力
- **无状态服务**: 服务实例可任意扩展
- **数据库分片**: MongoDB分片集群
- **负载均衡**: Nginx负载均衡
- **容器化部署**: Docker + Kubernetes

### 7.2 插件扩展机制
- **热插拔**: 运行时插件加载/卸载
- **版本管理**: 插件版本兼容性
- **依赖管理**: 插件依赖解析
- **沙箱隔离**: 插件运行环境隔离

### 7.3 API扩展性
- **版本控制**: API版本管理
- **向后兼容**: 渐进式API演进
- **文档生成**: 自动API文档生成
- **SDK支持**: 多语言SDK

## 8. 监控和运维架构

### 8.1 应用监控
- **性能监控**: APM工具集成
- **错误追踪**: 异常监控和告警
- **业务监控**: 关键指标监控
- **用户行为**: 用户操作分析

### 8.2 基础设施监控
- **服务器监控**: CPU、内存、磁盘
- **网络监控**: 带宽、延迟、丢包
- **数据库监控**: 连接数、查询性能
- **容器监控**: Docker容器状态

### 8.3 日志管理
- **日志收集**: ELK Stack
- **日志分析**: 实时日志分析
- **告警机制**: 基于日志的告警
- **审计追踪**: 操作审计日志

## 9. 部署架构

### 9.1 容器化部署
```yaml
# Docker Compose 示例
version: '3.8'
services:
  pageplug-server:
    image: pageplug/server:latest
    ports:
      - "8080:8080"
    environment:
      - MONGODB_URI=mongodb://mongo:27017/pageplug
      - REDIS_URL=redis://redis:6379

  pageplug-client:
    image: pageplug/client:latest
    ports:
      - "3000:80"
    depends_on:
      - pageplug-server
```

### 9.2 Kubernetes部署
- **Pod管理**: 应用Pod自动调度
- **服务发现**: Service和Ingress
- **配置管理**: ConfigMap和Secret
- **存储管理**: PersistentVolume

### 9.3 CI/CD流程
- **代码提交**: Git Hook触发
- **自动构建**: Docker镜像构建
- **自动测试**: 单元测试和集成测试
- **自动部署**: 滚动更新部署
