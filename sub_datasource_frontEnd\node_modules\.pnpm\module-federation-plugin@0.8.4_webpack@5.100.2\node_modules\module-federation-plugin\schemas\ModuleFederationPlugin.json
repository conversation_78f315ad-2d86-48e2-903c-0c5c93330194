{"definitions": {"Exposes": {"description": "Modules that should be exposed by this container. Property names are used as public paths.", "type": "object", "additionalProperties": {"description": "Modules that should be exposed by this container.", "anyOf": [{"$ref": "#/definitions/ExposesConfig"}, {"$ref": "#/definitions/ExposesItem"}]}}, "ExposesConfig": {"description": "Advanced configuration for modules that should be exposed by this container.", "type": "object", "additionalProperties": false, "properties": {"import": {"$ref": "#/definitions/ExposesItem"}, "name": {"description": "Custom chunk name for the exposed module.", "type": "string"}}, "required": ["import"]}, "ExposesItem": {"description": "Module that should be exposed by this container.", "type": "string", "minLength": 1}, "ExternalsType": {"description": "Specifies the default type of externals.", "enum": ["this", "window", "self", "global", "script"]}, "LibraryName": {"description": "The name of the library (some types allow unnamed libraries too).", "type": "string", "minLength": 1}, "LibraryOptions": {"description": "Options for library.", "type": "object", "additionalProperties": false, "properties": {"name": {"$ref": "#/definitions/LibraryName"}, "type": {"$ref": "#/definitions/LibraryType"}}}, "LibraryType": {"description": "Type of library (types included by default are 'var', 'this', 'window', 'self', 'global', 'commonjs', 'commonjs2').", "enum": ["var", "this", "window", "self", "global", "commonjs", "commonjs2"]}, "Remotes": {"description": "Container locations from which modules should be resolved and loaded at runtime. Property names are used as request scopes.", "type": "object", "additionalProperties": {"description": "Container location from which modules should be resolved and loaded at runtime.", "anyOf": [{"$ref": "#/definitions/RemotesConfig"}, {"$ref": "#/definitions/RemotesItem"}]}}, "RemotesConfig": {"description": "Advanced configuration for container locations from which modules should be resolved and loaded at runtime.", "type": "object", "additionalProperties": false, "properties": {"external": {"$ref": "#/definitions/RemotesItem"}}, "required": ["external"]}, "RemotesItem": {"description": "Container location from which modules should be resolved and loaded at runtime.", "type": "string", "minLength": 1}, "AdditionalFeatures": {"description": "Features in addition to the module federation", "type": "object", "properties": {"asyncChunkMode": {"description": "Default mode for dynamic imports of remote modules.", "type": "string"}, "keepAsync": {"description": "Whether to disable replacing the module with a synchronized one after loaded.", "type": "boolean"}}}}, "title": "ModuleFederationPluginOptions", "type": "object", "additionalProperties": false, "properties": {"exposes": {"$ref": "#/definitions/Exposes"}, "filename": {"description": "The filename of the container as relative path inside the `output.path` directory.", "type": "string", "absolutePath": false}, "library": {"$ref": "#/definitions/LibraryOptions"}, "name": {"description": "The name of the container.", "type": "string"}, "remoteType": {"description": "The external type of the remote containers.", "oneOf": [{"$ref": "#/definitions/ExternalsType"}]}, "remotes": {"$ref": "#/definitions/Remotes"}, "additionalFeatures": {"$ref": "#/definitions/AdditionalFeatures"}}}