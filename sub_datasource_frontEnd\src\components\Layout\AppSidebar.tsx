import React, { useState, useEffect } from 'react';
import { Layout, Menu, Button } from 'antd';
import {
  DatabaseOutlined,
  CodeOutlined,
  AppstoreOutlined,
  SettingOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  PlusOutlined,
  SearchOutlined
} from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import styled from 'styled-components';

import { useTheme } from '../../hooks/useTheme';

const { Sider } = Layout;

// 样式化组件
const StyledSider = styled(Sider)<{ $isDark: boolean }>`
  background: ${props => props.$isDark ? '#1f1f1f' : '#ffffff'} !important;
  border-right: 1px solid ${props => props.$isDark ? '#303030' : '#f0f0f0'};
  
  .ant-layout-sider-trigger {
    background: ${props => props.$isDark ? '#262626' : '#fafafa'} !important;
    color: ${props => props.$isDark ? '#ffffff' : '#262626'} !important;
    border-top: 1px solid ${props => props.$isDark ? '#303030' : '#f0f0f0'};
  }
`;

const SidebarHeader = styled.div<{ $collapsed: boolean; $isDark: boolean }>`
  padding: 16px;
  border-bottom: 1px solid ${props => props.$isDark ? '#303030' : '#f0f0f0'};
  display: flex;
  align-items: center;
  justify-content: ${props => props.$collapsed ? 'center' : 'space-between'};
  min-height: 64px;
`;

const QuickActions = styled.div<{ $collapsed: boolean }>`
  display: ${props => props.$collapsed ? 'none' : 'flex'};
  gap: 8px;
`;

const StyledMenu = styled(Menu)<{ $isDark: boolean }>`
  border-right: none !important;
  background: transparent !important;
  
  .ant-menu-item {
    margin: 4px 8px !important;
    border-radius: 6px !important;
    width: auto !important;
    color: ${props => props.$isDark ? '#d9d9d9' : '#595959'} !important;
    
    &:hover {
      background-color: ${props => props.$isDark ? '#262626' : '#f5f5f5'} !important;
      color: ${props => props.$isDark ? '#ffffff' : '#262626'} !important;
    }
    
    &.ant-menu-item-selected {
      background-color: #e6f7ff !important;
      color: #1890ff !important;
      
      .ant-menu-item-icon {
        color: #1890ff !important;
      }
    }
  }
  
  .ant-menu-item-icon {
    color: ${props => props.$isDark ? '#d9d9d9' : '#8c8c8c'} !important;
  }
  
  .ant-menu-submenu-title {
    margin: 4px 8px !important;
    border-radius: 6px !important;
    width: auto !important;
    color: ${props => props.$isDark ? '#d9d9d9' : '#595959'} !important;
    
    &:hover {
      background-color: ${props => props.$isDark ? '#262626' : '#f5f5f5'} !important;
      color: ${props => props.$isDark ? '#ffffff' : '#262626'} !important;
    }
  }
`;

const CollapseButton = styled(Button)<{ $isDark: boolean }>`
  border: none;
  background: transparent;
  color: ${props => props.$isDark ? '#d9d9d9' : '#595959'};
  
  &:hover {
    background: ${props => props.$isDark ? '#262626' : '#f5f5f5'};
    color: ${props => props.$isDark ? '#ffffff' : '#262626'};
  }
`;

type MenuItem = Required<MenuProps>['items'][number];

export const AppSidebar: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const { isDark } = useTheme();
  const navigate = useNavigate();
  const location = useLocation();

  // 菜单项配置
  const menuItems: MenuItem[] = [
    {
      key: '/datasources',
      icon: <DatabaseOutlined />,
      label: '数据源管理',
      children: [
        {
          key: '/datasources',
          label: '数据源列表'
        },
        {
          key: '/datasources/create',
          label: '新建数据源'
        }
      ]
    },
    {
      key: '/query',
      icon: <CodeOutlined />,
      label: '查询编辑器'
    },
    {
      key: '/plugins',
      icon: <AppstoreOutlined />,
      label: '插件管理'
    },
    {
      type: 'divider'
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '系统设置'
    }
  ];

  // 根据当前路径设置选中的菜单项
  const getSelectedKeys = () => {
    const path = location.pathname;
    if (path.startsWith('/datasources')) {
      return [path];
    }
    return [path];
  };

  // 根据当前路径设置展开的菜单项
  const getOpenKeys = () => {
    const path = location.pathname;
    if (path.startsWith('/datasources')) {
      return ['/datasources'];
    }
    return [];
  };

  const [selectedKeys, setSelectedKeys] = useState<string[]>(getSelectedKeys());
  const [openKeys, setOpenKeys] = useState<string[]>(getOpenKeys());

  // 监听路由变化更新选中状态
  useEffect(() => {
    setSelectedKeys(getSelectedKeys());
    setOpenKeys(getOpenKeys());
  }, [location.pathname]);

  // 菜单点击处理
  const handleMenuClick: MenuProps['onClick'] = ({ key }) => {
    navigate(key);
  };

  // 子菜单展开/收起处理
  const handleOpenChange = (keys: string[]) => {
    setOpenKeys(keys);
  };

  // 快速操作按钮
  const handleQuickAction = (action: string) => {
    switch (action) {
      case 'create':
        navigate('/datasources/create');
        break;
      case 'search':
        // TODO: 打开搜索面板
        console.log('打开搜索');
        break;
      default:
        break;
    }
  };

  return (
    <StyledSider
      $isDark={isDark}
      collapsible
      collapsed={collapsed}
      onCollapse={setCollapsed}
      trigger={null}
      width={240}
      collapsedWidth={64}
    >
      <SidebarHeader $collapsed={collapsed} $isDark={isDark}>
        <CollapseButton
          $isDark={isDark}
          type="text"
          icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          onClick={() => setCollapsed(!collapsed)}
          title={collapsed ? '展开侧边栏' : '收起侧边栏'}
        />
        
        <QuickActions $collapsed={collapsed}>
          <Button
            type="primary"
            size="small"
            icon={<PlusOutlined />}
            onClick={() => handleQuickAction('create')}
            title="新建数据源"
          >
            新建
          </Button>
          <Button
            size="small"
            icon={<SearchOutlined />}
            onClick={() => handleQuickAction('search')}
            title="搜索"
          />
        </QuickActions>
      </SidebarHeader>

      <StyledMenu
        $isDark={isDark}
        mode="inline"
        selectedKeys={selectedKeys}
        openKeys={openKeys}
        items={menuItems}
        onClick={handleMenuClick}
        onOpenChange={handleOpenChange}
        inlineIndent={16}
      />
    </StyledSider>
  );
};
