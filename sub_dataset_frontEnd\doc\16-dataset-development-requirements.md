# 数据集模块开发需求文档

## 1. 项目概述

### 1.1 项目目标
开发PagePlug数据集微前端模块，提供API管理、数据库查询、JS对象编辑等功能，作为独立的React18微前端应用集成到主系统中。

### 1.2 技术要求
- **前端框架**: React 18.2+ + TypeScript 5.0+
- **微前端方案**: Module Federation
- **状态管理**: Redux Toolkit + React Query
- **UI组件库**: Ant Design 5.0+
- **样式方案**: Styled Components (CSS-in-JS)
- **代码编辑器**: Monaco Editor
- **构建工具**: Webpack 5

### 1.3 功能范围
```
数据集模块功能范围:
├── API管理
│   ├── REST API编辑器
│   ├── GraphQL编辑器
│   ├── API测试和调试
│   └── API文档生成
├── 数据库查询
│   ├── SQL查询编辑器
│   ├── NoSQL查询编辑器
│   ├── 查询构建器
│   └── 查询结果展示
├── JS对象管理
│   ├── JavaScript代码编辑
│   ├── 函数管理
│   ├── 变量管理
│   └── 代码执行和调试
├── 数据集合
│   ├── 集合创建和管理
│   ├── 查询组织
│   └── 批量执行
└── 查询模板
    ├── 预定义模板
    ├── 自定义模板
    └── 模板应用
```

## 2. 功能需求详细说明

### 2.1 API管理模块

#### 2.1.1 REST API编辑器
**功能描述**: 提供可视化的REST API编辑界面

**核心功能**:
- HTTP方法选择 (GET, POST, PUT, DELETE, PATCH)
- URL路径配置，支持路径参数
- 请求头管理，支持动态值绑定
- 查询参数配置
- 请求体编辑 (JSON, Form Data, Raw)
- 认证配置 (Basic Auth, Bearer Token, API Key)
- 响应处理和格式化

**界面要求**:
```
REST API编辑器界面布局:
┌─────────────────────────────────────────────────────────────┐
│  [HTTP方法] [URL输入框                              ] [执行] │
├─────────────────────────────────────────────────────────────┤
│  标签页: [参数] [请求头] [请求体] [认证] [设置]              │
├─────────────────────────────────────────────────────────────┤
│  参数配置区域                                                │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │ 参数名      │ 参数值      │ 描述        │ 操作        │  │
│  ├─────────────┼─────────────┼─────────────┼─────────────┤  │
│  │ [输入框]    │ [输入框]    │ [输入框]    │ [删除]      │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
│  [+ 添加参数]                                               │
├─────────────────────────────────────────────────────────────┤
│  响应结果区域                                                │
│  标签页: [响应] [日志] [文档]                               │
│  ┌─────────────────────────────────────────────────────────┐│
│  │ 响应状态: 200 OK                                       ││
│  │ 响应时间: 245ms                                        ││
│  │ 响应大小: 1.2KB                                        ││
│  │                                                         ││
│  │ [JSON格式化显示区域]                                   ││
│  └─────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

**技术实现**:
```typescript
// REST API编辑器组件
interface RestAPIEditorProps {
  action: Action;
  onUpdate: (action: Action) => void;
  onExecute: (action: Action) => void;
  isExecuting: boolean;
  response?: ActionResponse;
}

const RestAPIEditor: React.FC<RestAPIEditorProps> = ({
  action,
  onUpdate,
  onExecute,
  isExecuting,
  response
}) => {
  const [activeTab, setActiveTab] = useState('params');
  
  return (
    <APIEditorContainer>
      <div className="api-editor">
        <div className="editor-header">
          <div className="request-line">
            <Select 
              value={action.actionConfiguration.httpMethod}
              onChange={(method) => updateAction({ httpMethod: method })}
              options={HTTP_METHODS}
            />
            <Input
              value={action.actionConfiguration.path}
              onChange={(e) => updateAction({ path: e.target.value })}
              placeholder="输入API端点URL"
            />
            <Button 
              type="primary" 
              loading={isExecuting}
              onClick={() => onExecute(action)}
            >
              执行
            </Button>
          </div>
        </div>
        
        <div className="editor-content">
          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane tab="参数" key="params">
              <ParameterEditor 
                parameters={action.actionConfiguration.queryParameters}
                onChange={(params) => updateAction({ queryParameters: params })}
              />
            </TabPane>
            <TabPane tab="请求头" key="headers">
              <HeaderEditor 
                headers={action.actionConfiguration.headers}
                onChange={(headers) => updateAction({ headers })}
              />
            </TabPane>
            <TabPane tab="请求体" key="body">
              <BodyEditor 
                body={action.actionConfiguration.body}
                contentType={getContentType(action.actionConfiguration.headers)}
                onChange={(body) => updateAction({ body })}
              />
            </TabPane>
          </Tabs>
        </div>
        
        <div className="response-section">
          <ResponseViewer response={response} />
        </div>
      </div>
    </APIEditorContainer>
  );
};
```

#### 2.1.2 GraphQL编辑器
**功能描述**: 提供GraphQL查询和变更的编辑界面

**核心功能**:
- GraphQL查询语法高亮
- 变量编辑器
- Schema浏览器
- 查询验证
- 自动补全

**界面要求**:
```
GraphQL编辑器界面布局:
┌─────────────────────────────────────────────────────────────┐
│  GraphQL查询编辑器                              [执行] [格式化] │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────────┬─────────────────────┬─────────────┐ │
│  │ 查询编辑器          │ 变量编辑器          │ Schema浏览器 │ │
│  │                     │                     │             │ │
│  │ query GetUser($id:  │ {                   │ ├─ Query    │ │
│  │   ID!) {            │   "id": "123"       │ │  ├─ user  │ │
│  │   user(id: $id) {   │ }                   │ │  └─ users │ │
│  │     id              │                     │ ├─ Mutation │ │
│  │     name            │                     │ └─ Types    │ │
│  │     email           │                     │             │ │
│  │   }                 │                     │             │ │
│  │ }                   │                     │             │ │
│  └─────────────────────┴─────────────────────┴─────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  响应结果                                                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ {                                                       │ │
│  │   "data": {                                             │ │
│  │     "user": {                                           │ │
│  │       "id": "123",                                      │ │
│  │       "name": "张三",                                   │ │
│  │       "email": "<EMAIL>"                   │ │
│  │     }                                                   │ │
│  │   }                                                     │ │
│  │ }                                                       │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 数据库查询模块

#### 2.2.1 SQL查询编辑器
**功能描述**: 提供SQL查询的编辑和执行界面

**核心功能**:
- SQL语法高亮和自动补全
- 表结构浏览器
- 查询历史记录
- 查询性能分析
- 结果分页和导出

**界面要求**:
```
SQL查询编辑器界面布局:
┌─────────────────────────────────────────────────────────────┐
│  [数据源选择] [查询类型] [保存] [执行] [格式化] [历史]        │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────────┬─────────────────────────────────────┐ │
│  │ 数据库结构          │ SQL查询编辑器                       │ │
│  │ ├─ 📁 tables        │                                     │ │
│  │ │  ├─ 📋 users      │ SELECT u.id, u.name, u.email       │ │
│  │ │  │  ├─ id (PK)    │ FROM users u                        │ │
│  │ │  │  ├─ name       │ WHERE u.status = 'active'           │ │
│  │ │  │  ├─ email      │ ORDER BY u.created_at DESC          │ │
│  │ │  │  └─ status     │ LIMIT {{Table1.pageSize}}           │ │
│  │ │  └─ 📋 orders     │ OFFSET {{Table1.pageOffset}};       │ │
│  │ └─ 📁 views         │                                     │ │
│  │    └─ 📋 user_stats │                                     │ │
│  └─────────────────────┴─────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  查询结果 [表格视图] [JSON视图] [导出CSV] [导出Excel]        │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ ID │ 姓名   │ 邮箱                    │ 状态   │ 创建时间 │ │
│  ├────┼────────┼─────────────────────────┼────────┼─────────┤ │
│  │ 1  │ 张三   │ <EMAIL>    │ active │ 2024-01 │ │
│  │ 2  │ 李四   │ <EMAIL>        │ active │ 2024-01 │ │
│  └─────────────────────────────────────────────────────────┘ │
│  共 156 条记录 | 第 1 页，共 16 页 | 执行时间: 23ms        │
└─────────────────────────────────────────────────────────────┘
```

#### 2.2.2 NoSQL查询编辑器
**功能描述**: 提供MongoDB等NoSQL数据库的查询界面

**核心功能**:
- MongoDB查询语法支持
- 聚合管道构建器
- 文档结构预览
- 查询优化建议

**界面要求**:
```
MongoDB查询编辑器界面布局:
┌─────────────────────────────────────────────────────────────┐
│  [集合选择] [操作类型] [执行] [格式化]                       │
├─────────────────────────────────────────────────────────────┤
│  操作类型: [Find] [Aggregate] [Insert] [Update] [Delete]    │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────────┬─────────────────────────────────────┐ │
│  │ 集合结构            │ 查询编辑器                          │ │
│  │ ├─ 📁 users         │ // Find查询                         │ │
│  │ │  └─ 📄 示例文档   │ {                                   │ │
│  │ │     {             │   "filter": {                       │ │
│  │ │       "_id": "...",│     "status": "active",             │ │
│  │ │       "name": "...",│     "age": { "$gte": 18 }          │ │
│  │ │       "age": 25,   │   },                                │ │
│  │ │       "status": "...",│   "sort": { "createdAt": -1 },     │ │
│  │ │       "tags": [...] │   "limit": {{Table1.pageSize}},     │ │
│  │ │     }             │   "skip": {{Table1.pageOffset}}     │ │
│  │ └─ 📁 orders        │ }                                   │ │
│  └─────────────────────┴─────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  查询结果 [文档视图] [表格视图] [JSON视图]                  │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ [                                                       │ │
│  │   {                                                     │ │
│  │     "_id": "507f1f77bcf86cd799439011",                  │ │
│  │     "name": "张三",                                     │ │
│  │     "age": 25,                                          │ │
│  │     "status": "active",                                 │ │
│  │     "tags": ["developer", "javascript"]                 │ │
│  │   },                                                    │ │
│  │   ...                                                   │ │
│  │ ]                                                       │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2.3 JS对象管理模块

#### 2.3.1 JavaScript代码编辑器
**功能描述**: 提供JavaScript代码的编辑和执行环境

**核心功能**:
- JavaScript语法高亮
- 代码自动补全
- 错误检查和提示
- 函数和变量管理
- 代码格式化

**界面要求**:
```
JS对象编辑器界面布局:
┌─────────────────────────────────────────────────────────────┐
│  JS对象: [对象名称]                    [保存] [运行] [格式化] │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────────┬─────────────────────────────────────┐ │
│  │ 代码编辑器          │ 函数和变量面板                      │ │
│  │                     │                                     │ │
│  │ export default {    │ 📁 变量                             │ │
│  │   // 变量定义       │ ├─ myVar1: "Hello"                  │ │
│  │   myVar1: "Hello",  │ ├─ myVar2: 123                      │ │
│  │   myVar2: 123,      │ └─ config: {...}                    │ │
│  │                     │                                     │ │
│  │   // 异步函数       │ 📁 函数                             │ │
│  │   fetchData: async  │ ├─ 🔧 fetchData() [async]           │ │
│  │     () => {         │ ├─ 🔧 processData()                 │ │
│  │     const response  │ ├─ 🔧 validateInput()               │ │
│  │       = await Api1  │ └─ 🔧 formatOutput()                │ │
│  │         .run();     │                                     │ │
│  │     return response │ 📁 依赖                             │ │
│  │       .data;        │ ├─ Api1                             │ │
│  │   },                │ ├─ Query1                           │ │
│  │                     │ └─ Table1                           │ │
│  │   // 数据处理函数   │                                     │ │
│  │   processData: (data│                                     │ │
│  │   ) => {            │                                     │ │
│  │     return data.map │                                     │ │
│  │       (item => ({   │                                     │ │
│  │       ...item,      │                                     │ │
│  │       formatted: true│                                     │ │
│  │     }));            │                                     │ │
│  │   }                 │                                     │ │
│  │ }                   │                                     │ │
│  └─────────────────────┴─────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  执行结果和日志                                              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ > fetchData()                                           │ │
│  │ ✅ 执行成功 (234ms)                                     │ │
│  │ 返回值: [                                               │ │
│  │   { id: 1, name: "张三", formatted: true },             │ │
│  │   { id: 2, name: "李四", formatted: true }              │ │
│  │ ]                                                       │ │
│  │                                                         │ │
│  │ > processData([...])                                    │ │
│  │ ✅ 执行成功 (12ms)                                      │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2.4 查询构建器模块

#### 2.4.1 可视化查询构建器
**功能描述**: 提供拖拽式的查询构建界面，无需编写代码

**核心功能**:
- 表和字段选择
- 条件构建器
- 排序和分组
- 聚合函数
- 预览生成的查询

**界面要求**:
```
可视化查询构建器界面布局:
┌─────────────────────────────────────────────────────────────┐
│  查询构建器 - [数据源选择]                    [预览SQL] [执行] │
├─────────────────────────────────────────────────────────────┤
│  1. 选择表和字段                                             │
│  ┌─────────────────────┬─────────────────────────────────────┐ │
│  │ 可用表              │ 已选择字段                          │ │
│  │ ☐ users             │ ✅ users.id (主键)                  │ │
│  │   ☐ id              │ ✅ users.name (姓名)                │ │
│  │   ☐ name            │ ✅ users.email (邮箱)               │ │
│  │   ☐ email           │ ✅ orders.total (订单金额)          │ │
│  │   ☐ created_at      │                                     │ │
│  │ ☐ orders            │ [+ 添加字段]                        │ │
│  │   ☐ id              │                                     │ │
│  │   ☐ user_id         │                                     │ │
│  │   ☐ total           │                                     │ │
│  └─────────────────────┴─────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  2. 设置查询条件                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ [users.status] [等于] [active]              [删除条件]  │ │
│  │ [AND] [users.age] [大于等于] [18]           [删除条件]  │ │
│  │ [AND] [orders.total] [大于] [100]           [删除条件]  │ │
│  │                                                         │ │
│  │ [+ 添加条件] [+ 添加条件组]                             │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  3. 排序和分组                                               │
│  ┌─────────────────────┬─────────────────────────────────────┐ │
│  │ 排序                │ 分组                                │ │
│  │ ✅ users.created_at │ ☐ users.status                      │ │
│  │    [降序 ▼]         │ ☐ users.department                  │ │
│  │ ☐ users.name        │                                     │ │
│  │    [升序 ▲]         │ 聚合函数:                           │ │
│  │                     │ ☐ COUNT(orders.id) as order_count   │ │
│  │ [+ 添加排序]        │ ☐ SUM(orders.total) as total_amount │ │
│  └─────────────────────┴─────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  4. 生成的SQL预览                                            │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ SELECT u.id, u.name, u.email, o.total                  │ │
│  │ FROM users u                                            │ │
│  │ LEFT JOIN orders o ON u.id = o.user_id                 │ │
│  │ WHERE u.status = 'active'                               │ │
│  │   AND u.age >= 18                                       │ │
│  │   AND o.total > 100                                     │ │
│  │ ORDER BY u.created_at DESC                              │ │
│  │ LIMIT 100;                                              │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 3. 技术实现要求

### 3.1 组件架构
```typescript
// 组件层次结构
DatasetApp/
├── APIEditor/
│   ├── RestAPIEditor
│   ├── GraphQLEditor
│   ├── RequestBuilder
│   ├── ResponseViewer
│   └── APITester
├── QueryEditor/
│   ├── SQLEditor
│   ├── NoSQLEditor
│   ├── QueryBuilder
│   ├── SchemaExplorer
│   └── ResultViewer
├── JSEditor/
│   ├── CodeEditor
│   ├── FunctionManager
│   ├── VariableManager
│   ├── DependencyTracker
│   └── ExecutionConsole
├── CollectionManager/
│   ├── CollectionList
│   ├── CollectionForm
│   ├── ActionOrganizer
│   └── BatchExecutor
└── Shared/
    ├── ParameterInput
    ├── DataViewer
    ├── ExecutionButton
    ├── ErrorBoundary
    └── LoadingSpinner
```

### 3.2 状态管理
```typescript
// Redux Store结构
interface DatasetState {
  actions: {
    items: Record<string, Action>;
    currentAction: Action | null;
    loading: boolean;
    error: string | null;
  };
  collections: {
    items: Record<string, Collection>;
    currentCollection: Collection | null;
    loading: boolean;
    error: string | null;
  };
  execution: {
    results: Record<string, ActionResult>;
    isExecuting: Record<string, boolean>;
    errors: Record<string, string>;
  };
  ui: {
    activeTab: string;
    sidebarCollapsed: boolean;
    theme: ThemeConfig;
  };
}
```

### 3.3 API接口设计
```typescript
// API服务接口
interface ActionAPI {
  // Action CRUD
  getActions(applicationId: string): Promise<Action[]>;
  createAction(action: CreateActionRequest): Promise<Action>;
  updateAction(id: string, action: UpdateActionRequest): Promise<Action>;
  deleteAction(id: string): Promise<void>;
  
  // Action执行
  executeAction(id: string, params?: Record<string, any>): Promise<ActionResult>;
  validateAction(action: Action): Promise<ValidationResult>;
  
  // 模板相关
  getTemplates(pluginType?: PluginType): Promise<QueryTemplate[]>;
  applyTemplate(templateId: string, variables: Record<string, any>): Promise<Action>;
}

interface CollectionAPI {
  getCollections(applicationId: string): Promise<Collection[]>;
  createCollection(collection: CreateCollectionRequest): Promise<Collection>;
  updateCollection(id: string, collection: UpdateCollectionRequest): Promise<Collection>;
  deleteCollection(id: string): Promise<void>;
  
  addActionToCollection(collectionId: string, actionId: string): Promise<Collection>;
  removeActionFromCollection(collectionId: string, actionId: string): Promise<Collection>;
  executeCollection(id: string): Promise<CollectionResult>;
}
```

## 4. 开发计划

### 4.1 开发阶段
```
Phase 1: 基础架构 (2周)
├── 项目搭建和配置
├── Module Federation配置
├── 基础组件库
├── 路由和状态管理
└── 样式系统

Phase 2: API编辑器 (3周)
├── REST API编辑器
├── GraphQL编辑器
├── 请求构建器
├── 响应查看器
└── API测试功能

Phase 3: 查询编辑器 (3周)
├── SQL编辑器
├── NoSQL编辑器
├── 查询构建器
├── 结果展示
└── 数据库结构浏览

Phase 4: JS对象编辑器 (2周)
├── 代码编辑器
├── 函数管理
├── 变量管理
└── 执行环境

Phase 5: 集合管理 (2周)
├── 集合创建和管理
├── 查询组织
├── 批量执行
└── 结果聚合

Phase 6: 集成测试 (1周)
├── 功能测试
├── 性能测试
├── 兼容性测试
└── 用户验收测试
```

### 4.2 质量要求
- **代码覆盖率**: ≥ 80%
- **性能要求**: 首屏加载 < 3s，操作响应 < 500ms
- **兼容性**: 支持Chrome 90+, Firefox 88+, Safari 14+
- **可访问性**: 符合WCAG 2.1 AA标准
- **安全性**: 防止XSS、CSRF攻击，敏感数据加密

### 4.3 交付物
- 完整的数据集微前端应用
- 组件库文档
- API接口文档
- 用户使用手册
- 部署和运维文档
- 单元测试和集成测试
