# This workflow is responsible for building, testing & packaging the RTS Node server.
name: Build RTS Workflow

on:
  # This line enables manual triggering of this workflow.
  workflow_dispatch:
  workflow_call:
    inputs:
      pr:
        description: "This is the PR number in case the workflow is being called in a pull request"
        required: false
        type: number
      skip-tests:
        description: "This is a boolean value in case the workflow is being called in build deploy-preview"
        required: false
        type: string
        default: "false"
      branch:
        description: "This is the branch to be used for the build."
        required: false
        type: string

  pull_request:
    branches: [release, master]
    paths:
      - "app/client/packages/rts/**"

# Change the working directory for all the jobs in this workflow
defaults:
  run:
    working-directory: app/client/packages/rts

jobs:
  build:
    runs-on: appsmith-self-hosted-deployment-runner
    # Only run this workflow for internally triggered events
    if: |
      github.event.pull_request.head.repo.full_name == github.repository ||
      github.event_name == 'push' ||
      github.event_name == 'workflow_dispatch' ||
      github.event_name == 'repository_dispatch' ||
      github.event_name == 'schedule'

    steps:
      # The checkout steps MUST happen first because the default directory is set according to the code base.
      # GitHub Action expects all future commands to be executed in the code directory. Hence, we need to check out
      # the code before doing anything else.

      # Check out merge commit with the base branch in case this workflow is invoked via pull request
      - name: Checkout the merged commit from PR and base branch
        if: inputs.pr != 0
        uses: actions/checkout@v4
        with:
          fetch-tags: true
          ref: refs/pull/${{ inputs.pr }}/merge

      # Check out the specified branch in case this workflow is called by another workflow
      - name: Checkout the specified branch
        if: inputs.pr == 0 && inputs.branch != ''
        uses: actions/checkout@v4
        with:
          fetch-tags: true
          ref: ${{ inputs.branch }}

      # Checkout the code in the current branch in case the workflow is called because of a branch push event
      - name: Checkout the head commit of the branch
        if: inputs.pr == 0 && inputs.branch == ''
        uses: actions/checkout@v4
        with:
          fetch-tags: true

      - name: Figure out the PR number
        run: echo ${{ inputs.pr }}

      - name: Print the Github event
        run: echo ${{ github.event_name }}

      # In case this is second attempt try restoring status of the prior attempt from cache
      - name: Restore the previous run result
        uses: actions/cache@v3
        with:
          path: |
            ~/run_result
          key: ${{ github.run_id }}-${{ github.job }}-rts

      # Fetch prior run result
      - name: Get the previous run result
        id: run_result
        run: cat ~/run_result 2>/dev/null || echo 'default'

      # In case of prior failure run the job
      - if: steps.run_result.outputs.run_result != 'success'
        run: echo "I'm alive!" && exit 0

      - name: Use Node.js
        if: steps.run_result.outputs.run_result != 'success'
        uses: actions/setup-node@v3
        with:
          node-version-file: app/client/package.json

      # actions/setup-node@v3 doesn’t work properly with Yarn 3
      # when the project lives in a subdirectory: https://github.com/actions/setup-node/issues/488
      # Restoring the cache manually instead
      - name: Restore Yarn cache
        if: steps.run_result.outputs.run_result != 'success'
        uses: actions/cache@v3
        with:
          path: app/client/.yarn/cache
          key: v1-yarn3-${{ hashFiles('app/client/yarn.lock') }}
          restore-keys: |
            v1-yarn3-

      # Install all the dependencies
      - name: Install dependencies
        if: steps.run_result.outputs.run_result != 'success'
        run: |
          npm install -g yarn
          yarn install --immutable

      # Run the Jest tests only if the workflow has been invoked in a PR
      - name: Run the jest tests
        if: steps.run_result.outputs.run_result != 'success' && inputs.pr != 0 && inputs.skip-tests != 'true'
        run: yarn run test:unit

      - name: Build
        if: steps.run_result.outputs.run_result != 'success'
        run: |
          yarn build

      # Set status = failure
      - name: Set result as failed if there are build failures
        if: failure()
        run: |
          echo "run_result=failed" >> $GITHUB_OUTPUT > ~/run_result
          exit 1;

      # Restore the previous built bundle if present. If not push the newly built into the cache
      - name: Restore the previous bundle
        uses: actions/cache@v3
        with:
          path: |
            app/client/packages/rts/dist/
          key: ${{ github.run_id }}-${{ github.job }}-rts

      # Tar the bundles to speed up the upload & download process
      - name: Tar the rts bundles
        run: |
          tar -cvf rts-dist.tar dist

      # Upload the build artifacts and dependencies so that it can be used by the test & deploy job in other workflows
      - name: Upload rts build bundle
        uses: actions/upload-artifact@v3
        with:
          name: rts-dist
          path: app/client/packages/rts/rts-dist.tar

      # Set status = success
      - name: Save the status of the run
        run: echo "run_result=success" >> $GITHUB_OUTPUT > ~/run_result
