// Type definitions for copy-to-clipboard 3.0
// Project: https://github.com/sudodoki/copy-to-clipboard
// Definitions by: <PERSON> <https://github.com/Denis<PERSON>ar<PERSON>>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/<PERSON><PERSON>>

interface Options {
  debug?: boolean;
  message?: string;
  format?: string; // MIME type
  onCopy?: (clipboardData: object) => void;
}

declare function copy(text: string, options?: Options): boolean;
declare namespace copy { }
export = copy;
