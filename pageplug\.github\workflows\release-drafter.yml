name: Release Drafter

on:
  push:
    branches:
      - release
    # Only trigger if files have changed in this specific path
    paths:
      - "app/client/**"
      - "app/server/**"
      - "app/client/packages/rts/**"

  # pull_request event is required only for autolabeler
  # pull_request:
  #   types: [opened, reopened, synchronize]
  #   branches: [release]

jobs:
  update_release_draft:
    runs-on: ubuntu-latest
    steps:
      # Drafts your next Release notes as Pull Requests are merged into "master"
      - uses: release-drafter/release-drafter@v5
        with:
          # (Optional) specify config name to use, relative to .github/. Default: release-drafter.yml
          config-name: release-drafter-template.yml
          disable-autolabeler: true
          commitish: master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
