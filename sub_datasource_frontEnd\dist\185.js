"use strict";(self.webpackChunkpageplug_datasource_frontend=self.webpackChunkpageplug_datasource_frontend||[]).push([[185],{635:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval("{/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   G: () => (/* binding */ AppSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(6070);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(212);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(8076);\n/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(antd__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(4702);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(2691);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(5469);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(3460);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(6692);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(929);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(9458);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(858);\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(1299);\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(react_router_dom__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(3017);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var _hooks_useTheme__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(5869);\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nconst { Sider } = antd__WEBPACK_IMPORTED_MODULE_2__.Layout;\r\nconst StyledSider = styled_components__WEBPACK_IMPORTED_MODULE_12___default()(Sider) `\n  background: ${props => props.$isDark ? '#1f1f1f' : '#ffffff'} !important;\n  border-right: 1px solid ${props => props.$isDark ? '#303030' : '#f0f0f0'};\n  \n  .ant-layout-sider-trigger {\n    background: ${props => props.$isDark ? '#262626' : '#fafafa'} !important;\n    color: ${props => props.$isDark ? '#ffffff' : '#262626'} !important;\n    border-top: 1px solid ${props => props.$isDark ? '#303030' : '#f0f0f0'};\n  }\n`;\r\nconst SidebarHeader = (styled_components__WEBPACK_IMPORTED_MODULE_12___default().div) `\n  padding: 16px;\n  border-bottom: 1px solid ${props => props.$isDark ? '#303030' : '#f0f0f0'};\n  display: flex;\n  align-items: center;\n  justify-content: ${props => props.$collapsed ? 'center' : 'space-between'};\n  min-height: 64px;\n`;\r\nconst QuickActions = (styled_components__WEBPACK_IMPORTED_MODULE_12___default().div) `\n  display: ${props => props.$collapsed ? 'none' : 'flex'};\n  gap: 8px;\n`;\r\nconst StyledMenu = styled_components__WEBPACK_IMPORTED_MODULE_12___default()((0,antd__WEBPACK_IMPORTED_MODULE_2__.Menu)) `\n  border-right: none !important;\n  background: transparent !important;\n  \n  .ant-menu-item {\n    margin: 4px 8px !important;\n    border-radius: 6px !important;\n    width: auto !important;\n    color: ${props => props.$isDark ? '#d9d9d9' : '#595959'} !important;\n    \n    &:hover {\n      background-color: ${props => props.$isDark ? '#262626' : '#f5f5f5'} !important;\n      color: ${props => props.$isDark ? '#ffffff' : '#262626'} !important;\n    }\n    \n    &.ant-menu-item-selected {\n      background-color: #e6f7ff !important;\n      color: #1890ff !important;\n      \n      .ant-menu-item-icon {\n        color: #1890ff !important;\n      }\n    }\n  }\n  \n  .ant-menu-item-icon {\n    color: ${props => props.$isDark ? '#d9d9d9' : '#8c8c8c'} !important;\n  }\n  \n  .ant-menu-submenu-title {\n    margin: 4px 8px !important;\n    border-radius: 6px !important;\n    width: auto !important;\n    color: ${props => props.$isDark ? '#d9d9d9' : '#595959'} !important;\n    \n    &:hover {\n      background-color: ${props => props.$isDark ? '#262626' : '#f5f5f5'} !important;\n      color: ${props => props.$isDark ? '#ffffff' : '#262626'} !important;\n    }\n  }\n`;\r\nconst CollapseButton = styled_components__WEBPACK_IMPORTED_MODULE_12___default()((0,antd__WEBPACK_IMPORTED_MODULE_2__.Button)) `\n  border: none;\n  background: transparent;\n  color: ${props => props.$isDark ? '#d9d9d9' : '#595959'};\n  \n  &:hover {\n    background: ${props => props.$isDark ? '#262626' : '#f5f5f5'};\n    color: ${props => props.$isDark ? '#ffffff' : '#262626'};\n  }\n`;\r\nconst AppSidebar = () => {\r\n    const [collapsed, setCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\r\n    const { isDark } = (0,_hooks_useTheme__WEBPACK_IMPORTED_MODULE_13__/* .useTheme */ .D)();\r\n    const navigate = (0,react_router_dom__WEBPACK_IMPORTED_MODULE_11__.useNavigate)();\r\n    const location = (0,react_router_dom__WEBPACK_IMPORTED_MODULE_11__.useLocation)();\r\n    const menuItems = [\r\n        {\r\n            key: '/datasources',\r\n            icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* [\"default\"] */ .A, {}),\r\n            label: '数据源管理',\r\n            children: [\r\n                {\r\n                    key: '/datasources',\r\n                    label: '数据源列表'\r\n                },\r\n                {\r\n                    key: '/datasources/create',\r\n                    label: '新建数据源'\r\n                }\r\n            ]\r\n        },\r\n        {\r\n            key: '/query',\r\n            icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* [\"default\"] */ .A, {}),\r\n            label: '查询编辑器'\r\n        },\r\n        {\r\n            key: '/plugins',\r\n            icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* [\"default\"] */ .A, {}),\r\n            label: '插件管理'\r\n        },\r\n        {\r\n            type: 'divider'\r\n        },\r\n        {\r\n            key: '/settings',\r\n            icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* [\"default\"] */ .A, {}),\r\n            label: '系统设置'\r\n        }\r\n    ];\r\n    const getSelectedKeys = () => {\r\n        const path = location.pathname;\r\n        if (path.startsWith('/datasources')) {\r\n            return [path];\r\n        }\r\n        return [path];\r\n    };\r\n    const getOpenKeys = () => {\r\n        const path = location.pathname;\r\n        if (path.startsWith('/datasources')) {\r\n            return ['/datasources'];\r\n        }\r\n        return [];\r\n    };\r\n    const [selectedKeys, setSelectedKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(getSelectedKeys());\r\n    const [openKeys, setOpenKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(getOpenKeys());\r\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\r\n        setSelectedKeys(getSelectedKeys());\r\n        setOpenKeys(getOpenKeys());\r\n    }, [location.pathname]);\r\n    const handleMenuClick = ({ key }) => {\r\n        navigate(key);\r\n    };\r\n    const handleOpenChange = (keys) => {\r\n        setOpenKeys(keys);\r\n    };\r\n    const handleQuickAction = (action) => {\r\n        switch (action) {\r\n            case 'create':\r\n                navigate('/datasources/create');\r\n                break;\r\n            case 'search':\r\n                console.log('打开搜索');\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n    };\r\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(StyledSider, { \"$isDark\": isDark, collapsible: true, collapsed: collapsed, onCollapse: setCollapsed, trigger: null, width: 240, collapsedWidth: 64, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(SidebarHeader, { \"$collapsed\": collapsed, \"$isDark\": isDark, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(CollapseButton, { \"$isDark\": isDark, type: \"text\", icon: collapsed ? (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* [\"default\"] */ .A, {}) : (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* [\"default\"] */ .A, {}), onClick: () => setCollapsed(!collapsed), title: collapsed ? '展开侧边栏' : '收起侧边栏' }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(QuickActions, { \"$collapsed\": collapsed, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Button, { type: \"primary\", size: \"small\", icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* [\"default\"] */ .A, {}), onClick: () => handleQuickAction('create'), title: \"\\u65B0\\u5EFA\\u6570\\u636E\\u6E90\", children: \"\\u65B0\\u5EFA\" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Button, { size: \"small\", icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* [\"default\"] */ .A, {}), onClick: () => handleQuickAction('search'), title: \"\\u641C\\u7D22\" })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(StyledMenu, { \"$isDark\": isDark, mode: \"inline\", selectedKeys: selectedKeys, openKeys: openKeys, items: menuItems, onClick: handleMenuClick, onOpenChange: handleOpenChange, inlineIndent: 16 })] }));\r\n};\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///635\n\n}")},3043:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval("{/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IQ: () => (/* binding */ RouteComponents),\n/* harmony export */   PE: () => (/* binding */ preloadStrategy),\n/* harmony export */   wA: () => (/* binding */ initCodeSplitting)\n/* harmony export */ });\n/* unused harmony exports createLazyComponent, preloadComponent, LazyComponents, PreloadStrategy */\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(212);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3128);\n\r\n\r\nfunction createLazyComponent(importFunc, options = {}) {\r\n    const { fallback: Fallback = _components_Common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_1__/* .LoadingSpinner */ .kt, retries = 3, retryDelay = 1000, chunkName } = options;\r\n    const LazyComponent = react__WEBPACK_IMPORTED_MODULE_0___default().lazy(async () => {\r\n        let attempt = 0;\r\n        while (attempt < retries) {\r\n            try {\r\n                const module = await importFunc();\r\n                if (true) {\r\n                    console.log(`[Code Splitting] Successfully loaded chunk: ${chunkName || 'unknown'}`);\r\n                }\r\n                return module;\r\n            }\r\n            catch (error) {\r\n                attempt++;\r\n                if (attempt >= retries) {\r\n                    console.error(`[Code Splitting] Failed to load chunk after ${retries} attempts:`, error);\r\n                    return {\r\n                        default: () => react__WEBPACK_IMPORTED_MODULE_0___default().createElement('div', {\r\n                            style: {\r\n                                padding: '20px',\r\n                                textAlign: 'center',\r\n                                color: '#ff4d4f'\r\n                            }\r\n                        }, `加载组件失败: ${chunkName || 'unknown'}`)\r\n                    };\r\n                }\r\n                console.warn(`[Code Splitting] Attempt ${attempt} failed, retrying in ${retryDelay}ms:`, error);\r\n                await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));\r\n            }\r\n        }\r\n        throw new Error('Unreachable code');\r\n    });\r\n    const WrappedComponent = react__WEBPACK_IMPORTED_MODULE_0___default().forwardRef((props, ref) => {\r\n        return react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Suspense), { fallback: react__WEBPACK_IMPORTED_MODULE_0___default().createElement(Fallback) }, react__WEBPACK_IMPORTED_MODULE_0___default().createElement(LazyComponent, { ...props, ref }));\r\n    });\r\n    WrappedComponent.displayName = `LazyComponent(${chunkName || 'Unknown'})`;\r\n    return WrappedComponent;\r\n}\r\nfunction preloadComponent(importFunc) {\r\n    if ('requestIdleCallback' in window) {\r\n        requestIdleCallback(() => {\r\n            importFunc().catch(error => {\r\n                console.warn('[Preload] Failed to preload component:', error);\r\n            });\r\n        });\r\n    }\r\n    else {\r\n        setTimeout(() => {\r\n            importFunc().catch(error => {\r\n                console.warn('[Preload] Failed to preload component:', error);\r\n            });\r\n        }, 100);\r\n    }\r\n}\r\nconst RouteComponents = {\r\n    DatasourceList: createLazyComponent(() => Promise.all(/* import() */[__webpack_require__.e(96), __webpack_require__.e(876), __webpack_require__.e(942)]).then(__webpack_require__.bind(__webpack_require__, 1561)), { chunkName: 'DatasourceList' }),\r\n    DatasourceDetail: createLazyComponent(() => Promise.all(/* import() */[__webpack_require__.e(96), __webpack_require__.e(876), __webpack_require__.e(977)]).then(__webpack_require__.bind(__webpack_require__, 1358)), { chunkName: 'DatasourceDetail' }),\r\n    DatasourceCreate: createLazyComponent(() => Promise.all(/* import() */[__webpack_require__.e(96), __webpack_require__.e(876), __webpack_require__.e(818)]).then(__webpack_require__.bind(__webpack_require__, 437)), { chunkName: 'DatasourceCreate' }),\r\n    QueryEditor: createLazyComponent(() => Promise.all(/* import() */[__webpack_require__.e(96), __webpack_require__.e(876), __webpack_require__.e(162)]).then(__webpack_require__.bind(__webpack_require__, 3781)), { chunkName: 'QueryEditor' }),\r\n    PluginManagement: createLazyComponent(() => Promise.all(/* import() */[__webpack_require__.e(96), __webpack_require__.e(876), __webpack_require__.e(725)]).then(__webpack_require__.bind(__webpack_require__, 4106)), { chunkName: 'PluginManagement' })\r\n};\r\nconst LazyComponents = {\r\n    DataVisualization: createLazyComponent(() => Promise.all(/* import() */[__webpack_require__.e(96), __webpack_require__.e(364)]).then(__webpack_require__.bind(__webpack_require__, 5364)), { chunkName: 'DataVisualization' }),\r\n    AdvancedQueryEditor: createLazyComponent(() => __webpack_require__.e(/* import() */ 629).then(__webpack_require__.bind(__webpack_require__, 2629)), { chunkName: 'AdvancedQueryEditor' }),\r\n    DataExporter: createLazyComponent(() => Promise.all(/* import() */[__webpack_require__.e(96), __webpack_require__.e(424)]).then(__webpack_require__.bind(__webpack_require__, 5424)), { chunkName: 'DataExporter' })\r\n};\r\nclass PreloadStrategy {\r\n    constructor() {\r\n        this.preloadedChunks = new Set();\r\n        this.preloadQueue = [];\r\n        this.isPreloading = false;\r\n    }\r\n    static getInstance() {\r\n        if (!PreloadStrategy.instance) {\r\n            PreloadStrategy.instance = new PreloadStrategy();\r\n        }\r\n        return PreloadStrategy.instance;\r\n    }\r\n    preloadByUserBehavior(currentRoute) {\r\n        const preloadMap = {\r\n            '/datasources': ['DatasourceDetail', 'DatasourceCreate'],\r\n            '/datasources/create': ['DatasourceList'],\r\n            '/query': ['DataVisualization', 'AdvancedQueryEditor'],\r\n            '/plugins': ['DatasourceCreate']\r\n        };\r\n        const chunksToPreload = preloadMap[currentRoute] || [];\r\n        chunksToPreload.forEach(chunkName => {\r\n            this.addToPreloadQueue(chunkName);\r\n        });\r\n        this.processPreloadQueue();\r\n    }\r\n    preloadByNetworkCondition() {\r\n        if ('connection' in navigator) {\r\n            const connection = navigator.connection;\r\n            if (connection.effectiveType === '4g' && !connection.saveData) {\r\n                this.preloadCriticalChunks();\r\n            }\r\n        }\r\n        else {\r\n            setTimeout(() => {\r\n                this.preloadCriticalChunks();\r\n            }, 2000);\r\n        }\r\n    }\r\n    preloadCriticalChunks() {\r\n        const criticalChunks = [\r\n            'DatasourceDetail',\r\n            'QueryEditor'\r\n        ];\r\n        criticalChunks.forEach(chunkName => {\r\n            this.addToPreloadQueue(chunkName);\r\n        });\r\n        this.processPreloadQueue();\r\n    }\r\n    addToPreloadQueue(chunkName) {\r\n        if (this.preloadedChunks.has(chunkName)) {\r\n            return;\r\n        }\r\n        const importFunc = this.getImportFunction(chunkName);\r\n        if (importFunc) {\r\n            this.preloadQueue.push(importFunc);\r\n        }\r\n    }\r\n    async processPreloadQueue() {\r\n        if (this.isPreloading || this.preloadQueue.length === 0) {\r\n            return;\r\n        }\r\n        this.isPreloading = true;\r\n        while (this.preloadQueue.length > 0) {\r\n            const importFunc = this.preloadQueue.shift();\r\n            if (importFunc) {\r\n                try {\r\n                    await importFunc();\r\n                    console.log('[Preload] Successfully preloaded chunk');\r\n                }\r\n                catch (error) {\r\n                    console.warn('[Preload] Failed to preload chunk:', error);\r\n                }\r\n                await new Promise(resolve => setTimeout(resolve, 100));\r\n            }\r\n        }\r\n        this.isPreloading = false;\r\n    }\r\n    getImportFunction(chunkName) {\r\n        const importMap = {\r\n            DatasourceList: () => Promise.all(/* import() */[__webpack_require__.e(96), __webpack_require__.e(876), __webpack_require__.e(942)]).then(__webpack_require__.bind(__webpack_require__, 1561)),\r\n            DatasourceDetail: () => Promise.all(/* import() */[__webpack_require__.e(96), __webpack_require__.e(876), __webpack_require__.e(977)]).then(__webpack_require__.bind(__webpack_require__, 1358)),\r\n            DatasourceCreate: () => Promise.all(/* import() */[__webpack_require__.e(96), __webpack_require__.e(876), __webpack_require__.e(818)]).then(__webpack_require__.bind(__webpack_require__, 437)),\r\n            QueryEditor: () => Promise.all(/* import() */[__webpack_require__.e(96), __webpack_require__.e(876), __webpack_require__.e(162)]).then(__webpack_require__.bind(__webpack_require__, 3781)),\r\n            PluginManagement: () => Promise.all(/* import() */[__webpack_require__.e(96), __webpack_require__.e(876), __webpack_require__.e(725)]).then(__webpack_require__.bind(__webpack_require__, 4106)),\r\n            DataVisualization: () => Promise.all(/* import() */[__webpack_require__.e(96), __webpack_require__.e(364)]).then(__webpack_require__.bind(__webpack_require__, 5364)),\r\n            AdvancedQueryEditor: () => __webpack_require__.e(/* import() */ 629).then(__webpack_require__.bind(__webpack_require__, 2629)),\r\n            DataExporter: () => Promise.all(/* import() */[__webpack_require__.e(96), __webpack_require__.e(424)]).then(__webpack_require__.bind(__webpack_require__, 5424))\r\n        };\r\n        return importMap[chunkName] || null;\r\n    }\r\n}\r\nfunction initCodeSplitting() {\r\n    const preloadStrategy = PreloadStrategy.getInstance();\r\n    window.addEventListener('popstate', () => {\r\n        const currentRoute = window.location.pathname;\r\n        preloadStrategy.preloadByUserBehavior(currentRoute);\r\n    });\r\n    preloadStrategy.preloadByNetworkCondition();\r\n    console.log('[Code Splitting] Optimization initialized');\r\n}\r\nconst preloadStrategy = PreloadStrategy.getInstance();\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///3043\n\n}")},3128:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval('{/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   kt: () => (/* binding */ LoadingSpinner)\n/* harmony export */ });\n/* unused harmony exports PageLoading, ContentLoading, ButtonLoading, TableLoading */\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(6070);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(212);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(8076);\n/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(antd__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(7107);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(3017);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_4__);\n\r\n\r\n\r\n\r\n\r\nconst rotate = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__.keyframes) `\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n`;\r\nconst pulse = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__.keyframes) `\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.5;\n  }\n`;\r\nconst LoadingContainer = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div) `\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 24px;\n  min-height: ${props => props.$minHeight || \'200px\'};\n  \n  ${props => props.$fullScreen && `\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-color: rgba(255, 255, 255, 0.8);\n    backdrop-filter: blur(4px);\n    z-index: 9999;\n    min-height: 100vh;\n  `}\n`;\r\nconst CustomLoadingIcon = styled_components__WEBPACK_IMPORTED_MODULE_4___default()((0,_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)) `\n  font-size: ${props => props.$size || 24}px !important;\n  color: ${props => props.$color || props.theme.colors.primary} !important;\n  animation: ${rotate} 1s linear infinite;\n`;\r\nconst LoadingText = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div) `\n  margin-top: 12px;\n  color: ${props => props.$color || props.theme.colors.text.secondary};\n  font-size: 14px;\n  animation: ${pulse} 2s ease-in-out infinite;\n`;\r\nconst DotLoader = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div) `\n  display: flex;\n  gap: 4px;\n  \n  .dot {\n    width: 8px;\n    height: 8px;\n    border-radius: 50%;\n    background-color: ${props => props.theme.colors.primary};\n    animation: ${pulse} 1.4s ease-in-out infinite both;\n    \n    &:nth-child(1) { animation-delay: -0.32s; }\n    &:nth-child(2) { animation-delay: -0.16s; }\n    &:nth-child(3) { animation-delay: 0s; }\n  }\n`;\r\nconst SkeletonLoader = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div) `\n  .skeleton-line {\n    height: 16px;\n    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n    background-size: 200% 100%;\n    animation: loading 1.5s infinite;\n    border-radius: 4px;\n    margin-bottom: 8px;\n    \n    &:last-child {\n      margin-bottom: 0;\n    }\n  }\n  \n  @keyframes loading {\n    0% {\n      background-position: 200% 0;\n    }\n    100% {\n      background-position: -200% 0;\n    }\n  }\n`;\r\nconst LoadingSpinner = ({ text = \'加载中...\', fullScreen = false, minHeight, variant = \'spin\', iconSize = 24, iconColor, textColor, skeletonLines = 3, ...spinProps }) => {\r\n    const renderLoader = () => {\r\n        switch (variant) {\r\n            case \'dots\':\r\n                return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(DotLoader, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "dot" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "dot" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "dot" })] }), text && (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(LoadingText, { "$color": textColor, children: text })] }));\r\n            case \'skeleton\':\r\n                return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SkeletonLoader, { children: Array.from({ length: skeletonLines }, (_, index) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "skeleton-line", style: {\r\n                            width: `${Math.random() * 40 + 60}%`\r\n                        } }, index))) }));\r\n            default:\r\n                return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Spin, { indicator: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(CustomLoadingIcon, { "$size": iconSize, "$color": iconColor }), tip: text, ...spinProps }));\r\n        }\r\n    };\r\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(LoadingContainer, { "$fullScreen": fullScreen, "$minHeight": minHeight, children: renderLoader() }));\r\n};\r\nconst PageLoading = ({ text = \'页面加载中...\' }) => (_jsx(LoadingSpinner, { fullScreen: true, text: text, iconSize: 32 }));\r\nconst ContentLoading = ({ text = \'内容加载中...\', minHeight = \'300px\' }) => (_jsx(LoadingSpinner, { text: text, minHeight: minHeight }));\r\nconst ButtonLoading = () => (_jsx(LoadingSpinner, { variant: "spin", iconSize: 14, text: "" }));\r\nconst TableLoading = () => (_jsx(LoadingSpinner, { variant: "skeleton", skeletonLines: 5 }));\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///3128\n\n}')},5520:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval("{/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   j: () => (/* binding */ AppHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(6070);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(212);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(8076);\n/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(antd__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5728);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(2932);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(1265);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(7124);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(6403);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(858);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(1324);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(3017);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _hooks_useTheme__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(5869);\n\r\n\r\n\r\n\r\n\r\n\r\nconst { Header } = antd__WEBPACK_IMPORTED_MODULE_2__.Layout;\r\nconst { Text } = antd__WEBPACK_IMPORTED_MODULE_2__.Typography;\r\nconst StyledHeader = styled_components__WEBPACK_IMPORTED_MODULE_10___default()(Header) `\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 24px;\n  background: ${props => props.$isDark ? '#1f1f1f' : '#ffffff'} !important;\n  border-bottom: 1px solid ${props => props.$isDark ? '#303030' : '#f0f0f0'};\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  position: sticky;\n  top: 0;\n  z-index: 100;\n`;\r\nconst LogoSection = (styled_components__WEBPACK_IMPORTED_MODULE_10___default().div) `\n  display: flex;\n  align-items: center;\n  gap: 12px;\n`;\r\nconst Logo = (styled_components__WEBPACK_IMPORTED_MODULE_10___default().div) `\n  width: 32px;\n  height: 32px;\n  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-weight: bold;\n  font-size: 16px;\n`;\r\nconst AppTitle = (styled_components__WEBPACK_IMPORTED_MODULE_10___default().div) `\n  .title {\n    font-size: 18px;\n    font-weight: 600;\n    color: ${props => props.$isDark ? '#ffffff' : '#262626'};\n    margin: 0;\n  }\n  \n  .subtitle {\n    font-size: 12px;\n    color: ${props => props.$isDark ? '#d9d9d9' : '#8c8c8c'};\n    margin: 0;\n  }\n`;\r\nconst ActionSection = (styled_components__WEBPACK_IMPORTED_MODULE_10___default().div) `\n  display: flex;\n  align-items: center;\n  gap: 8px;\n`;\r\nconst ThemeToggle = styled_components__WEBPACK_IMPORTED_MODULE_10___default()((0,antd__WEBPACK_IMPORTED_MODULE_2__.Button)) `\n  border: none;\n  background: transparent;\n  color: ${props => props.$isDark ? '#d9d9d9' : '#595959'};\n  \n  &:hover {\n    background: ${props => props.$isDark ? '#262626' : '#f5f5f5'};\n    color: ${props => props.$isDark ? '#ffffff' : '#262626'};\n  }\n`;\r\nconst NotificationButton = styled_components__WEBPACK_IMPORTED_MODULE_10___default()((0,antd__WEBPACK_IMPORTED_MODULE_2__.Button)) `\n  border: none;\n  background: transparent;\n  color: ${props => props.$isDark ? '#d9d9d9' : '#595959'};\n  \n  &:hover {\n    background: ${props => props.$isDark ? '#262626' : '#f5f5f5'};\n    color: ${props => props.$isDark ? '#ffffff' : '#262626'};\n  }\n`;\r\nconst UserAvatar = styled_components__WEBPACK_IMPORTED_MODULE_10___default()((0,antd__WEBPACK_IMPORTED_MODULE_2__.Avatar)) `\n  background-color: ${props => props.$isDark ? '#434343' : '#f0f0f0'};\n  color: ${props => props.$isDark ? '#ffffff' : '#262626'};\n  cursor: pointer;\n  \n  &:hover {\n    background-color: ${props => props.$isDark ? '#595959' : '#d9d9d9'};\n  }\n`;\r\nconst AppHeader = () => {\r\n    const { isDark, toggleTheme } = (0,_hooks_useTheme__WEBPACK_IMPORTED_MODULE_11__/* .useTheme */ .D)();\r\n    const userMenuItems = [\r\n        {\r\n            key: 'profile',\r\n            icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* [\"default\"] */ .A, {}),\r\n            label: '个人资料'\r\n        },\r\n        {\r\n            key: 'settings',\r\n            icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* [\"default\"] */ .A, {}),\r\n            label: '设置'\r\n        },\r\n        {\r\n            type: 'divider'\r\n        },\r\n        {\r\n            key: 'help',\r\n            icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* [\"default\"] */ .A, {}),\r\n            label: '帮助文档'\r\n        },\r\n        {\r\n            type: 'divider'\r\n        },\r\n        {\r\n            key: 'logout',\r\n            icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* [\"default\"] */ .A, {}),\r\n            label: '退出登录',\r\n            danger: true\r\n        }\r\n    ];\r\n    const handleUserMenuClick = ({ key }) => {\r\n        switch (key) {\r\n            case 'profile':\r\n                console.log('打开个人资料');\r\n                break;\r\n            case 'settings':\r\n                console.log('打开设置');\r\n                break;\r\n            case 'help':\r\n                window.open('https://docs.example.com', '_blank');\r\n                break;\r\n            case 'logout':\r\n                console.log('退出登录');\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n    };\r\n    const handleNotificationClick = () => {\r\n        console.log('打开通知');\r\n    };\r\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(StyledHeader, { \"$isDark\": isDark, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(LogoSection, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Logo, { \"$isDark\": isDark, children: \"DS\" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(AppTitle, { \"$isDark\": isDark, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { className: \"title\", children: \"\\u6570\\u636E\\u6E90\\u7BA1\\u7406\" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { className: \"subtitle\", children: \"PagePlug DataSource\" })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(ActionSection, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Space, { size: \"small\", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(ThemeToggle, { \"$isDark\": isDark, type: \"text\", icon: isDark ? (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* [\"default\"] */ .A, {}) : (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* [\"default\"] */ .A, {}), onClick: toggleTheme, title: isDark ? '切换到亮色主题' : '切换到暗色主题' }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Badge, { count: 3, size: \"small\", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(NotificationButton, { \"$isDark\": isDark, type: \"text\", icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* [\"default\"] */ .A, {}), onClick: handleNotificationClick, title: \"\\u901A\\u77E5\" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Dropdown, { menu: {\r\n                                items: userMenuItems,\r\n                                onClick: handleUserMenuClick\r\n                            }, placement: \"bottomRight\", trigger: ['click'], children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(UserAvatar, { \"$isDark\": isDark, size: \"small\", icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* [\"default\"] */ .A, {}) }) })] }) })] }));\r\n};\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///5520\n\n}")},5642:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval("{/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   H3: () => (/* binding */ initPerformanceMonitoring),\n/* harmony export */   Y5: () => (/* binding */ performanceCollector)\n/* harmony export */ });\n/* unused harmony exports PerformanceCollector, withPerformanceMonitoring, debounce, throttle, MemoryMonitor, lazyWithRetry, memoryMonitor */\nclass PerformanceCollector {\r\n    static getInstance() {\r\n        if (!PerformanceCollector.instance) {\r\n            PerformanceCollector.instance = new PerformanceCollector();\r\n        }\r\n        return PerformanceCollector.instance;\r\n    }\r\n    constructor() {\r\n        this.metrics = new Map();\r\n        this.observers = [];\r\n        this.initObservers();\r\n    }\r\n    initObservers() {\r\n        if ('PerformanceObserver' in window) {\r\n            try {\r\n                const navigationObserver = new PerformanceObserver((list) => {\r\n                    const entries = list.getEntries();\r\n                    entries.forEach((entry) => {\r\n                        if (entry.entryType === 'navigation') {\r\n                            const navEntry = entry;\r\n                            this.recordMetric('navigation.domContentLoaded', navEntry.domContentLoadedEventEnd - navEntry.domContentLoadedEventStart);\r\n                            this.recordMetric('navigation.loadComplete', navEntry.loadEventEnd - navEntry.loadEventStart);\r\n                            this.recordMetric('navigation.firstPaint', navEntry.responseEnd - navEntry.requestStart);\r\n                        }\r\n                    });\r\n                });\r\n                navigationObserver.observe({ entryTypes: ['navigation'] });\r\n                this.observers.push(navigationObserver);\r\n            }\r\n            catch (error) {\r\n                console.warn('Navigation observer not supported:', error);\r\n            }\r\n            try {\r\n                const resourceObserver = new PerformanceObserver((list) => {\r\n                    const entries = list.getEntries();\r\n                    entries.forEach((entry) => {\r\n                        if (entry.entryType === 'resource') {\r\n                            const resourceEntry = entry;\r\n                            const duration = resourceEntry.responseEnd - resourceEntry.requestStart;\r\n                            if (resourceEntry.name.includes('.js')) {\r\n                                this.recordMetric('resource.js.loadTime', duration);\r\n                            }\r\n                            else if (resourceEntry.name.includes('.css')) {\r\n                                this.recordMetric('resource.css.loadTime', duration);\r\n                            }\r\n                        }\r\n                    });\r\n                });\r\n                resourceObserver.observe({ entryTypes: ['resource'] });\r\n                this.observers.push(resourceObserver);\r\n            }\r\n            catch (error) {\r\n                console.warn('Resource observer not supported:', error);\r\n            }\r\n            try {\r\n                const measureObserver = new PerformanceObserver((list) => {\r\n                    const entries = list.getEntries();\r\n                    entries.forEach((entry) => {\r\n                        if (entry.entryType === 'measure') {\r\n                            this.recordMetric(`custom.${entry.name}`, entry.duration);\r\n                        }\r\n                    });\r\n                });\r\n                measureObserver.observe({ entryTypes: ['measure'] });\r\n                this.observers.push(measureObserver);\r\n            }\r\n            catch (error) {\r\n                console.warn('Measure observer not supported:', error);\r\n            }\r\n        }\r\n    }\r\n    recordMetric(name, value) {\r\n        this.metrics.set(name, value);\r\n        if (true) {\r\n            console.log(`[Performance] ${name}: ${value.toFixed(2)}ms`);\r\n        }\r\n    }\r\n    getMetric(name) {\r\n        return this.metrics.get(name);\r\n    }\r\n    getAllMetrics() {\r\n        return Object.fromEntries(this.metrics);\r\n    }\r\n    measureFunction(name, fn) {\r\n        const start = performance.now();\r\n        const result = fn();\r\n        const end = performance.now();\r\n        this.recordMetric(`function.${name}`, end - start);\r\n        return result;\r\n    }\r\n    async measureAsyncFunction(name, fn) {\r\n        const start = performance.now();\r\n        const result = await fn();\r\n        const end = performance.now();\r\n        this.recordMetric(`async.${name}`, end - start);\r\n        return result;\r\n    }\r\n    cleanup() {\r\n        this.observers.forEach(observer => observer.disconnect());\r\n        this.observers = [];\r\n        this.metrics.clear();\r\n    }\r\n}\r\nfunction withPerformanceMonitoring(WrappedComponent, componentName) {\r\n    return function PerformanceMonitoredComponent(props) {\r\n        const collector = PerformanceCollector.getInstance();\r\n        React.useEffect(() => {\r\n            const mountStart = performance.now();\r\n            return () => {\r\n                const mountEnd = performance.now();\r\n                collector.recordMetric(`component.${componentName}.mountTime`, mountEnd - mountStart);\r\n            };\r\n        }, []);\r\n        return React.createElement(WrappedComponent, props);\r\n    };\r\n}\r\nfunction debounce(func, wait, immediate = false) {\r\n    let timeout = null;\r\n    return function executedFunction(...args) {\r\n        const later = () => {\r\n            timeout = null;\r\n            if (!immediate)\r\n                func(...args);\r\n        };\r\n        const callNow = immediate && !timeout;\r\n        if (timeout)\r\n            clearTimeout(timeout);\r\n        timeout = setTimeout(later, wait);\r\n        if (callNow)\r\n            func(...args);\r\n    };\r\n}\r\nfunction throttle(func, limit) {\r\n    let inThrottle;\r\n    return function executedFunction(...args) {\r\n        if (!inThrottle) {\r\n            func(...args);\r\n            inThrottle = true;\r\n            setTimeout(() => inThrottle = false, limit);\r\n        }\r\n    };\r\n}\r\nclass MemoryMonitor {\r\n    constructor() {\r\n        this.intervalId = null;\r\n    }\r\n    static getInstance() {\r\n        if (!MemoryMonitor.instance) {\r\n            MemoryMonitor.instance = new MemoryMonitor();\r\n        }\r\n        return MemoryMonitor.instance;\r\n    }\r\n    startMonitoring(interval = 30000) {\r\n        if (this.intervalId) {\r\n            clearInterval(this.intervalId);\r\n        }\r\n        this.intervalId = setInterval(() => {\r\n            this.checkMemoryUsage();\r\n        }, interval);\r\n    }\r\n    stopMonitoring() {\r\n        if (this.intervalId) {\r\n            clearInterval(this.intervalId);\r\n            this.intervalId = null;\r\n        }\r\n    }\r\n    checkMemoryUsage() {\r\n        if ('memory' in performance) {\r\n            const memory = performance.memory;\r\n            const collector = PerformanceCollector.getInstance();\r\n            collector.recordMetric('memory.usedJSHeapSize', memory.usedJSHeapSize / 1024 / 1024);\r\n            collector.recordMetric('memory.totalJSHeapSize', memory.totalJSHeapSize / 1024 / 1024);\r\n            collector.recordMetric('memory.jsHeapSizeLimit', memory.jsHeapSizeLimit / 1024 / 1024);\r\n            const usageRatio = memory.usedJSHeapSize / memory.jsHeapSizeLimit;\r\n            if (usageRatio > 0.8) {\r\n                console.warn(`[Memory Warning] Memory usage is ${(usageRatio * 100).toFixed(1)}%`);\r\n            }\r\n        }\r\n    }\r\n}\r\nconst lazyWithRetry = (importFunc, retries = 3) => {\r\n    return React.lazy(async () => {\r\n        let attempt = 0;\r\n        while (attempt < retries) {\r\n            try {\r\n                return await importFunc();\r\n            }\r\n            catch (error) {\r\n                attempt++;\r\n                console.warn(`[Lazy Load] Attempt ${attempt} failed:`, error);\r\n                if (attempt >= retries) {\r\n                    throw error;\r\n                }\r\n                await new Promise(resolve => setTimeout(resolve, 1000 * attempt));\r\n            }\r\n        }\r\n        throw new Error('Max retries reached');\r\n    });\r\n};\r\nfunction initPerformanceMonitoring() {\r\n    const collector = PerformanceCollector.getInstance();\r\n    const memoryMonitor = MemoryMonitor.getInstance();\r\n    memoryMonitor.startMonitoring();\r\n    document.addEventListener('visibilitychange', () => {\r\n        if (document.visibilityState === 'visible') {\r\n            collector.recordMetric('page.becameVisible', performance.now());\r\n        }\r\n        else {\r\n            collector.recordMetric('page.becameHidden', performance.now());\r\n        }\r\n    });\r\n    window.addEventListener('beforeunload', () => {\r\n        collector.cleanup();\r\n        memoryMonitor.stopMonitoring();\r\n    });\r\n    console.log('[Performance] Monitoring initialized');\r\n}\r\nconst performanceCollector = PerformanceCollector.getInstance();\r\nconst memoryMonitor = MemoryMonitor.getInstance();\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///5642\n\n}")},5869:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval("{/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   D: () => (/* reexport safe */ _providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_0__.D)\n/* harmony export */ });\n/* harmony import */ var _providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(241);\n\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTg2OS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQ3NEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGFnZXBsdWctZGF0YXNvdXJjZS1mcm9udGVuZC8uL3NyYy9ob29rcy91c2VUaGVtZS50cz8zZTk1Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIOmHjeaWsOWvvOWHulRoZW1lUHJvdmlkZXLkuK3nmoR1c2VUaGVtZSBob29rXG5leHBvcnQgeyB1c2VUaGVtZSB9IGZyb20gJy4uL3Byb3ZpZGVycy9UaGVtZVByb3ZpZGVyJztcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///5869\n\n}")}}]);