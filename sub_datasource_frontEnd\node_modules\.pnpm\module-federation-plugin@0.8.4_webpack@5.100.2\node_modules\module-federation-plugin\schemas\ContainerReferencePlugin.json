{"definitions": {"ExternalsType": {"description": "Specifies the default type of externals.", "enum": ["this", "window", "self", "global", "script"]}, "Remotes": {"description": "Container locations from which modules should be resolved and loaded at runtime. Property names are used as request scopes.", "type": "object", "additionalProperties": {"description": "Container location from which modules should be resolved and loaded at runtime.", "anyOf": [{"$ref": "#/definitions/RemotesConfig"}, {"$ref": "#/definitions/RemotesItem"}]}}, "RemotesConfig": {"description": "Advanced configuration for container locations from which modules should be resolved and loaded at runtime.", "type": "object", "additionalProperties": false, "properties": {"external": {"$ref": "#/definitions/RemotesItem"}}, "required": ["external"]}, "RemotesItem": {"description": "Container location from which modules should be resolved and loaded at runtime.", "type": "string", "minLength": 1}, "AdditionalFeatures": {"description": "Features in addition to the module federation", "type": "object", "properties": {"asyncChunkMode": {"description": "Default mode for dynamic imports of remote modules.", "type": "string"}, "keepAsync": {"description": "Whether to disable replacing the module with a synchronized one after loaded.", "type": "boolean"}}}}, "title": "ContainerReferencePluginOptions", "type": "object", "additionalProperties": false, "properties": {"remoteType": {"description": "The external type of the remote containers.", "oneOf": [{"$ref": "#/definitions/ExternalsType"}]}, "remotes": {"$ref": "#/definitions/Remotes"}, "additionalFeatures": {"$ref": "#/definitions/AdditionalFeatures"}}, "required": ["remoteType", "remotes"]}