---
name: Label Checker
on:
  pull_request:
    branches: [release]
    types:
      - opened
      - synchronize
      - reopened
      - labeled
      - unlabeled
    
jobs:

  check_labels:
    name: Check for Test Plan Approved label
    runs-on: ubuntu-latest
    steps:
      - uses: docker://agilepathway/pull-request-label-checker:latest
        with:
          # Needs to have a Test Plan Approved label if not part of any of the pods below
          any_of: Test Plan Approved,User Education Pod,DevOps Pod,Performance,CI,Release Promotion,skip-testPlan
          repo_token: ${{ secrets.GITHUB_TOKEN }}
