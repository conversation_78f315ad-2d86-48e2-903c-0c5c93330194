name: Ad-hoc DP | Build Push Deploy from branch ( To be used by ops.appsmith.com API layer only)

on:
  # This workflow is only triggered by the `/build-deploy-preview` command dispatch
  workflow_dispatch:
    inputs:
      branch:
        description: "Github Branch to be deployed"
        required: true
        default: "release"
      skip-tests:
        description: "Flag to skip Cypress tests"
        required: true
        default: "true"
      id:
        description: "Document ID for the workflow entry in the MongoDB ( This gets initialized by the ops api )"
        required: true
      sub-domain-name:
        description: "Sub-domain for dp.appsmith.com to by used by this deploy preview (This will also be the image name and the k8s namespace identifier)"
        required: true
jobs:
  update-job-details-to-db:
    runs-on: ubuntu-latest
    steps:
      - name: Update workflow details
        run: |
          curl --request PATCH 'https://ops.appsmith.com/appsmith_deploy_workflow/${{github.event.inputs.id}}' \
          --header 'Content-Type: application/json' \
          --oauth2-bearer '${{secrets.INTERNAL_OPS_API_TOKEN}}' \
          --data ' {
                  "status": "IN_PROGRESS",
                  "githubWorkflowId": "${{ github.run_id }}"
          }'

  server-build:
    name: server-build
    uses: ./.github/workflows/server-build.yml
    secrets: inherit
    with:
      branch: ${{ github.event.inputs.branch }}
      skip-tests: ${{ github.event.inputs.skip-tests }}

  client-build:
    name: client-build
    uses: ./.github/workflows/client-build.yml
    secrets: inherit
    with:
      branch: ${{ github.event.inputs.branch }}
      skip-tests: ${{ github.event.inputs.skip-tests }}

  rts-build:
    name: rts-build
    uses: ./.github/workflows/rts-build.yml
    secrets: inherit
    with:
      branch: ${{ github.event.inputs.branch }}

  push-image:
    needs: [client-build, rts-build, server-build]
    runs-on: ubuntu-latest
    if: success()
    steps:
      # Check out merge commit
      - name: Checkout Branch
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch }}

      # Timestamp will be used to create cache key
      - id: timestamp
        run: echo "timestamp=$(date +'%Y-%m-%dT%H:%M:%S')" >> $GITHUB_OUTPUT

      # get Git-hash will be used to create cache key
      - id: git_hash
        run: echo "git_hash=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

      - name: Download the client build artifact
        uses: actions/download-artifact@v3
        with:
          name: client-build
          path: app/client

      - name: Unpack the client build artifact
        if: steps.run_result.outputs.run_result != 'success'
        run: |
          mkdir -p app/client/build
          tar -xvf app/client/build.tar -C app/client/build

      - name: Download the server build artifact
        uses: actions/download-artifact@v3
        with:
          name: server-build
          path: app/server/dist

      - name: Download the rts build artifact
        uses: actions/download-artifact@v3
        with:
          name: rts-dist
          path: app/client/packages/rts/dist

      - name: Untar the rts folder
        run: |
          tar -xvf app/client/packages/rts/dist/rts-dist.tar -C app/client/packages/rts/
          echo "Cleaning up the tar files"
          rm app/client/packages/rts/dist/rts-dist.tar

      - name: Generate info.json
        run: |
          if [[ -f scripts/generate_info_json.sh ]]; then
            scripts/generate_info_json.sh
          fi

      - name: Login to DockerHub
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_ACCESS_TOKEN }}

      - name: Push to Docker Hub
        uses: docker/build-push-action@v4
        with:
          context: .
          pull: true
          push: true
          cache-from: ${{ vars.DOCKER_HUB_ORGANIZATION }}/appsmith-${{ vars.EDITION }}:release
          tags: |
            ${{ vars.DOCKER_HUB_ORGANIZATION }}/appsmith-dp:${{ github.event.inputs.sub-domain-name }}
          build-args: |
            APPSMITH_CLOUD_SERVICES_BASE_URL=https://release-cs.appsmith.com
            BASE=${{ vars.DOCKER_HUB_ORGANIZATION }}/base-${{ vars.EDITION }}:release

    outputs:
      imageHash: ${{ github.event.inputs.sub-domain-name }}

  build-deploy-preview:
    needs: [push-image]
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: "."

    if: success()
    steps:
      - name: Checkout PR
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch }}

      - name: Install relevant packages
        run: |
          which aws
          sudo apt update -q && sudo apt install -y curl unzip less jq
          curl -LO https://storage.googleapis.com/kubernetes-release/release/v1.23.6/bin/linux/amd64/kubectl && \
          sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl && \
          curl -fsSL -o get_helm.sh https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 && \
          chmod 700 get_helm.sh; ./get_helm.sh

      - name: Deploy Helm chart
        env:
          AWS_ROLE_ARN: ${{ secrets.APPSMITH_EKS_AWS_ROLE_ARN }}
          AWS_ACCESS_KEY_ID: ${{ secrets.APPSMITH_CI_AWS_SECRET_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.APPSMITH_CI_AWS_SECRET_ACCESS_KEY }}
          IMAGE_HASH: ${{ needs.push-image.outputs.imageHash }}
          AWS_RELEASE_CERT: ${{ secrets.APPSMITH_AWS_RELEASE_CERT_RELEASE }}
          DOCKER_HUB_ORGANIZATION: ${{ vars.DOCKER_HUB_ORGANIZATION }}
          DOCKER_HUB_USERNAME: ${{ secrets.DOCKER_HUB_USERNAME }}
          DOCKER_HUB_ACCESS_TOKEN: ${{ secrets.DOCKER_HUB_ACCESS_TOKEN }}
          SUB_DOMAIN_NAME: ${{ github.event.inputs.sub-domain-name }}
          # RECREATE: ${{ github.event.client_payload.slash_command.args.named.recreate }}
          DB_USERNAME: ${{ secrets.DB_USERNAME }}
          DB_PASSWORD: ${{ secrets.DB_PASSWORD }}
          DB_URL: ${{ secrets.DB_URL }}
        run: |
          echo "environment variables set to deploy the image $IMAGE_HASH"
          /bin/bash ./scripts/build_dp_from_branch.sh

  update-db-on-completion:
    needs: [build-deploy-preview]
    runs-on: ubuntu-latest
    steps:
      - name: Update workflow completed successfully
        if: success()
        run: |
          curl --request PATCH 'https://ops.appsmith.com/appsmith_deploy_workflow/${{github.event.inputs.id}}' \
          --header 'Content-Type: application/json' \
          --oauth2-bearer '${{secrets.INTERNAL_OPS_API_TOKEN}}'  \
          --data ' {
                  "status": "COMPLETED"
          }'
      - name: Update workflow failure
        if: failure()
        run: |
          curl --request PATCH 'https://ops.appsmith.com/appsmith_deploy_workflow/${{github.event.inputs.id}}' \
          --header 'Content-Type: application/json' \
          --oauth2-bearer '${{secrets.INTERNAL_OPS_API_TOKEN}}'  \
          --data ' {
                  "status": "FAILED"
          }'
