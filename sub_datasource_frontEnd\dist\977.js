"use strict";(self.webpackChunkpageplug_datasource_frontend=self.webpackChunkpageplug_datasource_frontend||[]).push([[977],{1358:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval('{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(6070);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(212);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(8076);\n/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(antd__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(3504);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(2691);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(5469);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(3545);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(246);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(6025);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(1957);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(5941);\n/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(6706);\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(1299);\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(react_router_dom__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(3017);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(6287);\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var _services_apiClient__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(8876);\n/* harmony import */ var _components_Common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(3128);\n/* harmony import */ var _hooks_useTheme__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(5869);\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nconst { TabPane } = antd__WEBPACK_IMPORTED_MODULE_2__.Tabs;\r\nconst { Text, Paragraph } = antd__WEBPACK_IMPORTED_MODULE_2__.Typography;\r\nconst { confirm } = antd__WEBPACK_IMPORTED_MODULE_2__.Modal;\r\nconst PageContainer = (styled_components__WEBPACK_IMPORTED_MODULE_13___default().div) `\n  padding: 24px;\n`;\r\nconst PageHeader = (styled_components__WEBPACK_IMPORTED_MODULE_13___default().div) `\n  margin-bottom: 24px;\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n`;\r\nconst TitleSection = (styled_components__WEBPACK_IMPORTED_MODULE_13___default().div) ``;\r\nconst PageTitle = (styled_components__WEBPACK_IMPORTED_MODULE_13___default().h1) `\n  font-size: 24px;\n  font-weight: 600;\n  color: ${props => props.$isDark ? \'#ffffff\' : \'#262626\'};\n  margin: 0 0 8px 0;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n`;\r\nconst StatusIndicator = (styled_components__WEBPACK_IMPORTED_MODULE_13___default().div) `\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 4px 12px;\n  border-radius: 16px;\n  font-size: 14px;\n  font-weight: 500;\n  \n  ${props => {\r\n    switch (props.$status) {\r\n        case \'valid\':\r\n            return `\n          background-color: #f6ffed;\n          border: 1px solid #b7eb8f;\n          color: #52c41a;\n        `;\r\n        case \'invalid\':\r\n            return `\n          background-color: #fff2f0;\n          border: 1px solid #ffccc7;\n          color: #ff4d4f;\n        `;\r\n        default:\r\n            return `\n          background-color: #fafafa;\n          border: 1px solid #d9d9d9;\n          color: #8c8c8c;\n        `;\r\n    }\r\n}}\n`;\r\nconst ActionSection = (styled_components__WEBPACK_IMPORTED_MODULE_13___default().div) ``;\r\nconst TabContent = (styled_components__WEBPACK_IMPORTED_MODULE_13___default().div) `\n  padding: 16px 0;\n`;\r\nconst DatasourceDetail = () => {\r\n    const { id } = (0,react_router_dom__WEBPACK_IMPORTED_MODULE_12__.useParams)();\r\n    const navigate = (0,react_router_dom__WEBPACK_IMPORTED_MODULE_12__.useNavigate)();\r\n    const { isDark } = (0,_hooks_useTheme__WEBPACK_IMPORTED_MODULE_17__/* .useTheme */ .D)();\r\n    const [datasource, setDatasource] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\r\n    const [structure, setStructure] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\r\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\r\n    const [structureLoading, setStructureLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\r\n    const [testResult, setTestResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\r\n    const [testing, setTesting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\r\n    const loadDatasource = async () => {\r\n        if (!id)\r\n            return;\r\n        try {\r\n            setLoading(true);\r\n            const response = await _services_apiClient__WEBPACK_IMPORTED_MODULE_15__/* .apiClient */ .u.get(`/datasources/${id}`);\r\n            setDatasource(response.data);\r\n        }\r\n        catch (error) {\r\n            antd__WEBPACK_IMPORTED_MODULE_2__.message.error(\'加载数据源详情失败\');\r\n            console.error(\'Load datasource error:\', error);\r\n        }\r\n        finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n    const loadStructure = async () => {\r\n        if (!id)\r\n            return;\r\n        try {\r\n            setStructureLoading(true);\r\n            const response = await _services_apiClient__WEBPACK_IMPORTED_MODULE_15__/* .apiClient */ .u.get(`/datasources/${id}/structure`);\r\n            setStructure(response.data);\r\n        }\r\n        catch (error) {\r\n            antd__WEBPACK_IMPORTED_MODULE_2__.message.error(\'加载数据源结构失败\');\r\n            console.error(\'Load structure error:\', error);\r\n        }\r\n        finally {\r\n            setStructureLoading(false);\r\n        }\r\n    };\r\n    const handleTestConnection = async () => {\r\n        if (!id)\r\n            return;\r\n        try {\r\n            setTesting(true);\r\n            const response = await _services_apiClient__WEBPACK_IMPORTED_MODULE_15__/* .apiClient */ .u.post(`/datasources/${id}/test`);\r\n            setTestResult(response.data);\r\n            if (response.data.success) {\r\n                antd__WEBPACK_IMPORTED_MODULE_2__.message.success(`连接成功 (${response.data.responseTime}ms)`);\r\n            }\r\n            else {\r\n                antd__WEBPACK_IMPORTED_MODULE_2__.message.error(`连接失败: ${response.data.message}`);\r\n            }\r\n        }\r\n        catch (error) {\r\n            antd__WEBPACK_IMPORTED_MODULE_2__.message.error(\'连接测试失败\');\r\n            setTestResult({\r\n                success: false,\r\n                message: \'连接测试失败\',\r\n                responseTime: 0\r\n            });\r\n        }\r\n        finally {\r\n            setTesting(false);\r\n        }\r\n    };\r\n    const handleDelete = () => {\r\n        if (!datasource)\r\n            return;\r\n        confirm({\r\n            title: \'确认删除\',\r\n            content: `确定要删除数据源 "${datasource.name}" 吗？此操作不可恢复。`,\r\n            okText: \'删除\',\r\n            okType: \'danger\',\r\n            cancelText: \'取消\',\r\n            onOk: async () => {\r\n                try {\r\n                    await _services_apiClient__WEBPACK_IMPORTED_MODULE_15__/* .apiClient */ .u.delete(`/datasources/${id}`);\r\n                    antd__WEBPACK_IMPORTED_MODULE_2__.message.success(\'数据源删除成功\');\r\n                    navigate(\'/datasources\');\r\n                }\r\n                catch (error) {\r\n                    antd__WEBPACK_IMPORTED_MODULE_2__.message.error(\'删除数据源失败\');\r\n                }\r\n            }\r\n        });\r\n    };\r\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\r\n        loadDatasource();\r\n    }, [id]);\r\n    if (loading) {\r\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_components_Common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_16__/* .LoadingSpinner */ .kt, { text: "\\u52A0\\u8F7D\\u6570\\u636E\\u6E90\\u8BE6\\u60C5\\u4E2D..." });\r\n    }\r\n    if (!datasource) {\r\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(PageContainer, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Alert, { message: "\\u6570\\u636E\\u6E90\\u4E0D\\u5B58\\u5728", description: "\\u8BF7\\u68C0\\u67E5\\u6570\\u636E\\u6E90ID\\u662F\\u5426\\u6B63\\u786E", type: "error", showIcon: true }) }));\r\n    }\r\n    const buildTreeData = () => {\r\n        if (!structure)\r\n            return [];\r\n        return structure.tables.map(table => ({\r\n            title: ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Space, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .A, {}), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: table.name }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Tag, { size: "small", children: table.type })] })),\r\n            key: table.name,\r\n            children: table.columns.map(column => ({\r\n                title: ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Space, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: column.name }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Tag, { size: "small", color: "blue", children: column.type }), column.isPrimaryKey && (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Tag, { size: "small", color: "gold", children: "PK" }), column.isAutoIncrement && (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Tag, { size: "small", color: "green", children: "AI" }), !column.isNullable && (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Tag, { size: "small", color: "red", children: "NOT NULL" })] })),\r\n                key: `${table.name}.${column.name}`,\r\n                isLeaf: true\r\n            }))\r\n        }));\r\n    };\r\n    const structureColumns = [\r\n        {\r\n            title: \'列名\',\r\n            dataIndex: \'name\',\r\n            key: \'name\'\r\n        },\r\n        {\r\n            title: \'类型\',\r\n            dataIndex: \'type\',\r\n            key: \'type\',\r\n            render: (text) => (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Tag, { color: "blue", children: text })\r\n        },\r\n        {\r\n            title: \'属性\',\r\n            key: \'attributes\',\r\n            render: (_, record) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Space, { children: [record.isPrimaryKey && (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Tag, { color: "gold", children: "\\u4E3B\\u952E" }), record.isAutoIncrement && (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Tag, { color: "green", children: "\\u81EA\\u589E" }), !record.isNullable && (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Tag, { color: "red", children: "\\u975E\\u7A7A" })] }))\r\n        },\r\n        {\r\n            title: \'默认值\',\r\n            dataIndex: \'defaultValue\',\r\n            key: \'defaultValue\',\r\n            render: (text) => text || \'-\'\r\n        }\r\n    ];\r\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(PageContainer, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(PageHeader, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(TitleSection, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(PageTitle, { "$isDark": isDark, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A, {}), datasource.name, (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(StatusIndicator, { "$status": datasource.isConfigured\r\n                                            ? datasource.isValid\r\n                                                ? \'valid\'\r\n                                                : \'invalid\'\r\n                                            : \'unknown\', children: [datasource.isConfigured\r\n                                                ? datasource.isValid\r\n                                                    ? (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A, {})\r\n                                                    : (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A, {})\r\n                                                : (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A, {}), datasource.isConfigured\r\n                                                ? datasource.isValid\r\n                                                    ? \'连接正常\'\r\n                                                    : \'连接异常\'\r\n                                                : \'未配置\'] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(Text, { type: "secondary", children: [datasource.pluginName, " \\u2022 \\u521B\\u5EFA\\u4E8E ", dayjs__WEBPACK_IMPORTED_MODULE_14___default()(datasource.createdAt).format(\'YYYY-MM-DD HH:mm\')] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(ActionSection, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Space, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Button, { type: "primary", icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A, {}), loading: testing, onClick: handleTestConnection, children: "\\u6D4B\\u8BD5\\u8FDE\\u63A5" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Button, { icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .A, {}), onClick: () => navigate(`/datasources/${id}/edit`), children: "\\u7F16\\u8F91" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Button, { icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A, {}), danger: true, onClick: handleDelete, children: "\\u5220\\u9664" })] }) })] }), testResult && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Alert, { message: testResult.success ? \'连接测试成功\' : \'连接测试失败\', description: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { children: testResult.message }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: ["\\u54CD\\u5E94\\u65F6\\u95F4: ", testResult.responseTime, "ms"] }), testResult.details && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: ["\\u8BE6\\u7EC6\\u4FE1\\u606F: ", JSON.stringify(testResult.details, null, 2)] }))] }), type: testResult.success ? \'success\' : \'error\', showIcon: true, closable: true, style: { marginBottom: 24 } })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Tabs, { defaultActiveKey: "basic", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(TabPane, { tab: "\\u57FA\\u672C\\u4FE1\\u606F", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(TabContent, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Card, { title: "\\u6570\\u636E\\u6E90\\u914D\\u7F6E", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Descriptions, { column: 2, bordered: true, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Descriptions.Item, { label: "\\u6570\\u636E\\u6E90\\u540D\\u79F0", children: datasource.name }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Descriptions.Item, { label: "\\u63D2\\u4EF6\\u7C7B\\u578B", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Tag, { color: "blue", children: datasource.pluginName }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Descriptions.Item, { label: "\\u8FDE\\u63A5\\u5730\\u5740", children: datasource.datasourceConfiguration.url }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Descriptions.Item, { label: "\\u6570\\u636E\\u5E93\\u540D", children: datasource.datasourceConfiguration.databaseName || \'-\' }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Descriptions.Item, { label: "\\u8BA4\\u8BC1\\u65B9\\u5F0F", children: datasource.datasourceConfiguration.authentication?.authType || \'-\' }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Descriptions.Item, { label: "\\u7528\\u6237\\u540D", children: datasource.datasourceConfiguration.authentication?.username || \'-\' }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Descriptions.Item, { label: "SSL\\u8FDE\\u63A5", children: datasource.datasourceConfiguration.connection?.ssl?.enabled ? \'启用\' : \'禁用\' }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Descriptions.Item, { label: "\\u8FDE\\u63A5\\u8D85\\u65F6", children: [datasource.datasourceConfiguration.connection?.timeout || \'-\', "ms"] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Descriptions.Item, { label: "\\u521B\\u5EFA\\u65F6\\u95F4", children: dayjs__WEBPACK_IMPORTED_MODULE_14___default()(datasource.createdAt).format(\'YYYY-MM-DD HH:mm:ss\') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Descriptions.Item, { label: "\\u66F4\\u65B0\\u65F6\\u95F4", children: dayjs__WEBPACK_IMPORTED_MODULE_14___default()(datasource.updatedAt).format(\'YYYY-MM-DD HH:mm:ss\') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Descriptions.Item, { label: "\\u6700\\u540E\\u4F7F\\u7528", children: datasource.lastUsed\r\n                                                ? dayjs__WEBPACK_IMPORTED_MODULE_14___default()(datasource.lastUsed).format(\'YYYY-MM-DD HH:mm:ss\')\r\n                                                : \'从未使用\' })] }) }) }) }, "basic"), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(TabPane, { tab: "\\u6570\\u636E\\u7ED3\\u6784", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(TabContent, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Card, { title: "\\u6570\\u636E\\u5E93\\u7ED3\\u6784", extra: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Button, { icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .A, {}), loading: structureLoading, onClick: loadStructure, children: "\\u5237\\u65B0\\u7ED3\\u6784" }), children: !structure ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { style: { textAlign: \'center\', padding: \'40px 0\' }, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Button, { type: "primary", icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .A, {}), loading: structureLoading, onClick: loadStructure, children: "\\u52A0\\u8F7D\\u6570\\u636E\\u7ED3\\u6784" }) })) : structureLoading ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Spin, { size: "large", style: { display: \'block\', textAlign: \'center\', padding: \'40px 0\' } })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_2__.Tabs, { type: "card", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(TabPane, { tab: "\\u6811\\u5F62\\u89C6\\u56FE", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Tree, { treeData: buildTreeData(), defaultExpandAll: true, showIcon: true }) }, "tree"), structure.tables.map(table => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(TabPane, { tab: table.name, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Table, { columns: structureColumns, dataSource: table.columns, rowKey: "name", pagination: false, size: "small" }) }, table.name)))] })) }) }) }, "structure"), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(TabPane, { tab: "\\u67E5\\u8BE2\\u7F16\\u8F91\\u5668", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(TabContent, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Card, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { style: { textAlign: \'center\', padding: \'40px 0\' }, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A, { style: { fontSize: \'48px\', color: \'#d9d9d9\', marginBottom: \'16px\' } }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { style: { marginBottom: \'16px\' }, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Text, { type: "secondary", children: "\\u5728\\u67E5\\u8BE2\\u7F16\\u8F91\\u5668\\u4E2D\\u6D4B\\u8BD5\\u548C\\u6267\\u884CSQL\\u67E5\\u8BE2" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__.Button, { type: "primary", onClick: () => navigate(`/query/${id}`), children: "\\u6253\\u5F00\\u67E5\\u8BE2\\u7F16\\u8F91\\u5668" })] }) }) }) }, "query")] })] }));\r\n};\r\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DatasourceDetail);\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///1358\n\n}')}}]);