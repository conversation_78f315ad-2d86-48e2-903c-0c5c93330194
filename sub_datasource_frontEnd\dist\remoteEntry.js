var datasource_app;(()=>{"use strict";var e,r,t,o,n,a,i,l,u,s,d,f,c,p,h,v,m,g,b,y,w={5413:(e,r,t)=>{var o={"./DatasourceApp":()=>Promise.all([t.e(96),t.e(212),t.e(76),t.e(299),t.e(185),t.e(17),t.e(187)]).then(()=>()=>t(3627)),"./DatasourceRoutes":()=>Promise.all([t.e(96),t.e(212),t.e(76),t.e(299),t.e(17),t.e(323)]).then(()=>()=>t(5323)),"./DatasourceStore":()=>Promise.all([t.e(96),t.e(876),t.e(76),t.e(255),t.e(340)]).then(()=>()=>t(3340))},n=(e,r)=>(t.R=r,r=t.o(o,e)?o[e]():Promise.resolve().then(()=>{throw new Error('Module "'+e+'" does not exist in container.')}),t.R=void 0,r),a=(e,r)=>{if(t.S){var o="default",n=t.S[o];if(n&&n!==e)throw new Error("Container initialization failed as it has already been initialized with a different share scope");return t.S[o]=e,t.I(o,r)}};t.d(r,{get:()=>n,init:()=>a})}},S={};function P(e){var r=S[e];if(void 0!==r)return r.exports;var t=S[e]={id:e,loaded:!1,exports:{}};return w[e].call(t.exports,t,t.exports,P),t.loaded=!0,t.exports}P.m=w,P.c=S,e=[],P.O=(r,t,o,n)=>{if(!t){var a=1/0;for(s=0;s<e.length;s++){t=e[s][0],o=e[s][1],n=e[s][2];for(var i=!0,l=0;l<t.length;l++)(!1&n||a>=n)&&Object.keys(P.O).every(e=>P.O[e](t[l]))?t.splice(l--,1):(i=!1,n<a&&(a=n));if(i){e.splice(s--,1);var u=o();void 0!==u&&(r=u)}}return r}n=n||0;for(var s=e.length;s>0&&e[s-1][2]>n;s--)e[s]=e[s-1];e[s]=[t,o,n]},P.n=e=>{var r=e&&e.__esModule?()=>e.default:()=>e;return P.d(r,{a:r}),r},P.d=(e,r)=>{for(var t in r)P.o(r,t)&&!P.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:r[t]})},P.f={},P.e=e=>Promise.all(Object.keys(P.f).reduce((r,t)=>(P.f[t](e,r),r),[])),P.u=e=>(96===e?"vendors":e)+".js",P.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),P.hmd=e=>((e=Object.create(e)).children||(e.children=[]),Object.defineProperty(e,"exports",{enumerable:!0,set:()=>{throw new Error("ES Modules may not assign module.exports or exports.*, Use ESM export syntax, instead: "+e.id)}}),e),P.o=(e,r)=>Object.prototype.hasOwnProperty.call(e,r),r={},t="pageplug-datasource-frontend:",P.l=(e,o,n,a)=>{if(r[e])r[e].push(o);else{var i,l;if(void 0!==n)for(var u=document.getElementsByTagName("script"),s=0;s<u.length;s++){var d=u[s];if(d.getAttribute("src")==e||d.getAttribute("data-webpack")==t+n){i=d;break}}i||(l=!0,(i=document.createElement("script")).charset="utf-8",i.timeout=120,P.nc&&i.setAttribute("nonce",P.nc),i.setAttribute("data-webpack",t+n),i.src=e),r[e]=[o];var f=(t,o)=>{i.onerror=i.onload=null,clearTimeout(c);var n=r[e];if(delete r[e],i.parentNode&&i.parentNode.removeChild(i),n&&n.forEach(e=>e(o)),t)return t(o)},c=setTimeout(f.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=f.bind(null,i.onerror),i.onload=f.bind(null,i.onload),l&&document.head.appendChild(i)}},P.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},P.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),(()=>{P.S={};var e={},r={};P.I=(t,o)=>{o||(o=[]);var n=r[t];if(n||(n=r[t]={}),!(o.indexOf(n)>=0)){if(o.push(n),e[t])return e[t];P.o(P.S,t)||(P.S[t]={});var a=P.S[t],i="pageplug-datasource-frontend",l=(e,r,t,o)=>{var n=a[e]=a[e]||{},l=n[r];(!l||!l.loaded&&(!o!=!l.eager?o:i>l.from))&&(n[r]={get:t,from:i,eager:!!o})},u=[];return"default"===t&&(l("@reduxjs/toolkit","1.9.7",()=>P.e(96).then(()=>()=>P(4636))),l("antd","5.26.6",()=>Promise.all([P.e(96),P.e(212),P.e(714)]).then(()=>()=>P(9392))),l("react-dom","18.3.1",()=>Promise.all([P.e(96),P.e(212)]).then(()=>()=>P(8143))),l("react-redux","8.1.3",()=>Promise.all([P.e(96),P.e(212),P.e(714)]).then(()=>()=>P(309))),l("react-router-dom","6.30.1",()=>Promise.all([P.e(96),P.e(212),P.e(714)]).then(()=>()=>P(8842))),l("react","18.3.1",()=>P.e(96).then(()=>()=>P(758))),l("styled-components","5.3.11",()=>Promise.all([P.e(96),P.e(212)]).then(()=>()=>P(2407)))),e[t]=u.length?Promise.all(u).then(()=>e[t]=1):1}}})(),(()=>{var e;P.g.importScripts&&(e=P.g.location+"");var r=P.g.document;if(!e&&r&&(r.currentScript&&"SCRIPT"===r.currentScript.tagName.toUpperCase()&&(e=r.currentScript.src),!e)){var t=r.getElementsByTagName("script");if(t.length)for(var o=t.length-1;o>-1&&(!e||!/^http(s?):/.test(e));)e=t[o--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/^blob:/,"").replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),P.p=e})(),o=e=>{var r=e=>e.split(".").map(e=>+e==e?+e:e),t=/^([^-+]+)?(?:-([^+]+))?(?:\+(.+))?$/.exec(e),o=t[1]?r(t[1]):[];return t[2]&&(o.length++,o.push.apply(o,r(t[2]))),t[3]&&(o.push([]),o.push.apply(o,r(t[3]))),o},n=(e,r)=>{e=o(e),r=o(r);for(var t=0;;){if(t>=e.length)return t<r.length&&"u"!=(typeof r[t])[0];var n=e[t],a=(typeof n)[0];if(t>=r.length)return"u"==a;var i=r[t],l=(typeof i)[0];if(a!=l)return"o"==a&&"n"==l||"s"==l||"u"==a;if("o"!=a&&"u"!=a&&n!=i)return n<i;t++}},a=e=>{var r=e[0],t="";if(1===e.length)return"*";if(r+.5){t+=0==r?">=":-1==r?"<":1==r?"^":2==r?"~":r>0?"=":"!=";for(var o=1,n=1;n<e.length;n++)o--,t+="u"==(typeof(l=e[n]))[0]?"-":(o>0?".":"")+(o=2,l);return t}var i=[];for(n=1;n<e.length;n++){var l=e[n];i.push(0===l?"not("+u()+")":1===l?"("+u()+" || "+u()+")":2===l?i.pop()+" "+i.pop():a(l))}return u();function u(){return i.pop().replace(/^\((.+)\)$/,"$1")}},i=(e,r)=>{if(0 in e){r=o(r);var t=e[0],n=t<0;n&&(t=-t-1);for(var a=0,l=1,u=!0;;l++,a++){var s,d,f=l<e.length?(typeof e[l])[0]:"";if(a>=r.length||"o"==(d=(typeof(s=r[a]))[0]))return!u||("u"==f?l>t&&!n:""==f!=n);if("u"==d){if(!u||"u"!=f)return!1}else if(u)if(f==d)if(l<=t){if(s!=e[l])return!1}else{if(n?s>e[l]:s<e[l])return!1;s!=e[l]&&(u=!1)}else if("s"!=f&&"n"!=f){if(n||l<=t)return!1;u=!1,l--}else{if(l<=t||d<f!=n)return!1;u=!1}else"s"!=f&&"n"!=f&&(u=!1,l--)}}var c=[],p=c.pop.bind(c);for(a=1;a<e.length;a++){var h=e[a];c.push(1==h?p()|p():2==h?p()&p():h?i(h,r):!p())}return!!p()},l=(e,r)=>e&&P.o(e,r),u=e=>(e.loaded=1,e.get()),s=e=>Object.keys(e).reduce((r,t)=>(e[t].eager&&(r[t]=e[t]),r),{}),d=(e,r,t)=>{var o=t?s(e[r]):e[r];return Object.keys(o).reduce((e,r)=>!e||!o[e].loaded&&n(e,r)?r:e,0)},f=(e,r,t,o)=>"Unsatisfied version "+t+" from "+(t&&e[r][t].from)+" of shared singleton module "+r+" (required "+a(o)+")",c=e=>{throw new Error(e)},p=e=>{"undefined"!=typeof console&&console.warn&&console.warn(e)},h=(e,r,t)=>t?t():((e,r)=>c("Shared module "+r+" doesn't exist in shared scope "+e))(e,r),v=(e=>function(r,t,o,n,a){var i=P.I(r);return i&&i.then&&!o?i.then(e.bind(e,r,P.S[r],t,!1,n,a)):e(r,P.S[r],t,o,n,a)})((e,r,t,o,n,a)=>{if(!l(r,t))return h(e,t,a);var s=d(r,t,o);return i(n,s)||p(f(r,t,s,n)),u(r[t][s])}),m={},g={212:()=>v("default","react",!1,[1,18,2,0],()=>P.e(96).then(()=>()=>P(758))),1714:()=>v("default","react-dom",!1,[1,18,2,0],()=>P.e(96).then(()=>()=>P(8143))),8076:()=>v("default","antd",!1,[1,5,2,2],()=>Promise.all([P.e(96),P.e(212),P.e(714)]).then(()=>()=>P(9392))),1299:()=>v("default","react-router-dom",!1,[1,6,8,1],()=>Promise.all([P.e(96),P.e(714)]).then(()=>()=>P(8842))),3017:()=>v("default","styled-components",!1,[1,5,3,6],()=>P.e(96).then(()=>()=>P(2407))),9235:()=>v("default","@reduxjs/toolkit",!1,[1,1,9,3],()=>P.e(96).then(()=>()=>P(4636))),8037:()=>v("default","react-redux",!1,[1,8,0,5],()=>Promise.all([P.e(96),P.e(212),P.e(714)]).then(()=>()=>P(309)))},b={17:[3017],76:[8076],212:[212],255:[9235],299:[1299],340:[8037],714:[1714]},y={},P.f.consumes=(e,r)=>{P.o(b,e)&&b[e].forEach(e=>{if(P.o(m,e))return r.push(m[e]);if(!y[e]){var t=r=>{m[e]=0,P.m[e]=t=>{delete P.c[e],t.exports=r()}};y[e]=!0;var o=r=>{delete m[e],P.m[e]=t=>{throw delete P.c[e],r}};try{var n=g[e]();n.then?r.push(m[e]=n.then(t).catch(o)):t(n)}catch(e){o(e)}}})},(()=>{var e={676:0,43:0};P.f.j=(r,t)=>{var o=P.o(e,r)?e[r]:void 0;if(0!==o)if(o)t.push(o[2]);else if(/^(17|212|299|43|714|76)$/.test(r))e[r]=0;else{var n=new Promise((t,n)=>o=e[r]=[t,n]);t.push(o[2]=n);var a=P.p+P.u(r),i=new Error;P.l(a,t=>{if(P.o(e,r)&&(0!==(o=e[r])&&(e[r]=void 0),o)){var n=t&&("load"===t.type?"missing":t.type),a=t&&t.target&&t.target.src;i.message="Loading chunk "+r+" failed.\n("+n+": "+a+")",i.name="ChunkLoadError",i.type=n,i.request=a,o[1](i)}},"chunk-"+r,r)}},P.O.j=r=>0===e[r];var r=(r,t)=>{var o,n,a=t[0],i=t[1],l=t[2],u=0;if(a.some(r=>0!==e[r])){for(o in i)P.o(i,o)&&(P.m[o]=i[o]);if(l)var s=l(P)}for(r&&r(t);u<a.length;u++)n=a[u],P.o(e,n)&&e[n]&&e[n][0](),e[n]=0;return P.O(s)},t=self.webpackChunkpageplug_datasource_frontend=self.webpackChunkpageplug_datasource_frontend||[];t.forEach(r.bind(null,0)),t.push=r.bind(null,t.push.bind(t))})(),P.nc=void 0;var x=P.O(void 0,[43],()=>P(5413));x=P.O(x),datasource_app=x})();