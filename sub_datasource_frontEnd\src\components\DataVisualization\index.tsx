import React from 'react';
import { Card, Empty } from 'antd';
import { BarChartOutlined } from '@ant-design/icons';

interface DataVisualizationProps {
  data?: any[];
  type?: 'bar' | 'line' | 'pie';
}

const DataVisualization: React.FC<DataVisualizationProps> = ({ data, type = 'bar' }) => {
  return (
    <Card title="数据可视化" extra={<BarChartOutlined />}>
      <Empty
        description="数据可视化组件开发中..."
        image={Empty.PRESENTED_IMAGE_SIMPLE}
      />
    </Card>
  );
};

export default DataVisualization;
