import {
  ApiResponse,
  PaginatedResponse,
  Datasource,
  Plugin,
  Query,
  DatasourceStructure,
  ConnectionTestResult,
  QueryExecutionResult,
  CreateDatasourceRequest,
  UpdateDatasourceRequest,
  ExecuteQueryRequest,
  CreateQueryRequest
} from '../types/api';

import {
  mockPlugins,
  mockDatasources,
  mockQueries,
  mockDatasourceStructure,
  mockConnectionTestResults
} from './mockData';

// 模拟延迟
const delay = (ms: number = 500) => new Promise(resolve => setTimeout(resolve, ms));

// 生成唯一ID
const generateId = () => `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

// 创建API响应
const createResponse = <T>(data: T, message = 'Success'): ApiResponse<T> => ({
  code: 200,
  message,
  data,
  timestamp: new Date().toISOString()
});

// 创建分页响应
const createPaginatedResponse = <T>(
  items: T[],
  page: number,
  size: number
): ApiResponse<PaginatedResponse<T>> => {
  const start = (page - 1) * size;
  const end = start + size;
  const content = items.slice(start, end);
  const total = items.length;
  const totalPages = Math.ceil(total / size);

  return createResponse({
    content,
    pagination: {
      page,
      size,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrevious: page > 1
    }
  });
};

// 模拟API类
export class MockApi {
  // 数据源相关API
  static async getDatasources(params: {
    workspaceId?: string;
    pluginId?: string;
    page?: number;
    size?: number;
  } = {}): Promise<ApiResponse<PaginatedResponse<Datasource>>> {
    await delay();

    let filteredDatasources = [...mockDatasources];

    // 过滤条件
    if (params.workspaceId) {
      filteredDatasources = filteredDatasources.filter(
        ds => ds.workspaceId === params.workspaceId
      );
    }

    if (params.pluginId) {
      filteredDatasources = filteredDatasources.filter(
        ds => ds.pluginId === params.pluginId
      );
    }

    const page = params.page || 1;
    const size = params.size || 20;

    return createPaginatedResponse(filteredDatasources, page, size);
  }

  static async getDatasource(id: string): Promise<ApiResponse<Datasource>> {
    await delay();

    const datasource = mockDatasources.find(ds => ds.id === id);
    if (!datasource) {
      throw new Error(`Datasource with id ${id} not found`);
    }

    return createResponse(datasource);
  }

  static async createDatasource(
    request: CreateDatasourceRequest
  ): Promise<ApiResponse<Datasource>> {
    await delay(800);

    const plugin = mockPlugins.find(p => p.id === request.pluginId);
    if (!plugin) {
      throw new Error(`Plugin with id ${request.pluginId} not found`);
    }

    const newDatasource: Datasource = {
      id: `ds-${generateId()}`,
      name: request.name,
      pluginId: request.pluginId,
      pluginName: plugin.displayName,
      workspaceId: request.workspaceId,
      isValid: true,
      isConfigured: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      datasourceConfiguration: request.datasourceConfiguration
    };

    mockDatasources.push(newDatasource);
    return createResponse(newDatasource, 'Datasource created successfully');
  }

  static async updateDatasource(
    id: string,
    request: UpdateDatasourceRequest
  ): Promise<ApiResponse<Datasource>> {
    await delay(600);

    const datasourceIndex = mockDatasources.findIndex(ds => ds.id === id);
    if (datasourceIndex === -1) {
      throw new Error(`Datasource with id ${id} not found`);
    }

    const updatedDatasource = {
      ...mockDatasources[datasourceIndex],
      ...request,
      datasourceConfiguration: {
        ...mockDatasources[datasourceIndex].datasourceConfiguration,
        ...request.datasourceConfiguration
      },
      updatedAt: new Date().toISOString()
    };

    mockDatasources[datasourceIndex] = updatedDatasource;
    return createResponse(updatedDatasource, 'Datasource updated successfully');
  }

  static async deleteDatasource(id: string): Promise<ApiResponse<void>> {
    await delay(400);

    const datasourceIndex = mockDatasources.findIndex(ds => ds.id === id);
    if (datasourceIndex === -1) {
      throw new Error(`Datasource with id ${id} not found`);
    }

    mockDatasources.splice(datasourceIndex, 1);
    return createResponse(undefined, 'Datasource deleted successfully');
  }

  static async testDatasourceConnection(
    id: string
  ): Promise<ApiResponse<ConnectionTestResult>> {
    await delay(1500); // 模拟连接测试时间

    const result = mockConnectionTestResults[id] || {
      success: Math.random() > 0.3, // 70% 成功率
      message: Math.random() > 0.3 ? '连接成功' : '连接失败：超时',
      responseTime: Math.floor(Math.random() * 2000) + 100
    };

    return createResponse(result, 'Connection test completed');
  }

  static async getDatasourceStructure(
    id: string
  ): Promise<ApiResponse<DatasourceStructure>> {
    await delay(1000);

    const structure = mockDatasourceStructure[id] || {
      tables: []
    };

    return createResponse(structure, 'Structure retrieved successfully');
  }

  static async executeQuery(
    id: string,
    request: ExecuteQueryRequest
  ): Promise<ApiResponse<QueryExecutionResult>> {
    await delay(800);

    // 模拟查询结果
    const mockResult: QueryExecutionResult = {
      isExecutionSuccess: Math.random() > 0.1, // 90% 成功率
      body: [
        { id: 1, name: 'John Doe', email: '<EMAIL>', status: 'active' },
        { id: 2, name: 'Jane Smith', email: '<EMAIL>', status: 'active' },
        { id: 3, name: 'Bob Johnson', email: '<EMAIL>', status: 'inactive' }
      ],
      headers: {
        'Content-Type': 'application/json'
      },
      statusCode: '200',
      executionTime: Math.floor(Math.random() * 500) + 50,
      rowsAffected: 3
    };

    if (!mockResult.isExecutionSuccess) {
      mockResult.error = {
        message: 'Syntax error in SQL statement',
        code: 'SQL001',
        line: 1,
        column: 15
      };
      mockResult.body = [];
      mockResult.rowsAffected = 0;
    }

    return createResponse(mockResult, 'Query executed successfully');
  }

  // 插件相关API
  static async getPlugins(params: {
    category?: string;
    status?: string;
  } = {}): Promise<ApiResponse<Plugin[]>> {
    await delay();

    let filteredPlugins = [...mockPlugins];

    if (params.category) {
      filteredPlugins = filteredPlugins.filter(p => p.category === params.category);
    }

    if (params.status) {
      filteredPlugins = filteredPlugins.filter(p => p.status === params.status);
    }

    return createResponse(filteredPlugins);
  }

  static async getPlugin(id: string): Promise<ApiResponse<Plugin>> {
    await delay();

    const plugin = mockPlugins.find(p => p.id === id);
    if (!plugin) {
      throw new Error(`Plugin with id ${id} not found`);
    }

    return createResponse(plugin);
  }

  // 查询相关API
  static async getQueries(params: {
    datasourceId?: string;
    tag?: string;
  } = {}): Promise<ApiResponse<Query[]>> {
    await delay();

    let filteredQueries = [...mockQueries];

    if (params.datasourceId) {
      filteredQueries = filteredQueries.filter(q => q.datasourceId === params.datasourceId);
    }

    if (params.tag) {
      filteredQueries = filteredQueries.filter(q => q.tags.includes(params.tag));
    }

    return createResponse(filteredQueries);
  }

  static async createQuery(request: CreateQueryRequest): Promise<ApiResponse<Query>> {
    await delay(600);

    const newQuery: Query = {
      id: `query-${generateId()}`,
      name: request.name,
      datasourceId: request.datasourceId,
      query: request.query,
      parameters: request.parameters,
      tags: request.tags,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    mockQueries.push(newQuery);
    return createResponse(newQuery, 'Query created successfully');
  }

  static async executeQuery(
    queryId: string,
    parameters: Array<{ key: string; value: any }> = []
  ): Promise<ApiResponse<QueryExecutionResult>> {
    await delay(600);

    const query = mockQueries.find(q => q.id === queryId);
    if (!query) {
      throw new Error(`Query with id ${queryId} not found`);
    }

    // 更新最后执行时间
    query.lastExecuted = new Date().toISOString();

    // 模拟执行结果
    const mockResult: QueryExecutionResult = {
      isExecutionSuccess: true,
      body: [
        { id: 1, name: 'Sample Data', value: 100 },
        { id: 2, name: 'Another Record', value: 200 }
      ],
      headers: {
        'Content-Type': 'application/json'
      },
      statusCode: '200',
      executionTime: Math.floor(Math.random() * 300) + 50,
      rowsAffected: 2
    };

    return createResponse(mockResult, 'Query executed successfully');
  }
}
