"use strict";(self.webpackChunkpageplug_datasource_frontend=self.webpackChunkpageplug_datasource_frontend||[]).push([[876],{8876:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{eval("{\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  u: () => (/* binding */ apiClient)\n});\n\n// UNUSED EXPORTS: ApiClient\n\n// EXTERNAL MODULE: ./node_modules/.pnpm/axios@1.11.0/node_modules/axios/lib/axios.js + 48 modules\nvar axios = __webpack_require__(1886);\n// EXTERNAL MODULE: consume shared module (default) antd@^5.2.2 (singleton) (fallback: ./node_modules/.pnpm/antd@5.26.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/index.js)\nvar index_js_ = __webpack_require__(8076);\n;// ./src/services/mockData.ts\nconst mockPlugins = [\r\n    {\r\n        id: 'mysql-plugin',\r\n        name: 'mysql',\r\n        displayName: 'MySQL Database',\r\n        version: '1.0.0',\r\n        category: 'database',\r\n        status: 'active',\r\n        description: '连接到 MySQL 数据库，支持 5.7+ 版本',\r\n        iconUrl: '/icons/mysql.svg',\r\n        documentationUrl: 'https://docs.example.com/mysql',\r\n        supportedOperations: ['READ', 'WRITE', 'DELETE'],\r\n        templates: {\r\n            SELECT: 'SELECT * FROM table_name LIMIT 10;',\r\n            INSERT: 'INSERT INTO table_name (column1, column2) VALUES (?, ?);',\r\n            UPDATE: 'UPDATE table_name SET column1 = ? WHERE id = ?;',\r\n            DELETE: 'DELETE FROM table_name WHERE id = ?;'\r\n        }\r\n    },\r\n    {\r\n        id: 'postgresql-plugin',\r\n        name: 'postgresql',\r\n        displayName: 'PostgreSQL Database',\r\n        version: '1.0.0',\r\n        category: 'database',\r\n        status: 'active',\r\n        description: '连接到 PostgreSQL 数据库，支持 12+ 版本',\r\n        iconUrl: '/icons/postgresql.svg',\r\n        documentationUrl: 'https://docs.example.com/postgresql',\r\n        supportedOperations: ['READ', 'WRITE', 'DELETE'],\r\n        templates: {\r\n            SELECT: 'SELECT * FROM table_name LIMIT 10;',\r\n            INSERT: 'INSERT INTO table_name (column1, column2) VALUES ($1, $2);',\r\n            UPDATE: 'UPDATE table_name SET column1 = $1 WHERE id = $2;',\r\n            DELETE: 'DELETE FROM table_name WHERE id = $1;'\r\n        }\r\n    },\r\n    {\r\n        id: 'mongodb-plugin',\r\n        name: 'mongodb',\r\n        displayName: 'MongoDB Database',\r\n        version: '1.0.0',\r\n        category: 'database',\r\n        status: 'active',\r\n        description: '连接到 MongoDB 数据库，支持 4.4+ 版本',\r\n        iconUrl: '/icons/mongodb.svg',\r\n        documentationUrl: 'https://docs.example.com/mongodb',\r\n        supportedOperations: ['READ', 'WRITE', 'DELETE'],\r\n        templates: {\r\n            FIND: 'db.collection.find({});',\r\n            INSERT: 'db.collection.insertOne({});',\r\n            UPDATE: 'db.collection.updateOne({}, {$set: {}});',\r\n            DELETE: 'db.collection.deleteOne({});'\r\n        }\r\n    },\r\n    {\r\n        id: 'redis-plugin',\r\n        name: 'redis',\r\n        displayName: 'Redis Cache',\r\n        version: '1.0.0',\r\n        category: 'database',\r\n        status: 'active',\r\n        description: '连接到 Redis 缓存数据库',\r\n        iconUrl: '/icons/redis.svg',\r\n        documentationUrl: 'https://docs.example.com/redis',\r\n        supportedOperations: ['READ', 'WRITE', 'DELETE'],\r\n        templates: {\r\n            GET: 'GET key',\r\n            SET: 'SET key value',\r\n            DEL: 'DEL key',\r\n            KEYS: 'KEYS pattern'\r\n        }\r\n    },\r\n    {\r\n        id: 'restapi-plugin',\r\n        name: 'restapi',\r\n        displayName: 'REST API',\r\n        version: '1.0.0',\r\n        category: 'api',\r\n        status: 'active',\r\n        description: '连接到 REST API 服务',\r\n        iconUrl: '/icons/api.svg',\r\n        documentationUrl: 'https://docs.example.com/restapi',\r\n        supportedOperations: ['READ', 'WRITE'],\r\n        templates: {\r\n            GET: 'GET /api/endpoint',\r\n            POST: 'POST /api/endpoint',\r\n            PUT: 'PUT /api/endpoint',\r\n            DELETE: 'DELETE /api/endpoint'\r\n        }\r\n    },\r\n    {\r\n        id: 'elasticsearch-plugin',\r\n        name: 'elasticsearch',\r\n        displayName: 'Elasticsearch',\r\n        version: '1.0.0',\r\n        category: 'database',\r\n        status: 'active',\r\n        description: '连接到 Elasticsearch 搜索引擎',\r\n        iconUrl: '/icons/elasticsearch.svg',\r\n        documentationUrl: 'https://docs.example.com/elasticsearch',\r\n        supportedOperations: ['READ', 'WRITE'],\r\n        templates: {\r\n            SEARCH: 'GET /index/_search',\r\n            INDEX: 'POST /index/_doc',\r\n            UPDATE: 'POST /index/_update/id',\r\n            DELETE: 'DELETE /index/_doc/id'\r\n        }\r\n    }\r\n];\r\nconst mockDatasources = [\r\n    {\r\n        id: 'ds-001',\r\n        name: 'MySQL 生产环境',\r\n        pluginId: 'mysql-plugin',\r\n        pluginName: 'MySQL Database',\r\n        workspaceId: 'workspace-123',\r\n        isValid: true,\r\n        isConfigured: true,\r\n        lastUsed: '2024-01-15T09:45:00Z',\r\n        createdAt: '2024-01-10T10:30:00Z',\r\n        updatedAt: '2024-01-15T10:30:00Z',\r\n        datasourceConfiguration: {\r\n            url: 'mysql://prod-mysql.example.com:3306',\r\n            databaseName: 'production',\r\n            authentication: {\r\n                authType: 'basic',\r\n                username: 'app_user',\r\n                secretExists: {\r\n                    password: true\r\n                }\r\n            },\r\n            connection: {\r\n                ssl: {\r\n                    enabled: true,\r\n                    mode: 'REQUIRED'\r\n                },\r\n                timeout: 30000,\r\n                maxConnections: 10\r\n            }\r\n        }\r\n    },\r\n    {\r\n        id: 'ds-002',\r\n        name: 'PostgreSQL 开发环境',\r\n        pluginId: 'postgresql-plugin',\r\n        pluginName: 'PostgreSQL Database',\r\n        workspaceId: 'workspace-123',\r\n        isValid: true,\r\n        isConfigured: true,\r\n        lastUsed: '2024-01-14T16:20:00Z',\r\n        createdAt: '2024-01-08T14:15:00Z',\r\n        updatedAt: '2024-01-14T16:20:00Z',\r\n        datasourceConfiguration: {\r\n            url: 'postgresql://dev-postgres.example.com:5432',\r\n            databaseName: 'development',\r\n            authentication: {\r\n                authType: 'basic',\r\n                username: 'dev_user',\r\n                secretExists: {\r\n                    password: true\r\n                }\r\n            },\r\n            connection: {\r\n                ssl: {\r\n                    enabled: false\r\n                },\r\n                timeout: 15000,\r\n                maxConnections: 5\r\n            }\r\n        }\r\n    },\r\n    {\r\n        id: 'ds-003',\r\n        name: 'MongoDB 用户数据',\r\n        pluginId: 'mongodb-plugin',\r\n        pluginName: 'MongoDB Database',\r\n        workspaceId: 'workspace-123',\r\n        isValid: false,\r\n        isConfigured: true,\r\n        lastUsed: '2024-01-12T11:30:00Z',\r\n        createdAt: '2024-01-05T09:00:00Z',\r\n        updatedAt: '2024-01-12T11:30:00Z',\r\n        datasourceConfiguration: {\r\n            url: 'mongodb://mongo.example.com:27017',\r\n            databaseName: 'userdata',\r\n            authentication: {\r\n                authType: 'basic',\r\n                username: 'mongo_user',\r\n                secretExists: {\r\n                    password: true\r\n                }\r\n            }\r\n        }\r\n    },\r\n    {\r\n        id: 'ds-004',\r\n        name: 'Redis 缓存',\r\n        pluginId: 'redis-plugin',\r\n        pluginName: 'Redis Cache',\r\n        workspaceId: 'workspace-123',\r\n        isValid: true,\r\n        isConfigured: true,\r\n        lastUsed: '2024-01-15T08:15:00Z',\r\n        createdAt: '2024-01-03T16:45:00Z',\r\n        updatedAt: '2024-01-15T08:15:00Z',\r\n        datasourceConfiguration: {\r\n            url: 'redis://cache.example.com:6379',\r\n            authentication: {\r\n                authType: 'basic',\r\n                secretExists: {\r\n                    password: true\r\n                }\r\n            }\r\n        }\r\n    },\r\n    {\r\n        id: 'ds-005',\r\n        name: 'API Gateway',\r\n        pluginId: 'restapi-plugin',\r\n        pluginName: 'REST API',\r\n        workspaceId: 'workspace-123',\r\n        isValid: true,\r\n        isConfigured: true,\r\n        lastUsed: '2024-01-15T10:00:00Z',\r\n        createdAt: '2024-01-01T12:00:00Z',\r\n        updatedAt: '2024-01-15T10:00:00Z',\r\n        datasourceConfiguration: {\r\n            url: 'https://api.example.com',\r\n            authentication: {\r\n                authType: 'bearer',\r\n                secretExists: {\r\n                    token: true\r\n                }\r\n            },\r\n            headers: [\r\n                {\r\n                    key: 'Content-Type',\r\n                    value: 'application/json'\r\n                }\r\n            ]\r\n        }\r\n    }\r\n];\r\nconst mockQueries = [\r\n    {\r\n        id: 'query-001',\r\n        name: '获取活跃用户',\r\n        datasourceId: 'ds-001',\r\n        query: 'SELECT * FROM users WHERE status = ? AND last_login > ? LIMIT ?',\r\n        parameters: [\r\n            { key: 'status', value: 'active' },\r\n            { key: 'last_login', value: '2024-01-01' },\r\n            { key: 'limit', value: 100 }\r\n        ],\r\n        tags: ['users', 'active'],\r\n        createdAt: '2024-01-10T10:30:00Z',\r\n        updatedAt: '2024-01-15T10:30:00Z',\r\n        lastExecuted: '2024-01-15T09:45:00Z'\r\n    },\r\n    {\r\n        id: 'query-002',\r\n        name: '订单统计',\r\n        datasourceId: 'ds-002',\r\n        query: 'SELECT DATE(created_at) as date, COUNT(*) as count FROM orders WHERE created_at >= $1 GROUP BY DATE(created_at)',\r\n        parameters: [\r\n            { key: 'start_date', value: '2024-01-01' }\r\n        ],\r\n        tags: ['orders', 'statistics'],\r\n        createdAt: '2024-01-08T14:15:00Z',\r\n        updatedAt: '2024-01-14T16:20:00Z',\r\n        lastExecuted: '2024-01-14T16:20:00Z'\r\n    }\r\n];\r\nconst mockDatasourceStructure = {\r\n    'ds-001': {\r\n        tables: [\r\n            {\r\n                name: 'users',\r\n                type: 'TABLE',\r\n                columns: [\r\n                    {\r\n                        name: 'id',\r\n                        type: 'INT',\r\n                        isPrimaryKey: true,\r\n                        isAutoIncrement: true,\r\n                        isNullable: false\r\n                    },\r\n                    {\r\n                        name: 'username',\r\n                        type: 'VARCHAR',\r\n                        maxLength: 50,\r\n                        isNullable: false\r\n                    },\r\n                    {\r\n                        name: 'email',\r\n                        type: 'VARCHAR',\r\n                        maxLength: 100,\r\n                        isNullable: false\r\n                    },\r\n                    {\r\n                        name: 'status',\r\n                        type: 'ENUM',\r\n                        isNullable: false,\r\n                        defaultValue: 'active'\r\n                    },\r\n                    {\r\n                        name: 'created_at',\r\n                        type: 'TIMESTAMP',\r\n                        isNullable: false\r\n                    },\r\n                    {\r\n                        name: 'last_login',\r\n                        type: 'TIMESTAMP',\r\n                        isNullable: true\r\n                    }\r\n                ],\r\n                keys: [\r\n                    {\r\n                        name: 'PRIMARY',\r\n                        type: 'PRIMARY',\r\n                        columnNames: ['id']\r\n                    },\r\n                    {\r\n                        name: 'idx_email',\r\n                        type: 'UNIQUE',\r\n                        columnNames: ['email']\r\n                    }\r\n                ],\r\n                templates: [\r\n                    {\r\n                        title: '查询所有用户',\r\n                        body: 'SELECT * FROM users LIMIT 10;',\r\n                        suggested: true\r\n                    },\r\n                    {\r\n                        title: '查询活跃用户',\r\n                        body: \"SELECT * FROM users WHERE status = 'active' LIMIT 10;\"\r\n                    }\r\n                ]\r\n            },\r\n            {\r\n                name: 'orders',\r\n                type: 'TABLE',\r\n                columns: [\r\n                    {\r\n                        name: 'id',\r\n                        type: 'INT',\r\n                        isPrimaryKey: true,\r\n                        isAutoIncrement: true,\r\n                        isNullable: false\r\n                    },\r\n                    {\r\n                        name: 'user_id',\r\n                        type: 'INT',\r\n                        isNullable: false\r\n                    },\r\n                    {\r\n                        name: 'total_amount',\r\n                        type: 'DECIMAL',\r\n                        isNullable: false\r\n                    },\r\n                    {\r\n                        name: 'status',\r\n                        type: 'ENUM',\r\n                        isNullable: false\r\n                    },\r\n                    {\r\n                        name: 'created_at',\r\n                        type: 'TIMESTAMP',\r\n                        isNullable: false\r\n                    }\r\n                ],\r\n                keys: [\r\n                    {\r\n                        name: 'PRIMARY',\r\n                        type: 'PRIMARY',\r\n                        columnNames: ['id']\r\n                    },\r\n                    {\r\n                        name: 'fk_user_id',\r\n                        type: 'FOREIGN',\r\n                        columnNames: ['user_id']\r\n                    }\r\n                ],\r\n                templates: [\r\n                    {\r\n                        title: '查询订单',\r\n                        body: 'SELECT * FROM orders LIMIT 10;',\r\n                        suggested: true\r\n                    }\r\n                ]\r\n            }\r\n        ]\r\n    }\r\n};\r\nconst mockConnectionTestResults = {\r\n    'ds-001': {\r\n        success: true,\r\n        message: '连接成功',\r\n        responseTime: 150,\r\n        details: {\r\n            serverVersion: '8.0.28',\r\n            charset: 'utf8mb4'\r\n        }\r\n    },\r\n    'ds-002': {\r\n        success: true,\r\n        message: '连接成功',\r\n        responseTime: 120,\r\n        details: {\r\n            serverVersion: '14.5',\r\n            charset: 'UTF8'\r\n        }\r\n    },\r\n    'ds-003': {\r\n        success: false,\r\n        message: '连接失败：认证错误',\r\n        responseTime: 5000,\r\n        details: {\r\n            error: 'Authentication failed'\r\n        }\r\n    }\r\n};\r\n\n;// ./src/services/mockApi.ts\n\r\nconst delay = (ms = 500) => new Promise(resolve => setTimeout(resolve, ms));\r\nconst generateId = () => `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\r\nconst createResponse = (data, message = 'Success') => ({\r\n    code: 200,\r\n    message,\r\n    data,\r\n    timestamp: new Date().toISOString()\r\n});\r\nconst createPaginatedResponse = (items, page, size) => {\r\n    const start = (page - 1) * size;\r\n    const end = start + size;\r\n    const content = items.slice(start, end);\r\n    const total = items.length;\r\n    const totalPages = Math.ceil(total / size);\r\n    return createResponse({\r\n        content,\r\n        pagination: {\r\n            page,\r\n            size,\r\n            total,\r\n            totalPages,\r\n            hasNext: page < totalPages,\r\n            hasPrevious: page > 1\r\n        }\r\n    });\r\n};\r\nclass MockApi {\r\n    static async getDatasources(params = {}) {\r\n        await delay();\r\n        let filteredDatasources = [...mockDatasources];\r\n        if (params.workspaceId) {\r\n            filteredDatasources = filteredDatasources.filter(ds => ds.workspaceId === params.workspaceId);\r\n        }\r\n        if (params.pluginId) {\r\n            filteredDatasources = filteredDatasources.filter(ds => ds.pluginId === params.pluginId);\r\n        }\r\n        const page = params.page || 1;\r\n        const size = params.size || 20;\r\n        return createPaginatedResponse(filteredDatasources, page, size);\r\n    }\r\n    static async getDatasource(id) {\r\n        await delay();\r\n        const datasource = mockDatasources.find(ds => ds.id === id);\r\n        if (!datasource) {\r\n            throw new Error(`Datasource with id ${id} not found`);\r\n        }\r\n        return createResponse(datasource);\r\n    }\r\n    static async createDatasource(request) {\r\n        await delay(800);\r\n        const plugin = mockPlugins.find(p => p.id === request.pluginId);\r\n        if (!plugin) {\r\n            throw new Error(`Plugin with id ${request.pluginId} not found`);\r\n        }\r\n        const newDatasource = {\r\n            id: `ds-${generateId()}`,\r\n            name: request.name,\r\n            pluginId: request.pluginId,\r\n            pluginName: plugin.displayName,\r\n            workspaceId: request.workspaceId,\r\n            isValid: true,\r\n            isConfigured: true,\r\n            createdAt: new Date().toISOString(),\r\n            updatedAt: new Date().toISOString(),\r\n            datasourceConfiguration: request.datasourceConfiguration\r\n        };\r\n        mockDatasources.push(newDatasource);\r\n        return createResponse(newDatasource, 'Datasource created successfully');\r\n    }\r\n    static async updateDatasource(id, request) {\r\n        await delay(600);\r\n        const datasourceIndex = mockDatasources.findIndex(ds => ds.id === id);\r\n        if (datasourceIndex === -1) {\r\n            throw new Error(`Datasource with id ${id} not found`);\r\n        }\r\n        const updatedDatasource = {\r\n            ...mockDatasources[datasourceIndex],\r\n            ...request,\r\n            datasourceConfiguration: {\r\n                ...mockDatasources[datasourceIndex].datasourceConfiguration,\r\n                ...request.datasourceConfiguration\r\n            },\r\n            updatedAt: new Date().toISOString()\r\n        };\r\n        mockDatasources[datasourceIndex] = updatedDatasource;\r\n        return createResponse(updatedDatasource, 'Datasource updated successfully');\r\n    }\r\n    static async deleteDatasource(id) {\r\n        await delay(400);\r\n        const datasourceIndex = mockDatasources.findIndex(ds => ds.id === id);\r\n        if (datasourceIndex === -1) {\r\n            throw new Error(`Datasource with id ${id} not found`);\r\n        }\r\n        mockDatasources.splice(datasourceIndex, 1);\r\n        return createResponse(undefined, 'Datasource deleted successfully');\r\n    }\r\n    static async testDatasourceConnection(id) {\r\n        await delay(1500);\r\n        const result = mockConnectionTestResults[id] || {\r\n            success: Math.random() > 0.3,\r\n            message: Math.random() > 0.3 ? '连接成功' : '连接失败：超时',\r\n            responseTime: Math.floor(Math.random() * 2000) + 100\r\n        };\r\n        return createResponse(result, 'Connection test completed');\r\n    }\r\n    static async getDatasourceStructure(id) {\r\n        await delay(1000);\r\n        const structure = mockDatasourceStructure[id] || {\r\n            tables: []\r\n        };\r\n        return createResponse(structure, 'Structure retrieved successfully');\r\n    }\r\n    static async executeQuery(id, request) {\r\n        await delay(800);\r\n        const mockResult = {\r\n            isExecutionSuccess: Math.random() > 0.1,\r\n            body: [\r\n                { id: 1, name: 'John Doe', email: '<EMAIL>', status: 'active' },\r\n                { id: 2, name: 'Jane Smith', email: '<EMAIL>', status: 'active' },\r\n                { id: 3, name: 'Bob Johnson', email: '<EMAIL>', status: 'inactive' }\r\n            ],\r\n            headers: {\r\n                'Content-Type': 'application/json'\r\n            },\r\n            statusCode: '200',\r\n            executionTime: Math.floor(Math.random() * 500) + 50,\r\n            rowsAffected: 3\r\n        };\r\n        if (!mockResult.isExecutionSuccess) {\r\n            mockResult.error = {\r\n                message: 'Syntax error in SQL statement',\r\n                code: 'SQL001',\r\n                line: 1,\r\n                column: 15\r\n            };\r\n            mockResult.body = [];\r\n            mockResult.rowsAffected = 0;\r\n        }\r\n        return createResponse(mockResult, 'Query executed successfully');\r\n    }\r\n    static async getPlugins(params = {}) {\r\n        await delay();\r\n        let filteredPlugins = [...mockPlugins];\r\n        if (params.category) {\r\n            filteredPlugins = filteredPlugins.filter(p => p.category === params.category);\r\n        }\r\n        if (params.status) {\r\n            filteredPlugins = filteredPlugins.filter(p => p.status === params.status);\r\n        }\r\n        return createResponse(filteredPlugins);\r\n    }\r\n    static async getPlugin(id) {\r\n        await delay();\r\n        const plugin = mockPlugins.find(p => p.id === id);\r\n        if (!plugin) {\r\n            throw new Error(`Plugin with id ${id} not found`);\r\n        }\r\n        return createResponse(plugin);\r\n    }\r\n    static async getQueries(params = {}) {\r\n        await delay();\r\n        let filteredQueries = [...mockQueries];\r\n        if (params.datasourceId) {\r\n            filteredQueries = filteredQueries.filter(q => q.datasourceId === params.datasourceId);\r\n        }\r\n        if (params.tag) {\r\n            filteredQueries = filteredQueries.filter(q => q.tags.includes(params.tag));\r\n        }\r\n        return createResponse(filteredQueries);\r\n    }\r\n    static async createQuery(request) {\r\n        await delay(600);\r\n        const newQuery = {\r\n            id: `query-${generateId()}`,\r\n            name: request.name,\r\n            datasourceId: request.datasourceId,\r\n            query: request.query,\r\n            parameters: request.parameters,\r\n            tags: request.tags,\r\n            createdAt: new Date().toISOString(),\r\n            updatedAt: new Date().toISOString()\r\n        };\r\n        mockQueries.push(newQuery);\r\n        return createResponse(newQuery, 'Query created successfully');\r\n    }\r\n    static async executeQuery(queryId, parameters = []) {\r\n        await delay(600);\r\n        const query = mockQueries.find(q => q.id === queryId);\r\n        if (!query) {\r\n            throw new Error(`Query with id ${queryId} not found`);\r\n        }\r\n        query.lastExecuted = new Date().toISOString();\r\n        const mockResult = {\r\n            isExecutionSuccess: true,\r\n            body: [\r\n                { id: 1, name: 'Sample Data', value: 100 },\r\n                { id: 2, name: 'Another Record', value: 200 }\r\n            ],\r\n            headers: {\r\n                'Content-Type': 'application/json'\r\n            },\r\n            statusCode: '200',\r\n            executionTime: Math.floor(Math.random() * 300) + 50,\r\n            rowsAffected: 2\r\n        };\r\n        return createResponse(mockResult, 'Query executed successfully');\r\n    }\r\n}\r\n\n;// ./src/services/apiClient.ts\n\r\n\r\n\r\nconst defaultConfig = {\r\n    baseURL: \"http://localhost:8000/api/v1\" || 0,\r\n    timeout: 10000,\r\n    useMock:  true || 0\r\n};\r\nclass ApiClient {\r\n    constructor(config = {}) {\r\n        this.config = { ...defaultConfig, ...config };\r\n        this.axiosInstance = axios/* default */.A.create({\r\n            baseURL: this.config.baseURL,\r\n            timeout: this.config.timeout,\r\n            headers: {\r\n                'Content-Type': 'application/json'\r\n            }\r\n        });\r\n        this.setupInterceptors();\r\n    }\r\n    setupInterceptors() {\r\n        this.axiosInstance.interceptors.request.use((config) => {\r\n            const token = localStorage.getItem('auth_token');\r\n            if (token) {\r\n                config.headers.Authorization = `Bearer ${token}`;\r\n            }\r\n            config.headers['X-Request-ID'] = this.generateRequestId();\r\n            console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, config.data);\r\n            return config;\r\n        }, (error) => {\r\n            console.error('[API Request Error]', error);\r\n            return Promise.reject(error);\r\n        });\r\n        this.axiosInstance.interceptors.response.use((response) => {\r\n            console.log(`[API Response] ${response.config.url}`, response.data);\r\n            return response;\r\n        }, (error) => {\r\n            console.error('[API Response Error]', error);\r\n            if (error.response) {\r\n                const { status, data } = error.response;\r\n                switch (status) {\r\n                    case 401:\r\n                        index_js_.message.error('认证失败，请重新登录');\r\n                        localStorage.removeItem('auth_token');\r\n                        window.location.href = '/login';\r\n                        break;\r\n                    case 403:\r\n                        index_js_.message.error('权限不足');\r\n                        break;\r\n                    case 404:\r\n                        index_js_.message.error('请求的资源不存在');\r\n                        break;\r\n                    case 429:\r\n                        index_js_.message.error('请求过于频繁，请稍后再试');\r\n                        break;\r\n                    case 500:\r\n                        index_js_.message.error('服务器内部错误');\r\n                        break;\r\n                    default:\r\n                        index_js_.message.error(data?.message || '请求失败');\r\n                }\r\n                return Promise.reject(data || error);\r\n            }\r\n            else if (error.request) {\r\n                index_js_.message.error('网络连接失败，请检查网络设置');\r\n                return Promise.reject(new Error('Network Error'));\r\n            }\r\n            else {\r\n                index_js_.message.error('请求配置错误');\r\n                return Promise.reject(error);\r\n            }\r\n        });\r\n    }\r\n    generateRequestId() {\r\n        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n    }\r\n    async request(config) {\r\n        if (this.config.useMock) {\r\n            return this.handleMockRequest(config);\r\n        }\r\n        const response = await this.axiosInstance.request(config);\r\n        return response.data;\r\n    }\r\n    async handleMockRequest(config) {\r\n        const { method = 'GET', url = '', data } = config;\r\n        const path = url.replace(this.config.baseURL, '');\r\n        console.log(`[Mock API] ${method.toUpperCase()} ${path}`, data);\r\n        try {\r\n            if (path.startsWith('/datasources')) {\r\n                return this.handleDatasourceMockRequest(method, path, data);\r\n            }\r\n            else if (path.startsWith('/plugins')) {\r\n                return this.handlePluginMockRequest(method, path, data);\r\n            }\r\n            else if (path.startsWith('/queries')) {\r\n                return this.handleQueryMockRequest(method, path, data);\r\n            }\r\n            else {\r\n                throw new Error(`Mock API not implemented for ${path}`);\r\n            }\r\n        }\r\n        catch (error) {\r\n            console.error('[Mock API Error]', error);\r\n            throw error;\r\n        }\r\n    }\r\n    async handleDatasourceMockRequest(method, path, data) {\r\n        const segments = path.split('/').filter(Boolean);\r\n        switch (method.toUpperCase()) {\r\n            case 'GET':\r\n                if (segments.length === 1) {\r\n                    return MockApi.getDatasources(data);\r\n                }\r\n                else if (segments.length === 2) {\r\n                    return MockApi.getDatasource(segments[1]);\r\n                }\r\n                else if (segments.length === 3 && segments[2] === 'structure') {\r\n                    return MockApi.getDatasourceStructure(segments[1]);\r\n                }\r\n                break;\r\n            case 'POST':\r\n                if (segments.length === 1) {\r\n                    return MockApi.createDatasource(data);\r\n                }\r\n                else if (segments.length === 3 && segments[2] === 'test') {\r\n                    return MockApi.testDatasourceConnection(segments[1]);\r\n                }\r\n                else if (segments.length === 3 && segments[2] === 'execute') {\r\n                    return MockApi.executeQuery(segments[1], data);\r\n                }\r\n                break;\r\n            case 'PUT':\r\n                if (segments.length === 2) {\r\n                    return MockApi.updateDatasource(segments[1], data);\r\n                }\r\n                break;\r\n            case 'DELETE':\r\n                if (segments.length === 2) {\r\n                    return MockApi.deleteDatasource(segments[1]);\r\n                }\r\n                break;\r\n        }\r\n        throw new Error(`Mock API route not found: ${method} ${path}`);\r\n    }\r\n    async handlePluginMockRequest(method, path, data) {\r\n        const segments = path.split('/').filter(Boolean);\r\n        switch (method.toUpperCase()) {\r\n            case 'GET':\r\n                if (segments.length === 1) {\r\n                    return MockApi.getPlugins(data);\r\n                }\r\n                else if (segments.length === 2) {\r\n                    return MockApi.getPlugin(segments[1]);\r\n                }\r\n                break;\r\n        }\r\n        throw new Error(`Mock API route not found: ${method} ${path}`);\r\n    }\r\n    async handleQueryMockRequest(method, path, data) {\r\n        const segments = path.split('/').filter(Boolean);\r\n        switch (method.toUpperCase()) {\r\n            case 'GET':\r\n                if (segments.length === 1) {\r\n                    return MockApi.getQueries(data);\r\n                }\r\n                break;\r\n            case 'POST':\r\n                if (segments.length === 1) {\r\n                    return MockApi.createQuery(data);\r\n                }\r\n                else if (segments.length === 3 && segments[2] === 'execute') {\r\n                    return MockApi.executeQuery(segments[1], data);\r\n                }\r\n                break;\r\n        }\r\n        throw new Error(`Mock API route not found: ${method} ${path}`);\r\n    }\r\n    async get(url, config) {\r\n        return this.request({ ...config, method: 'GET', url });\r\n    }\r\n    async post(url, data, config) {\r\n        return this.request({ ...config, method: 'POST', url, data });\r\n    }\r\n    async put(url, data, config) {\r\n        return this.request({ ...config, method: 'PUT', url, data });\r\n    }\r\n    async delete(url, config) {\r\n        return this.request({ ...config, method: 'DELETE', url });\r\n    }\r\n}\r\nconst apiClient = new ApiClient();\r\n\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///8876\n\n}")}}]);