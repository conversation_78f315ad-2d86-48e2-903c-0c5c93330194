import React, { Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';

import { LoadingSpinner } from '../components/Common/LoadingSpinner';

// 懒加载页面组件
const DatasourceList = React.lazy(() => import('../pages/DatasourceList'));
const DatasourceDetail = React.lazy(() => import('../pages/DatasourceDetail'));
const DatasourceCreate = React.lazy(() => import('../pages/DatasourceCreate'));
const QueryEditor = React.lazy(() => import('../pages/QueryEditor'));
const PluginManagement = React.lazy(() => import('../pages/PluginManagement'));

// 路由配置接口
export interface RouteConfig {
  path: string;
  element: React.ReactNode;
  title: string;
  icon?: string;
  children?: RouteConfig[];
  meta?: {
    requireAuth?: boolean;
    roles?: string[];
    hideInMenu?: boolean;
  };
}

// 路由配置
export const routeConfig: RouteConfig[] = [
  {
    path: '/',
    element: <Navigate to="/datasources" replace />,
    title: '首页'
  },
  {
    path: '/datasources',
    element: <DatasourceList />,
    title: '数据源管理',
    icon: 'DatabaseOutlined',
    children: [
      {
        path: '/datasources',
        element: <DatasourceList />,
        title: '数据源列表'
      },
      {
        path: '/datasources/create',
        element: <DatasourceCreate />,
        title: '新建数据源',
        meta: {
          hideInMenu: true
        }
      },
      {
        path: '/datasources/:id',
        element: <DatasourceDetail />,
        title: '数据源详情',
        meta: {
          hideInMenu: true
        }
      },
      {
        path: '/datasources/:id/edit',
        element: <DatasourceCreate />,
        title: '编辑数据源',
        meta: {
          hideInMenu: true
        }
      }
    ]
  },
  {
    path: '/query',
    element: <QueryEditor />,
    title: '查询编辑器',
    icon: 'CodeOutlined',
    children: [
      {
        path: '/query',
        element: <QueryEditor />,
        title: '查询编辑器'
      },
      {
        path: '/query/:datasourceId',
        element: <QueryEditor />,
        title: '查询编辑器',
        meta: {
          hideInMenu: true
        }
      }
    ]
  },
  {
    path: '/plugins',
    element: <PluginManagement />,
    title: '插件管理',
    icon: 'AppstoreOutlined'
  }
];

// 路由组件
export const AppRoutes: React.FC = () => {
  return (
    <Suspense fallback={<LoadingSpinner text="页面加载中..." />}>
      <Routes>
        {/* 默认重定向到数据源列表 */}
        <Route path="/" element={<Navigate to="/datasources" replace />} />
        
        {/* 数据源管理路由 */}
        <Route path="/datasources" element={<DatasourceList />} />
        <Route path="/datasources/create" element={<DatasourceCreate />} />
        <Route path="/datasources/:id" element={<DatasourceDetail />} />
        <Route path="/datasources/:id/edit" element={<DatasourceCreate />} />
        
        {/* 查询编辑器路由 */}
        <Route path="/query" element={<QueryEditor />} />
        <Route path="/query/:datasourceId" element={<QueryEditor />} />
        
        {/* 插件管理路由 */}
        <Route path="/plugins" element={<PluginManagement />} />
        
        {/* 404 页面 */}
        <Route path="*" element={<Navigate to="/datasources" replace />} />
      </Routes>
    </Suspense>
  );
};

// 获取路由面包屑
export const getBreadcrumbFromPath = (pathname: string): Array<{ title: string; path?: string }> => {
  const segments = pathname.split('/').filter(Boolean);
  const breadcrumb: Array<{ title: string; path?: string }> = [];

  // 根据路径生成面包屑
  if (segments.length === 0) {
    return [{ title: '首页', path: '/' }];
  }

  // 数据源相关路由
  if (segments[0] === 'datasources') {
    breadcrumb.push({ title: '数据源管理', path: '/datasources' });
    
    if (segments.length === 1) {
      // /datasources
      breadcrumb.push({ title: '数据源列表' });
    } else if (segments[1] === 'create') {
      // /datasources/create
      breadcrumb.push({ title: '新建数据源' });
    } else if (segments.length === 2) {
      // /datasources/:id
      breadcrumb.push({ title: '数据源详情' });
    } else if (segments[2] === 'edit') {
      // /datasources/:id/edit
      breadcrumb.push({ title: '数据源详情', path: `/datasources/${segments[1]}` });
      breadcrumb.push({ title: '编辑数据源' });
    }
  }
  
  // 查询编辑器路由
  else if (segments[0] === 'query') {
    breadcrumb.push({ title: '查询编辑器', path: '/query' });
    
    if (segments.length === 2) {
      // /query/:datasourceId
      breadcrumb.push({ title: '数据源查询' });
    }
  }
  
  // 插件管理路由
  else if (segments[0] === 'plugins') {
    breadcrumb.push({ title: '插件管理' });
  }
  
  // 其他路由
  else {
    breadcrumb.push({ title: '未知页面' });
  }

  return breadcrumb;
};

// 获取当前路由的标题
export const getTitleFromPath = (pathname: string): string => {
  const breadcrumb = getBreadcrumbFromPath(pathname);
  return breadcrumb[breadcrumb.length - 1]?.title || 'PagePlug 数据源管理';
};

// 检查路由是否需要认证
export const requiresAuth = (pathname: string): boolean => {
  // 目前所有路由都不需要特殊认证
  return false;
};

// 检查用户是否有权限访问路由
export const hasPermission = (pathname: string, userRoles: string[] = []): boolean => {
  // 目前所有路由都允许访问
  return true;
};

// 获取菜单项（用于侧边栏）
export const getMenuItems = () => {
  return routeConfig
    .filter(route => !route.meta?.hideInMenu && route.path !== '/')
    .map(route => ({
      key: route.path,
      label: route.title,
      icon: route.icon,
      children: route.children
        ?.filter(child => !child.meta?.hideInMenu)
        ?.map(child => ({
          key: child.path,
          label: child.title
        }))
    }));
};

export default AppRoutes;
