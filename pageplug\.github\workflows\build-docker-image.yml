name: Appsmith Build Docker Image Workflow

on:
  # This line enables manual triggering of this workflow.
  workflow_dispatch:
  workflow_call:
    inputs:
      pr:
        description: "This is the PR number in case the workflow is being called in a pull request"
        required: false
        type: number

jobs:
  build-docker:
    runs-on: ubuntu-latest
    if: |
      github.event.pull_request.head.repo.full_name == github.repository ||
      github.event_name == 'push' ||
      github.event_name == 'workflow_dispatch' ||
      github.event_name == 'repository_dispatch' ||
      github.event_name == 'schedule' 
    defaults:
      run:
        shell: bash

    steps:
      # Check out merge commit
      - name: Fork based /ok-to-test checkout
        if: inputs.pr != 0
        uses: actions/checkout@v4
        with:
          ref: "refs/pull/${{ inputs.pr }}/merge"

      # Checkout the code in the current branch in case the workflow is called because of a branch push event
      - name: Checkout the head commit of the branch
        if: inputs.pr == 0
        uses: actions/checkout@v4

      # Setup Java
      - name: Set up JDK 17
        if: steps.run_result.outputs.run_result != 'success'
        uses: actions/setup-java@v3
        with:
          distribution: 'temurin'
          java-version: '17'

      - name: Download the client build artifact
        if: steps.run_result.outputs.run_result != 'success'
        uses: actions/download-artifact@v3
        with:
          name: client-build
          path: app/client

      - name: Unpack the client build artifact
        if: steps.run_result.outputs.run_result != 'success'
        run: |
          mkdir -p app/client/build
          tar -xvf app/client/build.tar -C app/client/build

      - name: Download the server build artifact
        if: steps.run_result.outputs.run_result != 'success'
        uses: actions/download-artifact@v3
        with:
          name: server-build
          path: app/server/dist/

      - name: Download the rts build artifact
        if: steps.run_result.outputs.run_result != 'success'
        uses: actions/download-artifact@v3
        with:
          name: rts-dist
          path: app/client/packages/rts/dist

      - name: Un-tar the rts folder
        run: |
          tar -xvf app/client/packages/rts/dist/rts-dist.tar -C app/client/packages/rts/
          echo "Cleaning up the tar files"
          rm app/client/packages/rts/dist/rts-dist.tar

      - name: Generate info.json
        run: |
          if [[ -f scripts/generate_info_json.sh ]]; then
            scripts/generate_info_json.sh
          fi

      # We don't use Depot Docker builds because it's faster for local Docker images to be built locally.
      # It's slower and more expensive to build these Docker images on Depot and download it back to the CI node.
      - name: Build docker image
        if: steps.run_result.outputs.run_result != 'success'
        working-directory: "."
        run: |
          set -o xtrace
          declare -a args
          if [[ "${{ inputs.pr }}" != 0 || "${{ github.ref_name }}" != master ]]; then
            args+=(--build-arg "APPSMITH_CLOUD_SERVICES_BASE_URL=https://release-cs.appsmith.com")
            base_tag=release
          else
            base_tag=nightly
          fi
          args+=(--build-arg "BASE=${{ vars.DOCKER_HUB_ORGANIZATION }}/base-${{ vars.EDITION }}:$base_tag")
          docker build -t cicontainer "${args[@]}" .

      # Saving the docker image to tar file
      - name: Save Docker image to tar file
        run: |
          docker save cicontainer -o cicontainer.tar
          gzip cicontainer.tar

      - name: Cache docker image
        uses: actions/cache/save@v3
        with:
          path: cicontainer.tar.gz
          key: docker-image-${{github.run_id}}

      - name: Save the status of the run
        run: echo "run_result=success" >> $GITHUB_OUTPUT > ~/run_result
