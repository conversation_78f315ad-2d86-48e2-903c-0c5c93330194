{"definitions": {"Exposes": {"description": "Modules that should be exposed by this container. Property names are used as public paths.", "type": "object", "additionalProperties": {"description": "Modules that should be exposed by this container.", "anyOf": [{"$ref": "#/definitions/ExposesConfig"}, {"$ref": "#/definitions/ExposesItem"}]}}, "ExposesConfig": {"description": "Advanced configuration for modules that should be exposed by this container.", "type": "object", "additionalProperties": false, "properties": {"import": {"$ref": "#/definitions/ExposesItem"}, "name": {"description": "Custom chunk name for the exposed module.", "type": "string"}}, "required": ["import"]}, "ExposesItem": {"description": "Module that should be exposed by this container.", "type": "string", "minLength": 1}, "LibraryName": {"description": "The name of the library (some types allow unnamed libraries too).", "type": "string", "minLength": 1}, "LibraryOptions": {"description": "Options for library.", "type": "object", "additionalProperties": false, "properties": {"name": {"$ref": "#/definitions/LibraryName"}, "type": {"$ref": "#/definitions/LibraryType"}}}, "LibraryType": {"description": "Type of library (types included by default are 'var', 'this', 'window', 'self', 'global', 'commonjs', 'commonjs2').", "enum": ["var", "this", "window", "self", "global", "commonjs", "commonjs2"]}}, "title": "ModuleFederationPluginOptions", "type": "object", "additionalProperties": false, "properties": {"exposes": {"$ref": "#/definitions/Exposes"}, "filename": {"description": "The filename of the container as relative path inside the `output.path` directory.", "type": "string", "absolutePath": false}, "library": {"$ref": "#/definitions/LibraryOptions"}, "name": {"description": "The name of the container.", "type": "string"}, "shareScope": {"description": "The name of the share scope which is shared with the host (defaults to 'default').", "type": "string", "minLength": 1}}}