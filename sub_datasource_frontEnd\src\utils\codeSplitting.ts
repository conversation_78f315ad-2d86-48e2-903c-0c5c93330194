import React from 'react';
import { LoadingSpinner } from '../components/Common/LoadingSpinner';

/**
 * 增强的懒加载组件工厂
 */
export function createLazyComponent<T extends React.ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  options: {
    fallback?: React.ComponentType;
    retries?: number;
    retryDelay?: number;
    chunkName?: string;
  } = {}
) {
  const {
    fallback: Fallback = LoadingSpinner,
    retries = 3,
    retryDelay = 1000,
    chunkName
  } = options;

  const LazyComponent = React.lazy(async () => {
    let attempt = 0;
    
    while (attempt < retries) {
      try {
        const module = await importFunc();
        
        // 记录成功加载
        if (process.env.NODE_ENV === 'development') {
          console.log(`[Code Splitting] Successfully loaded chunk: ${chunkName || 'unknown'}`);
        }
        
        return module;
      } catch (error) {
        attempt++;
        
        if (attempt >= retries) {
          console.error(`[Code Splitting] Failed to load chunk after ${retries} attempts:`, error);
          
          // 返回错误组件
          return {
            default: () => React.createElement('div', {
              style: {
                padding: '20px',
                textAlign: 'center',
                color: '#ff4d4f'
              }
            }, `加载组件失败: ${chunkName || 'unknown'}`)
          };
        }
        
        console.warn(`[Code Splitting] Attempt ${attempt} failed, retrying in ${retryDelay}ms:`, error);
        await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
      }
    }
    
    throw new Error('Unreachable code');
  });

  // 包装组件以提供更好的错误处理
  const WrappedComponent = React.forwardRef<any, any>((props, ref) => {
    return React.createElement(
      React.Suspense,
      { fallback: React.createElement(Fallback) },
      React.createElement(LazyComponent, { ...props, ref })
    );
  });

  WrappedComponent.displayName = `LazyComponent(${chunkName || 'Unknown'})`;
  
  return WrappedComponent;
}

/**
 * 预加载组件
 */
export function preloadComponent(importFunc: () => Promise<any>) {
  // 在空闲时间预加载
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
      importFunc().catch(error => {
        console.warn('[Preload] Failed to preload component:', error);
      });
    });
  } else {
    // 降级到 setTimeout
    setTimeout(() => {
      importFunc().catch(error => {
        console.warn('[Preload] Failed to preload component:', error);
      });
    }, 100);
  }
}

/**
 * 路由级别的代码分割
 */
export const RouteComponents = {
  // 数据源管理相关页面
  DatasourceList: createLazyComponent(
    () => import('../pages/DatasourceList'),
    { chunkName: 'DatasourceList' }
  ),
  
  DatasourceDetail: createLazyComponent(
    () => import('../pages/DatasourceDetail'),
    { chunkName: 'DatasourceDetail' }
  ),
  
  DatasourceCreate: createLazyComponent(
    () => import('../pages/DatasourceCreate'),
    { chunkName: 'DatasourceCreate' }
  ),
  
  // 查询编辑器
  QueryEditor: createLazyComponent(
    () => import('../pages/QueryEditor'),
    { chunkName: 'QueryEditor' }
  ),
  
  // 插件管理
  PluginManagement: createLazyComponent(
    () => import('../pages/PluginManagement'),
    { chunkName: 'PluginManagement' }
  )
};

/**
 * 组件级别的代码分割
 */
export const LazyComponents = {
  // 复杂的图表组件
  DataVisualization: createLazyComponent(
    () => import('../components/DataVisualization'),
    { chunkName: 'DataVisualization' }
  ),
  
  // 高级查询编辑器
  AdvancedQueryEditor: createLazyComponent(
    () => import('../components/AdvancedQueryEditor'),
    { chunkName: 'AdvancedQueryEditor' }
  ),
  
  // 数据导出组件
  DataExporter: createLazyComponent(
    () => import('../components/DataExporter'),
    { chunkName: 'DataExporter' }
  )
};

/**
 * 智能预加载策略
 */
export class PreloadStrategy {
  private static instance: PreloadStrategy;
  private preloadedChunks = new Set<string>();
  private preloadQueue: Array<() => Promise<any>> = [];
  private isPreloading = false;

  static getInstance(): PreloadStrategy {
    if (!PreloadStrategy.instance) {
      PreloadStrategy.instance = new PreloadStrategy();
    }
    return PreloadStrategy.instance;
  }

  /**
   * 根据用户行为预加载相关组件
   */
  preloadByUserBehavior(currentRoute: string) {
    const preloadMap: Record<string, string[]> = {
      '/datasources': ['DatasourceDetail', 'DatasourceCreate'],
      '/datasources/create': ['DatasourceList'],
      '/query': ['DataVisualization', 'AdvancedQueryEditor'],
      '/plugins': ['DatasourceCreate']
    };

    const chunksToPreload = preloadMap[currentRoute] || [];
    chunksToPreload.forEach(chunkName => {
      this.addToPreloadQueue(chunkName);
    });

    this.processPreloadQueue();
  }

  /**
   * 基于网络状况的智能预加载
   */
  preloadByNetworkCondition() {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      
      // 只在良好的网络条件下预加载
      if (connection.effectiveType === '4g' && !connection.saveData) {
        this.preloadCriticalChunks();
      }
    } else {
      // 无法检测网络状况时，延迟预加载
      setTimeout(() => {
        this.preloadCriticalChunks();
      }, 2000);
    }
  }

  /**
   * 预加载关键组件
   */
  private preloadCriticalChunks() {
    const criticalChunks = [
      'DatasourceDetail',
      'QueryEditor'
    ];

    criticalChunks.forEach(chunkName => {
      this.addToPreloadQueue(chunkName);
    });

    this.processPreloadQueue();
  }

  /**
   * 添加到预加载队列
   */
  private addToPreloadQueue(chunkName: string) {
    if (this.preloadedChunks.has(chunkName)) {
      return;
    }

    const importFunc = this.getImportFunction(chunkName);
    if (importFunc) {
      this.preloadQueue.push(importFunc);
    }
  }

  /**
   * 处理预加载队列
   */
  private async processPreloadQueue() {
    if (this.isPreloading || this.preloadQueue.length === 0) {
      return;
    }

    this.isPreloading = true;

    while (this.preloadQueue.length > 0) {
      const importFunc = this.preloadQueue.shift();
      if (importFunc) {
        try {
          await importFunc();
          console.log('[Preload] Successfully preloaded chunk');
        } catch (error) {
          console.warn('[Preload] Failed to preload chunk:', error);
        }
        
        // 在预加载之间添加小延迟，避免阻塞主线程
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    this.isPreloading = false;
  }

  /**
   * 获取导入函数
   */
  private getImportFunction(chunkName: string): (() => Promise<any>) | null {
    const importMap: Record<string, () => Promise<any>> = {
      DatasourceList: () => import('../pages/DatasourceList'),
      DatasourceDetail: () => import('../pages/DatasourceDetail'),
      DatasourceCreate: () => import('../pages/DatasourceCreate'),
      QueryEditor: () => import('../pages/QueryEditor'),
      PluginManagement: () => import('../pages/PluginManagement'),
      DataVisualization: () => import('../components/DataVisualization'),
      AdvancedQueryEditor: () => import('../components/AdvancedQueryEditor'),
      DataExporter: () => import('../components/DataExporter')
    };

    return importMap[chunkName] || null;
  }
}

/**
 * 初始化代码分割优化
 */
export function initCodeSplitting() {
  const preloadStrategy = PreloadStrategy.getInstance();
  
  // 监听路由变化进行智能预加载
  window.addEventListener('popstate', () => {
    const currentRoute = window.location.pathname;
    preloadStrategy.preloadByUserBehavior(currentRoute);
  });

  // 基于网络状况预加载
  preloadStrategy.preloadByNetworkCondition();

  console.log('[Code Splitting] Optimization initialized');
}

// 导出单例实例
export const preloadStrategy = PreloadStrategy.getInstance();
