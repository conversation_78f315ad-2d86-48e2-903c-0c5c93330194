/* TailwindCSS 基础样式 */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5;
}

body {
  margin: 0;
  background-color: #f5f5f5;
  color: #333;
}

/* 代码字体 */
code {
  font-family: 'JetBrains Mono', 'Consolas', 'Monaco', 'Courier New', monospace;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Ant Design 样式覆盖 */
.ant-layout {
  background: transparent !important;
}

.ant-layout-header {
  background: #fff !important;
  border-bottom: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  padding: 0 24px;
  height: 64px;
  line-height: 64px;
}

.ant-layout-sider {
  background: #fff !important;
  border-right: 1px solid #f0f0f0;
}

.ant-menu {
  border-right: none !important;
}

.ant-menu-item {
  margin: 4px 8px !important;
  border-radius: 6px !important;
  width: auto !important;
}

.ant-menu-item-selected {
  background-color: #e6f7ff !important;
  color: #1890ff !important;
}

.ant-menu-item:hover {
  background-color: #f5f5f5 !important;
}

/* 表格样式优化 */
.ant-table {
  border-radius: 8px;
  overflow: hidden;
}

.ant-table-thead > tr > th {
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 600;
}

.ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

/* 表单样式优化 */
.ant-form-item-label > label {
  font-weight: 500;
}

.ant-input,
.ant-select-selector,
.ant-input-number {
  border-radius: 6px;
}

.ant-input:focus,
.ant-select-focused .ant-select-selector,
.ant-input-number:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 按钮样式优化 */
.ant-btn {
  border-radius: 6px;
  font-weight: 500;
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.02);
}

.ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

/* 卡片样式优化 */
.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
}

/* 模态框样式优化 */
.ant-modal {
  border-radius: 8px;
  overflow: hidden;
}

.ant-modal-header {
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

/* 标签样式优化 */
.ant-tag {
  border-radius: 4px;
  font-weight: 500;
}

/* 通知样式优化 */
.ant-notification {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 自定义工具类 */
.tw-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.tw-slide-up {
  animation: slideUp 0.3s ease-out;
}

.tw-hover-lift {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.tw-hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 响应式工具类 */
@media (max-width: 768px) {
  .ant-layout-content {
    padding: 16px;
  }
  
  .ant-table {
    font-size: 12px;
  }
  
  .ant-btn {
    font-size: 12px;
    height: 32px;
    padding: 0 12px;
  }
}

/* 暗色主题支持 */
.dark {
  .ant-layout-header {
    background: #1f1f1f !important;
    border-bottom-color: #303030;
  }
  
  .ant-layout-sider {
    background: #1f1f1f !important;
    border-right-color: #303030;
  }
  
  .ant-menu {
    background: transparent !important;
    color: #fff;
  }
  
  .ant-menu-item {
    color: #fff;
  }
  
  .ant-menu-item:hover {
    background-color: #262626 !important;
  }
  
  .ant-menu-item-selected {
    background-color: #1890ff !important;
  }
}
