name: <PERSON><PERSON> Charts Publish

# Package & publish new version for <PERSON><PERSON> chart to S3 bucket.

on:
  push:
    branches:
      - master
    paths:
      - "deploy/helm/**"

  workflow_dispatch:

jobs:
  publish:
    runs-on: ubuntu-latest

    defaults:
      run:
        working-directory: deploy/helm
        shell: bash

    steps:
      - name: Checkout the code
        uses: actions/checkout@v4

      - name: Setup He<PERSON>
        uses: azure/setup-helm@v1
        with:
          version: v3.6.3

      - name: Get the chart version
        id: chart-version
        run: |
          set -o xtrace
          cat "${{ github.workspace }}/deploy/helm/Chart.yaml"
          chart_version="$(awk '$1 == "version:" {print $2}' "${{ github.workspace }}/deploy/helm/Chart.yaml" | head -1 )"
          echo "version=$chart_version" >> $GITHUB_OUTPUT
      # Scan the S3 bucket with the Helm version retrieved from Chart.yaml
      # If an archive with this version already exists in the bucket, fail immediately.
      - name: Check chart version is not already published
        env:
          AWS_ACCESS_KEY_ID: "${{ secrets.HELM_AWS_ACCESS_KEY_ID }}"
          AWS_SECRET_ACCESS_KEY: "${{ secrets.HELM_AWS_SECRET_ACCESS_KEY }}"
          AWS_EC2_METADATA_DISABLED: true
        run: |
          set -o xtrace
          chart_version="${{ steps.chart-version.outputs.version }}"
          if [[ -z $chart_version ]]; then
            echo "Empty chart version from Chart.yaml. Exiting." >&2
            exit 1
          fi
          archive="appsmith-$chart_version.tgz"
          if [[ "$archive" == "$(aws s3api list-objects --bucket '${{ secrets.HELM_S3_BUCKET }}' --prefix "$archive" --query 'Contents[0].Key' --output text)" ]]; then
            echo "$archive is already present in the Helm bucket. Please change the version number in 'Chart.yaml' file." >&2
            exit 1
          fi
      - name: Publish Helm
        env:
          AWS_ACCESS_KEY_ID: "${{ secrets.HELM_AWS_ACCESS_KEY_ID }}"
          AWS_SECRET_ACCESS_KEY: "${{ secrets.HELM_AWS_SECRET_ACCESS_KEY }}"
          # Not really sure why this is needed, but without it, we see the error:
          #   <botocore.awsrequest.AWSRequest object at 0x7fde607adac0>
          #   Error: Process completed with exit code 255.
          AWS_EC2_METADATA_DISABLED: true
        run: |
          echo "Publishing new Helm chart version"
          helm repo add bitnami https://charts.bitnami.com/bitnami
          helm dep build
          helm package .
          aws s3 cp s3://${{ secrets.HELM_S3_BUCKET }}/index.yaml .
          helm repo index . --url https://${{ secrets.HELM_S3_BUCKET }} --merge index.yaml
          aws s3 cp appsmith-${{ steps.chart-version.outputs.version }}.tgz s3://${{ secrets.HELM_S3_BUCKET }}
          aws s3 cp index.yaml s3://${{ secrets.HELM_S3_BUCKET }}
