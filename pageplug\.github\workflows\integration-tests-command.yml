name: <PERSON><PERSON>mith External Integration Test Workflow

on:
  # This workflow is only triggered by the ok to test command dispatch
  repository_dispatch:
    types: [ok-to-test-command]

jobs:
  tag-check:
    runs-on: ubuntu-latest
    outputs:
      tags: ${{ steps.checkAll.outputs.tags }}
      matrix: ${{ steps.checkAll.outputs.matrix }}
    steps:
      - name: Add a comment on the PR mentioning that this method of running ok-to-test is now defunct
        uses: peter-evans/create-or-update-comment@v3
        with:
          issue-number: ${{ github.event.client_payload.pull_request.number }}
          body: |
            Whoops! Looks like you're using an outdated method of running the Cypress suite. 
            Please check this <a href="https://www.notion.so/appsmith/Changes-to-triggering-Cypress-tests-in-PRs-9c14598a1dd94d13a17610e1473e514f" target="_blank">doc</a> to learn how to correct this!

      
