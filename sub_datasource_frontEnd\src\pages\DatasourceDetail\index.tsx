import React, { useState, useEffect } from 'react';
import {
  Card,
  Descriptions,
  Button,
  Space,
  Tag,
  Tabs,
  Table,
  Tree,
  Alert,
  Spin,
  message,
  Modal,
  Typography
} from 'antd';
import {
  EditOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  DatabaseOutlined,
  TableOutlined,
  CodeOutlined
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import dayjs from 'dayjs';

import { Datasource, DatasourceStructure, ConnectionTestResult } from '../../types/api';
import { apiClient } from '../../services/apiClient';
import { LoadingSpinner } from '../../components/Common/LoadingSpinner';
import { useTheme } from '../../hooks/useTheme';

const { TabPane } = Tabs;
const { Text, Paragraph } = Typography;
const { confirm } = Modal;

// 样式化组件
const PageContainer = styled.div`
  padding: 24px;
`;

const PageHeader = styled.div`
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
`;

const TitleSection = styled.div``;

const PageTitle = styled.h1<{ $isDark: boolean }>`
  font-size: 24px;
  font-weight: 600;
  color: ${props => props.$isDark ? '#ffffff' : '#262626'};
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
`;

const StatusIndicator = styled.div<{ $status: 'valid' | 'invalid' | 'unknown' }>`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 500;
  
  ${props => {
    switch (props.$status) {
      case 'valid':
        return `
          background-color: #f6ffed;
          border: 1px solid #b7eb8f;
          color: #52c41a;
        `;
      case 'invalid':
        return `
          background-color: #fff2f0;
          border: 1px solid #ffccc7;
          color: #ff4d4f;
        `;
      default:
        return `
          background-color: #fafafa;
          border: 1px solid #d9d9d9;
          color: #8c8c8c;
        `;
    }
  }}
`;

const ActionSection = styled.div``;

const TabContent = styled.div`
  padding: 16px 0;
`;

interface DatasourceDetailProps {}

const DatasourceDetail: React.FC<DatasourceDetailProps> = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { isDark } = useTheme();

  const [datasource, setDatasource] = useState<Datasource | null>(null);
  const [structure, setStructure] = useState<DatasourceStructure | null>(null);
  const [loading, setLoading] = useState(true);
  const [structureLoading, setStructureLoading] = useState(false);
  const [testResult, setTestResult] = useState<ConnectionTestResult | null>(null);
  const [testing, setTesting] = useState(false);

  // 加载数据源详情
  const loadDatasource = async () => {
    if (!id) return;

    try {
      setLoading(true);
      const response = await apiClient.get(`/datasources/${id}`);
      setDatasource(response.data);
    } catch (error) {
      message.error('加载数据源详情失败');
      console.error('Load datasource error:', error);
    } finally {
      setLoading(false);
    }
  };

  // 加载数据源结构
  const loadStructure = async () => {
    if (!id) return;

    try {
      setStructureLoading(true);
      const response = await apiClient.get(`/datasources/${id}/structure`);
      setStructure(response.data);
    } catch (error) {
      message.error('加载数据源结构失败');
      console.error('Load structure error:', error);
    } finally {
      setStructureLoading(false);
    }
  };

  // 测试连接
  const handleTestConnection = async () => {
    if (!id) return;

    try {
      setTesting(true);
      const response = await apiClient.post(`/datasources/${id}/test`);
      setTestResult(response.data);
      
      if (response.data.success) {
        message.success(`连接成功 (${response.data.responseTime}ms)`);
      } else {
        message.error(`连接失败: ${response.data.message}`);
      }
    } catch (error) {
      message.error('连接测试失败');
      setTestResult({
        success: false,
        message: '连接测试失败',
        responseTime: 0
      });
    } finally {
      setTesting(false);
    }
  };

  // 删除数据源
  const handleDelete = () => {
    if (!datasource) return;

    confirm({
      title: '确认删除',
      content: `确定要删除数据源 "${datasource.name}" 吗？此操作不可恢复。`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await apiClient.delete(`/datasources/${id}`);
          message.success('数据源删除成功');
          navigate('/datasources');
        } catch (error) {
          message.error('删除数据源失败');
        }
      }
    });
  };

  useEffect(() => {
    loadDatasource();
  }, [id]);

  if (loading) {
    return <LoadingSpinner text="加载数据源详情中..." />;
  }

  if (!datasource) {
    return (
      <PageContainer>
        <Alert
          message="数据源不存在"
          description="请检查数据源ID是否正确"
          type="error"
          showIcon
        />
      </PageContainer>
    );
  }

  // 构建结构树数据
  const buildTreeData = () => {
    if (!structure) return [];

    return structure.tables.map(table => ({
      title: (
        <Space>
          <TableOutlined />
          <span>{table.name}</span>
          <Tag size="small">{table.type}</Tag>
        </Space>
      ),
      key: table.name,
      children: table.columns.map(column => ({
        title: (
          <Space>
            <span>{column.name}</span>
            <Tag size="small" color="blue">{column.type}</Tag>
            {column.isPrimaryKey && <Tag size="small" color="gold">PK</Tag>}
            {column.isAutoIncrement && <Tag size="small" color="green">AI</Tag>}
            {!column.isNullable && <Tag size="small" color="red">NOT NULL</Tag>}
          </Space>
        ),
        key: `${table.name}.${column.name}`,
        isLeaf: true
      }))
    }));
  };

  // 表格列定义
  const structureColumns = [
    {
      title: '列名',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (text: string) => <Tag color="blue">{text}</Tag>
    },
    {
      title: '属性',
      key: 'attributes',
      render: (_: any, record: any) => (
        <Space>
          {record.isPrimaryKey && <Tag color="gold">主键</Tag>}
          {record.isAutoIncrement && <Tag color="green">自增</Tag>}
          {!record.isNullable && <Tag color="red">非空</Tag>}
        </Space>
      )
    },
    {
      title: '默认值',
      dataIndex: 'defaultValue',
      key: 'defaultValue',
      render: (text: any) => text || '-'
    }
  ];

  return (
    <PageContainer>
      <PageHeader>
        <TitleSection>
          <PageTitle $isDark={isDark}>
            <DatabaseOutlined />
            {datasource.name}
            <StatusIndicator
              $status={
                datasource.isConfigured
                  ? datasource.isValid
                    ? 'valid'
                    : 'invalid'
                  : 'unknown'
              }
            >
              {datasource.isConfigured
                ? datasource.isValid
                  ? <CheckCircleOutlined />
                  : <ExclamationCircleOutlined />
                : <ExclamationCircleOutlined />}
              {datasource.isConfigured
                ? datasource.isValid
                  ? '连接正常'
                  : '连接异常'
                : '未配置'}
            </StatusIndicator>
          </PageTitle>
          <Text type="secondary">
            {datasource.pluginName} • 创建于 {dayjs(datasource.createdAt).format('YYYY-MM-DD HH:mm')}
          </Text>
        </TitleSection>

        <ActionSection>
          <Space>
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              loading={testing}
              onClick={handleTestConnection}
            >
              测试连接
            </Button>
            <Button
              icon={<EditOutlined />}
              onClick={() => navigate(`/datasources/${id}/edit`)}
            >
              编辑
            </Button>
            <Button
              icon={<DeleteOutlined />}
              danger
              onClick={handleDelete}
            >
              删除
            </Button>
          </Space>
        </ActionSection>
      </PageHeader>

      {/* 连接测试结果 */}
      {testResult && (
        <Alert
          message={testResult.success ? '连接测试成功' : '连接测试失败'}
          description={
            <div>
              <div>{testResult.message}</div>
              <div>响应时间: {testResult.responseTime}ms</div>
              {testResult.details && (
                <div>
                  详细信息: {JSON.stringify(testResult.details, null, 2)}
                </div>
              )}
            </div>
          }
          type={testResult.success ? 'success' : 'error'}
          showIcon
          closable
          style={{ marginBottom: 24 }}
        />
      )}

      <Tabs defaultActiveKey="basic">
        <TabPane tab="基本信息" key="basic">
          <TabContent>
            <Card title="数据源配置">
              <Descriptions column={2} bordered>
                <Descriptions.Item label="数据源名称">
                  {datasource.name}
                </Descriptions.Item>
                <Descriptions.Item label="插件类型">
                  <Tag color="blue">{datasource.pluginName}</Tag>
                </Descriptions.Item>
                <Descriptions.Item label="连接地址">
                  {datasource.datasourceConfiguration.url}
                </Descriptions.Item>
                <Descriptions.Item label="数据库名">
                  {datasource.datasourceConfiguration.databaseName || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="认证方式">
                  {datasource.datasourceConfiguration.authentication?.authType || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="用户名">
                  {datasource.datasourceConfiguration.authentication?.username || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="SSL连接">
                  {datasource.datasourceConfiguration.connection?.ssl?.enabled ? '启用' : '禁用'}
                </Descriptions.Item>
                <Descriptions.Item label="连接超时">
                  {datasource.datasourceConfiguration.connection?.timeout || '-'}ms
                </Descriptions.Item>
                <Descriptions.Item label="创建时间">
                  {dayjs(datasource.createdAt).format('YYYY-MM-DD HH:mm:ss')}
                </Descriptions.Item>
                <Descriptions.Item label="更新时间">
                  {dayjs(datasource.updatedAt).format('YYYY-MM-DD HH:mm:ss')}
                </Descriptions.Item>
                <Descriptions.Item label="最后使用">
                  {datasource.lastUsed
                    ? dayjs(datasource.lastUsed).format('YYYY-MM-DD HH:mm:ss')
                    : '从未使用'}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </TabContent>
        </TabPane>

        <TabPane tab="数据结构" key="structure">
          <TabContent>
            <Card
              title="数据库结构"
              extra={
                <Button
                  icon={<ReloadOutlined />}
                  loading={structureLoading}
                  onClick={loadStructure}
                >
                  刷新结构
                </Button>
              }
            >
              {!structure ? (
                <div style={{ textAlign: 'center', padding: '40px 0' }}>
                  <Button
                    type="primary"
                    icon={<ReloadOutlined />}
                    loading={structureLoading}
                    onClick={loadStructure}
                  >
                    加载数据结构
                  </Button>
                </div>
              ) : structureLoading ? (
                <Spin size="large" style={{ display: 'block', textAlign: 'center', padding: '40px 0' }} />
              ) : (
                <Tabs type="card">
                  <TabPane tab="树形视图" key="tree">
                    <Tree
                      treeData={buildTreeData()}
                      defaultExpandAll
                      showIcon
                    />
                  </TabPane>
                  {structure.tables.map(table => (
                    <TabPane tab={table.name} key={table.name}>
                      <Table
                        columns={structureColumns}
                        dataSource={table.columns}
                        rowKey="name"
                        pagination={false}
                        size="small"
                      />
                    </TabPane>
                  ))}
                </Tabs>
              )}
            </Card>
          </TabContent>
        </TabPane>

        <TabPane tab="查询编辑器" key="query">
          <TabContent>
            <Card>
              <div style={{ textAlign: 'center', padding: '40px 0' }}>
                <CodeOutlined style={{ fontSize: '48px', color: '#d9d9d9', marginBottom: '16px' }} />
                <div style={{ marginBottom: '16px' }}>
                  <Text type="secondary">在查询编辑器中测试和执行SQL查询</Text>
                </div>
                <Button
                  type="primary"
                  onClick={() => navigate(`/query/${id}`)}
                >
                  打开查询编辑器
                </Button>
              </div>
            </Card>
          </TabContent>
        </TabPane>
      </Tabs>
    </PageContainer>
  );
};

export default DatasourceDetail;
