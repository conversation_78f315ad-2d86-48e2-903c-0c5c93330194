import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Select,
  Switch,
  Button,
  Card,
  Steps,
  Row,
  Col,
  Alert,
  Space,
  Divider,
  message,
  Spin
} from 'antd';
import {
  DatabaseOutlined,
  SettingOutlined,
  CheckCircleOutlined,
  SaveOutlined,
  PlayCircleOutlined,
  ArrowLeftOutlined
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import styled from 'styled-components';

import { Plugin, Datasource, CreateDatasourceRequest, ConnectionTestResult } from '../../types/api';
import { apiClient } from '../../services/apiClient';
import { LoadingSpinner } from '../../components/Common/LoadingSpinner';
import { useTheme } from '../../hooks/useTheme';

const { Option } = Select;
const { TextArea } = Input;
const { Step } = Steps;

// 样式化组件
const PageContainer = styled.div`
  padding: 24px;
  max-width: 1000px;
  margin: 0 auto;
`;

const PageHeader = styled.div`
  margin-bottom: 24px;
`;

const PageTitle = styled.h1<{ $isDark: boolean }>`
  font-size: 24px;
  font-weight: 600;
  color: ${props => props.$isDark ? '#ffffff' : '#262626'};
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
`;

const StepsContainer = styled.div`
  margin-bottom: 32px;
`;

const FormSection = styled(Card)`
  margin-bottom: 24px;
`;

const TestResultContainer = styled.div<{ $success: boolean }>`
  padding: 16px;
  border-radius: 6px;
  margin-top: 16px;
  background-color: ${props => props.$success ? '#f6ffed' : '#fff2f0'};
  border: 1px solid ${props => props.$success ? '#b7eb8f' : '#ffccc7'};
`;

interface DatasourceCreateProps {}

const DatasourceCreate: React.FC<DatasourceCreateProps> = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { isDark } = useTheme();
  const [form] = Form.useForm();

  const [currentStep, setCurrentStep] = useState(0);
  const [plugins, setPlugins] = useState<Plugin[]>([]);
  const [selectedPlugin, setSelectedPlugin] = useState<Plugin | null>(null);
  const [datasource, setDatasource] = useState<Datasource | null>(null);
  const [loading, setLoading] = useState(false);
  const [testing, setTesting] = useState(false);
  const [testResult, setTestResult] = useState<ConnectionTestResult | null>(null);
  const [saving, setSaving] = useState(false);

  const isEdit = !!id;

  // 加载插件列表
  const loadPlugins = async () => {
    try {
      const response = await apiClient.get('/plugins', {
        params: { status: 'active' }
      });
      setPlugins(response.data);
    } catch (error) {
      message.error('加载插件列表失败');
    }
  };

  // 加载数据源详情（编辑模式）
  const loadDatasource = async () => {
    if (!id) return;

    try {
      setLoading(true);
      const response = await apiClient.get(`/datasources/${id}`);
      const ds = response.data;
      setDatasource(ds);

      // 设置表单值
      form.setFieldsValue({
        name: ds.name,
        pluginId: ds.pluginId,
        url: ds.datasourceConfiguration.url,
        databaseName: ds.datasourceConfiguration.databaseName,
        authType: ds.datasourceConfiguration.authentication?.authType,
        username: ds.datasourceConfiguration.authentication?.username,
        sslEnabled: ds.datasourceConfiguration.connection?.ssl?.enabled,
        timeout: ds.datasourceConfiguration.connection?.timeout
      });

      // 设置选中的插件
      const plugin = plugins.find(p => p.id === ds.pluginId);
      setSelectedPlugin(plugin || null);
    } catch (error) {
      message.error('加载数据源详情失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadPlugins();
  }, []);

  useEffect(() => {
    if (plugins.length > 0 && isEdit) {
      loadDatasource();
    }
  }, [plugins, id]);

  // 插件选择处理
  const handlePluginChange = (pluginId: string) => {
    const plugin = plugins.find(p => p.id === pluginId);
    setSelectedPlugin(plugin || null);
    setTestResult(null);

    // 根据插件类型设置默认值
    if (plugin) {
      const defaults: any = {};
      
      switch (plugin.name) {
        case 'mysql':
          defaults.url = 'mysql://localhost:3306';
          defaults.timeout = 30000;
          break;
        case 'postgresql':
          defaults.url = 'postgresql://localhost:5432';
          defaults.timeout = 15000;
          break;
        case 'mongodb':
          defaults.url = 'mongodb://localhost:27017';
          break;
        case 'redis':
          defaults.url = 'redis://localhost:6379';
          break;
        case 'restapi':
          defaults.url = 'https://api.example.com';
          break;
      }

      form.setFieldsValue(defaults);
    }
  };

  // 测试连接
  const handleTestConnection = async () => {
    try {
      await form.validateFields();
      const values = form.getFieldsValue();

      setTesting(true);
      setTestResult(null);

      // 构建测试请求
      const testRequest: CreateDatasourceRequest = {
        name: values.name || 'Test Connection',
        pluginId: values.pluginId,
        workspaceId: 'workspace-123',
        datasourceConfiguration: {
          url: values.url,
          databaseName: values.databaseName,
          authentication: values.authType ? {
            authType: values.authType,
            username: values.username,
            password: values.password
          } : undefined,
          connection: {
            ssl: {
              enabled: values.sslEnabled || false
            },
            timeout: values.timeout
          }
        }
      };

      // 如果是编辑模式，使用现有数据源ID测试
      if (isEdit && id) {
        const response = await apiClient.post(`/datasources/${id}/test`);
        setTestResult(response.data);
      } else {
        // 创建临时数据源进行测试（模拟）
        const mockResult: ConnectionTestResult = {
          success: Math.random() > 0.3,
          message: Math.random() > 0.3 ? '连接成功' : '连接失败：认证错误',
          responseTime: Math.floor(Math.random() * 1000) + 100
        };
        
        await new Promise(resolve => setTimeout(resolve, 1500));
        setTestResult(mockResult);
      }

      if (testResult?.success) {
        message.success('连接测试成功');
        setCurrentStep(2);
      } else {
        message.error('连接测试失败');
      }
    } catch (error) {
      message.error('连接测试失败');
      setTestResult({
        success: false,
        message: '连接测试失败',
        responseTime: 0
      });
    } finally {
      setTesting(false);
    }
  };

  // 保存数据源
  const handleSave = async () => {
    try {
      await form.validateFields();
      const values = form.getFieldsValue();

      setSaving(true);

      const request: CreateDatasourceRequest = {
        name: values.name,
        pluginId: values.pluginId,
        workspaceId: 'workspace-123',
        datasourceConfiguration: {
          url: values.url,
          databaseName: values.databaseName,
          authentication: values.authType ? {
            authType: values.authType,
            username: values.username,
            password: values.password
          } : undefined,
          connection: {
            ssl: {
              enabled: values.sslEnabled || false
            },
            timeout: values.timeout
          }
        }
      };

      if (isEdit && id) {
        await apiClient.put(`/datasources/${id}`, request);
        message.success('数据源更新成功');
      } else {
        const response = await apiClient.post('/datasources', request);
        message.success('数据源创建成功');
        navigate(`/datasources/${response.data.id}`);
        return;
      }

      navigate('/datasources');
    } catch (error) {
      message.error(isEdit ? '更新数据源失败' : '创建数据源失败');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return <LoadingSpinner text="加载数据源信息中..." />;
  }

  const steps = [
    {
      title: '选择插件',
      description: '选择数据源类型',
      icon: <DatabaseOutlined />
    },
    {
      title: '配置连接',
      description: '设置连接参数',
      icon: <SettingOutlined />
    },
    {
      title: '测试连接',
      description: '验证连接配置',
      icon: <CheckCircleOutlined />
    }
  ];

  return (
    <PageContainer>
      <PageHeader>
        <Space>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/datasources')}
          >
            返回
          </Button>
        </Space>
        <PageTitle $isDark={isDark}>
          <DatabaseOutlined />
          {isEdit ? '编辑数据源' : '新建数据源'}
        </PageTitle>
      </PageHeader>

      {!isEdit && (
        <StepsContainer>
          <Steps current={currentStep} items={steps} />
        </StepsContainer>
      )}

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSave}
        initialValues={{
          authType: 'basic',
          sslEnabled: false,
          timeout: 30000
        }}
      >
        {/* 基本信息 */}
        <FormSection title="基本信息">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="数据源名称"
                name="name"
                rules={[
                  { required: true, message: '请输入数据源名称' },
                  { min: 2, max: 50, message: '名称长度为2-50个字符' }
                ]}
              >
                <Input placeholder="请输入数据源名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="插件类型"
                name="pluginId"
                rules={[{ required: true, message: '请选择插件类型' }]}
              >
                <Select
                  placeholder="请选择插件类型"
                  onChange={handlePluginChange}
                  disabled={isEdit}
                >
                  {plugins.map(plugin => (
                    <Option key={plugin.id} value={plugin.id}>
                      <Space>
                        <span>{plugin.displayName}</span>
                        <span style={{ color: '#8c8c8c' }}>v{plugin.version}</span>
                      </Space>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          {selectedPlugin && (
            <Alert
              message={selectedPlugin.displayName}
              description={selectedPlugin.description}
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />
          )}
        </FormSection>

        {/* 连接配置 */}
        {selectedPlugin && (
          <FormSection title="连接配置">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="连接地址"
                  name="url"
                  rules={[{ required: true, message: '请输入连接地址' }]}
                >
                  <Input placeholder="例如: mysql://localhost:3306" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="数据库名" name="databaseName">
                  <Input placeholder="请输入数据库名（可选）" />
                </Form.Item>
              </Col>
            </Row>

            <Divider orientation="left">认证配置</Divider>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item label="认证方式" name="authType">
                  <Select>
                    <Option value="basic">用户名密码</Option>
                    <Option value="oauth">OAuth</Option>
                    <Option value="apikey">API Key</Option>
                    <Option value="bearer">Bearer Token</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="用户名" name="username">
                  <Input placeholder="请输入用户名" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="密码" name="password">
                  <Input.Password placeholder="请输入密码" />
                </Form.Item>
              </Col>
            </Row>

            <Divider orientation="left">高级配置</Divider>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item label="启用SSL" name="sslEnabled" valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="连接超时(ms)" name="timeout">
                  <Input type="number" placeholder="30000" />
                </Form.Item>
              </Col>
            </Row>
          </FormSection>
        )}

        {/* 测试结果 */}
        {testResult && (
          <FormSection title="连接测试结果">
            <TestResultContainer $success={testResult.success}>
              <div style={{ marginBottom: 8 }}>
                <strong>
                  {testResult.success ? '✅ 连接成功' : '❌ 连接失败'}
                </strong>
              </div>
              <div>消息: {testResult.message}</div>
              <div>响应时间: {testResult.responseTime}ms</div>
              {testResult.details && (
                <div style={{ marginTop: 8 }}>
                  <details>
                    <summary>详细信息</summary>
                    <pre style={{ marginTop: 8, fontSize: '12px' }}>
                      {JSON.stringify(testResult.details, null, 2)}
                    </pre>
                  </details>
                </div>
              )}
            </TestResultContainer>
          </FormSection>
        )}

        {/* 操作按钮 */}
        <Card>
          <Space>
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              loading={testing}
              onClick={handleTestConnection}
              disabled={!selectedPlugin}
            >
              测试连接
            </Button>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              loading={saving}
              onClick={handleSave}
              disabled={!testResult?.success}
            >
              {isEdit ? '更新数据源' : '创建数据源'}
            </Button>
            <Button onClick={() => navigate('/datasources')}>
              取消
            </Button>
          </Space>
        </Card>
      </Form>
    </PageContainer>
  );
};

export default DatasourceCreate;
