name: On demand build Docker image and deploy preview

on:
  # This workflow is only triggered by the `/build-deploy-preview` command dispatch
  repository_dispatch:
    types: [build-deploy-preview-command]

jobs:
  notify-job-details:
    runs-on: ubuntu-latest
    steps:
      # This step creates a comment on the PR with a link to this workflow run.
      - name: Add a comment on the PR with link to workflow run
        uses: peter-evans/create-or-update-comment@v3
        with:
          issue-number: ${{ github.event.client_payload.pull_request.number }}
          body: |
            Deploying Your Preview: <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}>.
            Workflow: `${{ github.workflow }}`.
            skip-tests: `${{ github.event.client_payload.slash_command.args.named.skip-tests }}`.
            env: `${{ github.event.client_payload.slash_command.args.named.env }}`.
            PR: ${{ github.event.client_payload.pull_request.number }}.
            recreate: ${{ github.event.client_payload.slash_command.args.named.recreate }}.

  server-build:
    name: server-build
    if: github.event.client_payload.slash_command.args.named.env != 'release'
    uses: ./.github/workflows/server-build.yml
    secrets: inherit
    with:
      pr: ${{ github.event.client_payload.pull_request.number }}
      skip-tests: ${{ github.event.client_payload.slash_command.args.named.skip-tests }}

  client-build:
    name: client-build
    if: github.event.client_payload.slash_command.args.named.env != 'release'
    uses: ./.github/workflows/client-build.yml
    secrets: inherit
    with:
      pr: ${{ github.event.client_payload.pull_request.number }}
      skip-tests: ${{ github.event.client_payload.slash_command.args.named.skip-tests }}

  rts-build:
    name: rts-build
    if: github.event.client_payload.slash_command.args.named.env != 'release'
    uses: ./.github/workflows/rts-build.yml
    secrets: inherit
    with:
      pr: ${{ github.event.client_payload.pull_request.number }}
      skip-tests: ${{ github.event.client_payload.slash_command.args.named.skip-tests }}

  vercel-build:
    name: vercel-build
    if: github.event.client_payload.slash_command.args.named.env == 'release'
    runs-on: ubuntu-latest
    env:
      VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
      VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

    steps:
      - name: Checkout PR
        uses: actions/checkout@v4
        with:
          ref: "refs/pull/${{ github.event.client_payload.pull_request.number }}/merge"

      - name: Install Vercel CLI
        run: npm install --global vercel@latest

      - name: Use Node.js
        if: steps.run_result.outputs.run_result != 'success'
        uses: actions/setup-node@v3
        with:
          node-version-file: app/client/package.json

      # actions/setup-node@v3 doesn’t work properly with Yarn 3
      # when the project lives in a subdirectory: https://github.com/actions/setup-node/issues/488
      # Restoring the cache manually instead
      - name: Restore Yarn cache
        if: steps.run_result.outputs.run_result != 'success'
        uses: actions/cache@v3
        with:
          path: app/.yarn/cache
          key: v1-yarn3-${{ hashFiles('app/yarn.lock') }}
          restore-keys: |
            v1-yarn3-

      - name: Pull Vercel Environment Information
        run: vercel pull --yes --token=${{ secrets.VERCEL_TOKEN }}

      - name: Build Project Artifacts
        run: vercel build --yes --token=${{ secrets.VERCEL_TOKEN }}

      - name: Deploy Project Artifacts to Vercel
        run: |
          vercel deploy --prebuilt --token=${{ secrets.VERCEL_TOKEN }} | tee -a ~/run_result.txt

      - uses: actions/github-script@v6
        with:
          script: |
            const dpUrl = require("fs").readFileSync(process.env.HOME + "/run_result.txt", "utf8")
            const bodyLines = ["Deploy-Preview-URL: " + dpUrl]
            if (context.repo.repo === "appsmith") {
              bodyLines.push(
                "",
                "🚨 *Note*: The release environment runs EE code, so using a frontend-only DP on this repo, will",
                "likely behave unexpectedly. Consider using a full DP instead.",
                "[Learn more](https://notion.so/031b87bce3404e3a95240d4c14c82e46).",
              )
            }
            github.rest.issues.createComment({
              issue_number: context.payload.client_payload.pull_request.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: bodyLines.join("\n"),
            })

  push-image:
    needs: [client-build, rts-build, server-build]
    runs-on: ubuntu-latest
    if: success()
    steps:
      # Check out merge commit
      - name: Checkout PR
        uses: actions/checkout@v4
        with:
          ref: "refs/pull/${{ github.event.client_payload.pull_request.number }}/merge"

      # Timestamp will be used to create cache key
      - id: timestamp
        run: echo "timestamp=$(date +'%Y-%m-%dT%H:%M:%S')" >> $GITHUB_OUTPUT

      # get Git-hash will be used to create cache key
      - id: git_hash
        run: echo "git_hash=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

      - name: Download the client build artifact
        uses: actions/download-artifact@v3
        with:
          name: client-build
          path: app/client

      - name: Unpack the client build artifact
        if: steps.run_result.outputs.run_result != 'success'
        run: |
          mkdir -p app/client/build
          tar -xvf app/client/build.tar -C app/client/build

      - name: Download the server build artifact
        uses: actions/download-artifact@v3
        with:
          name: server-build
          path: app/server/dist

      - name: Download the rts build artifact
        uses: actions/download-artifact@v3
        with:
          name: rts-dist
          path: app/client/packages/rts/dist

      - name: Untar the rts folder
        run: |
          tar -xvf app/client/packages/rts/dist/rts-dist.tar -C app/client/packages/rts/
          echo "Cleaning up the tar files"
          rm app/client/packages/rts/dist/rts-dist.tar

      - name: Generate info.json
        run: |
          if [[ -f scripts/generate_info_json.sh ]]; then
            scripts/generate_info_json.sh
          fi

      - name: Login to DockerHub
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_ACCESS_TOKEN }}

      - name: Push to Docker Hub
        uses: docker/build-push-action@v4
        with:
          context: .
          pull: true
          push: true
          cache-from: ${{ vars.DOCKER_HUB_ORGANIZATION }}/appsmith-${{ vars.EDITION }}:release
          tags: |
            ${{ vars.DOCKER_HUB_ORGANIZATION }}/appsmith-dp:${{ vars.EDITION }}-${{ github.event.client_payload.pull_request.number }}
          build-args: |
            APPSMITH_CLOUD_SERVICES_BASE_URL=https://release-cs.appsmith.com
            BASE=${{ vars.DOCKER_HUB_ORGANIZATION }}/base-${{ vars.EDITION }}:release

    outputs:
      imageHash: ${{ vars.EDITION }}-${{ github.event.client_payload.pull_request.number }}

  build-deploy-preview:
    needs: [push-image]
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: "."

    if: success()
    steps:
      - name: Checkout PR
        uses: actions/checkout@v4
        with:
          ref: "refs/pull/${{ github.event.client_payload.pull_request.number }}/merge"

      - name: Print versions of tools
        run: |
          set -o errexit
          set -o xtrace
          aws --version || true
          kubectl version || true
          helm version || true

      - name: Install mongosh
        run: |
          curl -fsSL https://pgp.mongodb.com/server-6.0.asc \
            | sudo gpg -o /usr/share/keyrings/mongodb-server-6.0.gpg --dearmor
          echo "deb [ arch=amd64,arm64 signed-by=/usr/share/keyrings/mongodb-server-6.0.gpg ] https://repo.mongodb.org/apt/ubuntu jammy/mongodb-org/6.0 multiverse" \
            | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list
          sudo apt-get update
          sudo apt-get install --yes mongodb-mongosh
          echo "MongoSH version: $(mongosh --version)"

      - name: Install relevant packages
        run: |
          set -o errexit
          # We still need Kubectl v1.23. Once we up the cluster version, we can get rid of this step.
          curl -LO https://storage.googleapis.com/kubernetes-release/release/v1.23.6/bin/linux/amd64/kubectl
          sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl
          kubectl version || true

      - name: Deploy Helm chart
        env:
          AWS_ROLE_ARN: ${{ secrets.APPSMITH_EKS_AWS_ROLE_ARN }}
          AWS_ACCESS_KEY_ID: ${{ secrets.APPSMITH_CI_AWS_SECRET_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.APPSMITH_CI_AWS_SECRET_ACCESS_KEY }}
          IMAGE_HASH: ${{ needs.push-image.outputs.imageHash }}
          AWS_RELEASE_CERT: ${{ secrets.APPSMITH_AWS_RELEASE_CERT_RELEASE }}
          DOCKER_HUB_ORGANIZATION: ${{ vars.DOCKER_HUB_ORGANIZATION }}
          DOCKER_HUB_USERNAME: ${{ secrets.DOCKER_HUB_USERNAME }}
          DOCKER_HUB_ACCESS_TOKEN: ${{ secrets.DOCKER_HUB_ACCESS_TOKEN }}
          PULL_REQUEST_NUMBER: ${{ github.event.client_payload.pull_request.number }}
          RECREATE: ${{ github.event.client_payload.slash_command.args.named.recreate }}
          DB_USERNAME: ${{ secrets.DB_USERNAME }}
          DB_PASSWORD: ${{ secrets.DB_PASSWORD }}
          DB_URL: ${{ secrets.DB_URL }}
          DP_EFS_ID: ${{ secrets.APPSMITH_DP_EFS_ID }}
          REDIS_URL: ${{ secrets.APPSMITH_DP_REDIS_URL }}
        run: |
          echo "environment variables set to deploy the image" $IMAGE_HASH
          /bin/bash ./scripts/deploy_preview.sh

  notify-url:
    needs: [build-deploy-preview]
    runs-on: ubuntu-latest
    if: success()
    steps:
      # This step creates a comment on the PR with a link to this workflow run.
      - name: Add a comment on the PR with link to Deploy-Preview
        uses: peter-evans/create-or-update-comment@v3
        with:
          issue-number: ${{ github.event.client_payload.pull_request.number }}
          body: |
            Deploy-Preview-URL: https://${{ vars.EDITION }}-${{ github.event.client_payload.pull_request.number }}.dp.appsmith.com
