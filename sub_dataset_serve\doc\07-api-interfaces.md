# PagePlug 数据源 API 接口设计

## 1. API 设计原则

### 1.1 RESTful 设计规范
- **资源导向**: 以资源为中心设计API
- **HTTP动词**: 使用标准HTTP方法(GET, POST, PUT, DELETE)
- **状态码**: 使用标准HTTP状态码
- **统一响应**: 统一的响应格式和错误处理

### 1.2 API版本控制
- **URL版本**: `/api/v1/datasources`
- **向后兼容**: 保持API向后兼容性
- **废弃策略**: 渐进式API废弃和迁移

### 1.3 安全设计
- **认证**: JWT Token认证
- **授权**: 基于角色的权限控制
- **限流**: API调用频率限制
- **审计**: 操作日志记录

## 2. 数据源管理 API

### 2.1 数据源 CRUD 操作

#### 2.1.1 创建数据源
```http
POST /api/v1/datasources
Content-Type: application/json
Authorization: Bearer {token}

{
  "name": "MySQL Production",
  "pluginId": "mysql-plugin",
  "workspaceId": "workspace-123",
  "datasourceConfiguration": {
    "url": "mysql://localhost:3306",
    "databaseName": "production",
    "authentication": {
      "authType": "basic",
      "username": "admin",
      "password": "password123"
    },
    "connection": {
      "ssl": {
        "enabled": true,
        "mode": "REQUIRED"
      }
    },
    "properties": [
      {
        "key": "useSSL",
        "value": "true"
      }
    ]
  }
}
```

**响应示例**:
```json
{
  "code": 201,
  "message": "Datasource created successfully",
  "data": {
    "id": "ds-456",
    "name": "MySQL Production",
    "pluginId": "mysql-plugin",
    "workspaceId": "workspace-123",
    "isValid": true,
    "isConfigured": true,
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:30:00Z",
    "datasourceConfiguration": {
      "url": "mysql://localhost:3306",
      "databaseName": "production",
      "authentication": {
        "authType": "basic",
        "username": "admin",
        "secretExists": {
          "password": true
        }
      }
    }
  }
}
```

#### 2.1.2 获取数据源列表
```http
GET /api/v1/datasources?workspaceId=workspace-123&pluginId=mysql-plugin&page=1&size=20
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "content": [
      {
        "id": "ds-456",
        "name": "MySQL Production",
        "pluginId": "mysql-plugin",
        "pluginName": "MySQL",
        "isValid": true,
        "isConfigured": true,
        "lastUsed": "2024-01-15T09:45:00Z",
        "createdAt": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 1,
      "totalPages": 1
    }
  }
}
```

#### 2.1.3 获取数据源详情
```http
GET /api/v1/datasources/{datasourceId}
Authorization: Bearer {token}
```

#### 2.1.4 更新数据源
```http
PUT /api/v1/datasources/{datasourceId}
Content-Type: application/json
Authorization: Bearer {token}

{
  "name": "MySQL Production Updated",
  "datasourceConfiguration": {
    "url": "mysql://new-host:3306",
    "databaseName": "production"
  }
}
```

#### 2.1.5 删除数据源
```http
DELETE /api/v1/datasources/{datasourceId}
Authorization: Bearer {token}
```

### 2.2 数据源操作 API

#### 2.2.1 测试数据源连接
```http
POST /api/v1/datasources/{datasourceId}/test
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "Connection test completed",
  "data": {
    "success": true,
    "message": "Connection successful",
    "responseTime": 150,
    "details": {
      "serverVersion": "8.0.28",
      "charset": "utf8mb4"
    }
  }
}
```

#### 2.2.2 获取数据源结构
```http
GET /api/v1/datasources/{datasourceId}/structure
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "Structure retrieved successfully",
  "data": {
    "tables": [
      {
        "name": "users",
        "type": "TABLE",
        "columns": [
          {
            "name": "id",
            "type": "INT",
            "isPrimaryKey": true,
            "isAutoIncrement": true,
            "isNullable": false
          },
          {
            "name": "name",
            "type": "VARCHAR",
            "maxLength": 255,
            "isNullable": false
          },
          {
            "name": "email",
            "type": "VARCHAR",
            "maxLength": 255,
            "isNullable": true
          }
        ],
        "keys": [
          {
            "name": "PRIMARY",
            "type": "PRIMARY",
            "columnNames": ["id"]
          }
        ],
        "templates": [
          {
            "title": "查询所有用户",
            "body": "SELECT * FROM users LIMIT 10;",
            "suggested": true
          }
        ]
      }
    ]
  }
}
```

#### 2.2.3 执行查询
```http
POST /api/v1/datasources/{datasourceId}/execute
Content-Type: application/json
Authorization: Bearer {token}

{
  "query": "SELECT * FROM users WHERE age > {{minAge}} LIMIT {{limit}}",
  "parameters": [
    {
      "key": "minAge",
      "value": 18
    },
    {
      "key": "limit",
      "value": 10
    }
  ]
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "Query executed successfully",
  "data": {
    "isExecutionSuccess": true,
    "body": [
      {
        "id": 1,
        "name": "John Doe",
        "email": "<EMAIL>",
        "age": 25
      },
      {
        "id": 2,
        "name": "Jane Smith",
        "email": "<EMAIL>",
        "age": 30
      }
    ],
    "headers": {
      "Content-Type": "application/json"
    },
    "statusCode": "200",
    "executionTime": 45,
    "rowsAffected": 2
  }
}
```

## 3. 插件管理 API

### 3.1 插件信息 API

#### 3.1.1 获取插件列表
```http
GET /api/v1/plugins?category=database&status=active
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "id": "mysql-plugin",
      "name": "MySQL",
      "displayName": "MySQL Database",
      "version": "1.0.0",
      "category": "database",
      "status": "active",
      "description": "Connect to MySQL databases",
      "iconUrl": "/icons/mysql.svg",
      "documentationUrl": "https://docs.example.com/mysql",
      "supportedOperations": ["READ", "WRITE"],
      "templates": {
        "SELECT": "SELECT * FROM table_name;",
        "INSERT": "INSERT INTO table_name (column1, column2) VALUES (value1, value2);"
      }
    }
  ]
}
```

#### 3.1.2 获取插件详情
```http
GET /api/v1/plugins/{pluginId}
Authorization: Bearer {token}
```

#### 3.1.3 获取插件表单配置
```http
GET /api/v1/plugins/{pluginId}/form
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "sections": [
      {
        "name": "Connection",
        "fields": [
          {
            "name": "url",
            "label": "Host Address",
            "type": "INPUT_TEXT",
            "required": true,
            "placeholder": "localhost",
            "validation": {
              "pattern": "^[a-zA-Z0-9.-]+$",
              "message": "Invalid host format"
            }
          },
          {
            "name": "port",
            "label": "Port",
            "type": "INPUT_NUMBER",
            "required": true,
            "defaultValue": 3306,
            "validation": {
              "min": 1,
              "max": 65535
            }
          }
        ]
      }
    ]
  }
}
```

## 4. 查询管理 API

### 4.1 查询操作 API

#### 4.1.1 保存查询
```http
POST /api/v1/queries
Content-Type: application/json
Authorization: Bearer {token}

{
  "name": "Get Active Users",
  "datasourceId": "ds-456",
  "query": "SELECT * FROM users WHERE status = 'active'",
  "parameters": [],
  "tags": ["users", "active"]
}
```

#### 4.1.2 获取查询列表
```http
GET /api/v1/queries?datasourceId=ds-456&tag=users
Authorization: Bearer {token}
```

#### 4.1.3 执行保存的查询
```http
POST /api/v1/queries/{queryId}/execute
Content-Type: application/json
Authorization: Bearer {token}

{
  "parameters": [
    {
      "key": "status",
      "value": "active"
    }
  ]
}
```

## 5. 统一响应格式

### 5.1 成功响应格式
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    // 响应数据
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### 5.2 错误响应格式
```json
{
  "code": 400,
  "message": "Validation failed",
  "error": {
    "type": "VALIDATION_ERROR",
    "details": [
      {
        "field": "datasourceConfiguration.url",
        "message": "URL is required"
      }
    ]
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### 5.3 分页响应格式
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "content": [
      // 数据列表
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 100,
      "totalPages": 5,
      "hasNext": true,
      "hasPrevious": false
    }
  }
}
```

## 6. 错误码定义

### 6.1 通用错误码
- `400` - 请求参数错误
- `401` - 未认证
- `403` - 权限不足
- `404` - 资源不存在
- `409` - 资源冲突
- `429` - 请求频率超限
- `500` - 服务器内部错误

### 6.2 业务错误码
```json
{
  "DATASOURCE_NOT_FOUND": {
    "code": "DS001",
    "message": "Datasource not found"
  },
  "DATASOURCE_CONNECTION_FAILED": {
    "code": "DS002",
    "message": "Failed to connect to datasource"
  },
  "QUERY_EXECUTION_FAILED": {
    "code": "DS003",
    "message": "Query execution failed"
  },
  "PLUGIN_NOT_FOUND": {
    "code": "PL001",
    "message": "Plugin not found"
  },
  "PLUGIN_LOAD_FAILED": {
    "code": "PL002",
    "message": "Failed to load plugin"
  },
  "INVALID_QUERY_SYNTAX": {
    "code": "QR001",
    "message": "Invalid query syntax"
  },
  "QUERY_TIMEOUT": {
    "code": "QR002",
    "message": "Query execution timeout"
  }
}
```

## 7. API 安全

### 7.1 认证机制
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 7.2 权限控制
- **数据源权限**: READ, WRITE, DELETE
- **工作空间权限**: MEMBER, ADMIN, OWNER
- **资源级权限**: 基于资源ID的细粒度权限

### 7.3 API限流
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1642248000
```

## 8. API 文档和测试

### 8.1 OpenAPI 规范
```yaml
openapi: 3.0.0
info:
  title: PagePlug Datasource API
  version: 1.0.0
  description: API for managing datasources and executing queries

paths:
  /api/v1/datasources:
    get:
      summary: List datasources
      parameters:
        - name: workspaceId
          in: query
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DatasourceListResponse'
```

### 8.2 API测试用例
```javascript
// Jest测试示例
describe('Datasource API', () => {
  test('should create datasource successfully', async () => {
    const datasource = {
      name: 'Test MySQL',
      pluginId: 'mysql-plugin',
      workspaceId: 'workspace-123'
    };
    
    const response = await request(app)
      .post('/api/v1/datasources')
      .send(datasource)
      .expect(201);
    
    expect(response.body.data.name).toBe('Test MySQL');
    expect(response.body.data.id).toBeDefined();
  });
  
  test('should test datasource connection', async () => {
    const response = await request(app)
      .post('/api/v1/datasources/ds-456/test')
      .expect(200);
    
    expect(response.body.data.success).toBe(true);
  });
});
```
