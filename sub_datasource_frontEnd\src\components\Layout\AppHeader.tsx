import React from 'react';
import { Layout, But<PERSON>, Space, Dropdown, Avatar, Typography, Badge } from 'antd';
import {
  BulbOutlined,
  BulbFilled,
  BellOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';
import type { MenuProps } from 'antd';
import styled from 'styled-components';

import { useTheme } from '../../hooks/useTheme';

const { Header } = Layout;
const { Text } = Typography;

// 样式化组件
const StyledHeader = styled(Header)<{ $isDark: boolean }>`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  background: ${props => props.$isDark ? '#1f1f1f' : '#ffffff'} !important;
  border-bottom: 1px solid ${props => props.$isDark ? '#303030' : '#f0f0f0'};
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  position: sticky;
  top: 0;
  z-index: 100;
`;

const LogoSection = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const Logo = styled.div<{ $isDark: boolean }>`
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 16px;
`;

const AppTitle = styled.div<{ $isDark: boolean }>`
  .title {
    font-size: 18px;
    font-weight: 600;
    color: ${props => props.$isDark ? '#ffffff' : '#262626'};
    margin: 0;
  }
  
  .subtitle {
    font-size: 12px;
    color: ${props => props.$isDark ? '#d9d9d9' : '#8c8c8c'};
    margin: 0;
  }
`;

const ActionSection = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const ThemeToggle = styled(Button)<{ $isDark: boolean }>`
  border: none;
  background: transparent;
  color: ${props => props.$isDark ? '#d9d9d9' : '#595959'};
  
  &:hover {
    background: ${props => props.$isDark ? '#262626' : '#f5f5f5'};
    color: ${props => props.$isDark ? '#ffffff' : '#262626'};
  }
`;

const NotificationButton = styled(Button)<{ $isDark: boolean }>`
  border: none;
  background: transparent;
  color: ${props => props.$isDark ? '#d9d9d9' : '#595959'};
  
  &:hover {
    background: ${props => props.$isDark ? '#262626' : '#f5f5f5'};
    color: ${props => props.$isDark ? '#ffffff' : '#262626'};
  }
`;

const UserAvatar = styled(Avatar)<{ $isDark: boolean }>`
  background-color: ${props => props.$isDark ? '#434343' : '#f0f0f0'};
  color: ${props => props.$isDark ? '#ffffff' : '#262626'};
  cursor: pointer;
  
  &:hover {
    background-color: ${props => props.$isDark ? '#595959' : '#d9d9d9'};
  }
`;

export const AppHeader: React.FC = () => {
  const { isDark, toggleTheme } = useTheme();

  // 用户菜单项
  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料'
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置'
    },
    {
      type: 'divider'
    },
    {
      key: 'help',
      icon: <QuestionCircleOutlined />,
      label: '帮助文档'
    },
    {
      type: 'divider'
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      danger: true
    }
  ];

  const handleUserMenuClick: MenuProps['onClick'] = ({ key }) => {
    switch (key) {
      case 'profile':
        // TODO: 打开个人资料页面
        console.log('打开个人资料');
        break;
      case 'settings':
        // TODO: 打开设置页面
        console.log('打开设置');
        break;
      case 'help':
        // TODO: 打开帮助文档
        window.open('https://docs.example.com', '_blank');
        break;
      case 'logout':
        // TODO: 执行退出登录
        console.log('退出登录');
        break;
      default:
        break;
    }
  };

  const handleNotificationClick = () => {
    // TODO: 打开通知面板
    console.log('打开通知');
  };

  return (
    <StyledHeader $isDark={isDark}>
      <LogoSection>
        <Logo $isDark={isDark}>DS</Logo>
        <AppTitle $isDark={isDark}>
          <div className="title">数据源管理</div>
          <div className="subtitle">PagePlug DataSource</div>
        </AppTitle>
      </LogoSection>

      <ActionSection>
        <Space size="small">
          {/* 主题切换按钮 */}
          <ThemeToggle
            $isDark={isDark}
            type="text"
            icon={isDark ? <BulbFilled /> : <BulbOutlined />}
            onClick={toggleTheme}
            title={isDark ? '切换到亮色主题' : '切换到暗色主题'}
          />

          {/* 通知按钮 */}
          <Badge count={3} size="small">
            <NotificationButton
              $isDark={isDark}
              type="text"
              icon={<BellOutlined />}
              onClick={handleNotificationClick}
              title="通知"
            />
          </Badge>

          {/* 用户菜单 */}
          <Dropdown
            menu={{
              items: userMenuItems,
              onClick: handleUserMenuClick
            }}
            placement="bottomRight"
            trigger={['click']}
          >
            <UserAvatar $isDark={isDark} size="small" icon={<UserOutlined />} />
          </Dropdown>
        </Space>
      </ActionSection>
    </StyledHeader>
  );
};
