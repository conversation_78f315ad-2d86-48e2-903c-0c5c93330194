# This workflow is responsible for building, testing & packaging the Appsmithctl CLI util
name: Build Appsmithctl CLI util Workflow

on:
  # This line enables manual triggering of this workflow.
  workflow_dispatch:
  workflow_call:
    inputs:
      pr:
        description: "This is the PR number in case the workflow is being called in a pull request"
        required: false
        type: number

  pull_request:
    branches: [release, master]
    paths:
      - "deploy/docker/fs/opt/appsmith/utils/**"

# Change the working directory for all the jobs in this workflow
defaults:
  run:
    working-directory: deploy/docker/fs/opt/appsmith/utils/

jobs:
  build:
    runs-on: ubuntu-latest
    # Only run this workflow for internally triggered events
    if: |
      github.event.pull_request.head.repo.full_name == github.repository ||
      github.event_name == 'push' ||
      github.event_name == 'workflow_dispatch' ||
      github.event_name == 'repository_dispatch'

    steps:
      # The checkout steps MUST happen first because the default directory is set according to the code base.
      # GitHub Action expects all future commands to be executed in the code directory. Hence, we need to check out
      # the code before doing anything else.

      # Check out merge commit with the base branch in case this workflow is invoked via pull request
      - name: Checkout the merged commit from PR and base branch
        if: inputs.pr != 0
        uses: actions/checkout@v4
        with:
          ref: refs/pull/${{ inputs.pr }}/merge

      # Checkout the code in the current branch in case the workflow is called because of a branch push event
      - name: Checkout the head commit of the branch
        if: inputs.pr == 0
        uses: actions/checkout@v4

      - name: Figure out the PR number
        run: echo ${{ inputs.pr }}

      - name: Print the Github event
        run: echo ${{ github.event_name }}

      # In case this is second attempt try restoring status of the prior attempt from cache
      - name: Restore the previous run result
        uses: actions/cache@v3
        with:
          path: |
            ~/appsmithctl_run_result
          key: ${{ github.run_id }}-${{ github.job }}-appsmithctl-util

      # Fetch prior run result
      - name: Get the previous run result
        id: appsmithctl_run_result
        run: cat ~/appsmithctl_run_result 2>/dev/null || echo 'default'

      # In case of prior failure run the job
      - if: steps.appsmithctl_run_result.outputs.appsmithctl_run_result != 'success'
        run: echo "I'm alive!" && exit 0

      - name: Use Node.js
        if: steps.appsmithctl_run_result.outputs.appsmithctl_run_result != 'success'
        uses: actions/setup-node@v3
        with:
          node-version-file: app/client/package.json

      # Install all the dependencies
      - name: Install dependencies
        if: steps.appsmithctl_run_result.outputs.appsmithctl_run_result != 'success'
        run: yarn install --frozen-lockfile

      # Run the Jest tests only if the workflow has been invoked in a PR
      - name: Run the jest tests
        if: steps.appsmithctl_run_result.outputs.appsmithctl_run_result != 'success'
        run: yarn run test

      # Set status = failure
      - name: Set result as failed if there are build failures
        if: failure()
        run: |
          echo "appsmithctl_run_result=failed" >> $GITHUB_OUTPUT > ~/appsmithctl_run_result
          exit 1;

      # Set status = success
      - name: Save the status of the run
        run: echo "appsmithctl_run_result=success" >> $GITHUB_OUTPUT > ~/appsmithctl_run_result
