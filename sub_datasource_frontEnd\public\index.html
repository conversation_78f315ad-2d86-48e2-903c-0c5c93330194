<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8" />
  <!-- <link rel="icon" href="/favicon.ico" /> -->
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <meta name="theme-color" content="#000000" />
  <meta name="description" content="PagePlug 数据源管理微前端应用" />
  <title>PagePlug - 数据源管理</title>
  
  <!-- 预加载关键字体 -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- 预加载图标字体 -->
  <link rel="preload" href="https://at.alicdn.com/t/font_1788044_0dwu4guekcwr.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
  
  <style>
    /* 初始加载样式 */
    #root {
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;
    }
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid #f3f4f6;
      border-top: 3px solid #3b82f6;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    .loading-text {
      color: #6b7280;
      font-family: 'Inter', sans-serif;
      font-size: 14px;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    /* 隐藏加载状态当应用加载完成 */
    .app-loaded .loading-container {
      display: none;
    }
  </style>
</head>
<body>
  <noscript>您需要启用 JavaScript 来运行此应用程序。</noscript>
  
  <!-- 微前端应用容器 -->
  <div id="datasource-app">
    <div id="root">
      <div class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载数据源管理应用...</div>
      </div>
    </div>
  </div>
  
  <script>
    // 应用加载完成后移除加载状态
    window.addEventListener('DOMContentLoaded', function() {
      setTimeout(function() {
        const container = document.getElementById('datasource-app');
        if (container) {
          container.classList.add('app-loaded');
        }
      }, 100);
    });
  </script>
</body>
</html>
