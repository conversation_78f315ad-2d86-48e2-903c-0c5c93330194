import React from 'react';
import { createRoot } from 'react-dom/client';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';

import App from './App';
import { store } from './store';
import { ThemeProvider } from './providers/ThemeProvider';
import { ErrorBoundary } from './components/ErrorBoundary';

import './styles/index.css';

// 设置dayjs中文语言
dayjs.locale('zh-cn');

// 微前端启动函数
export const mount = (element: HTMLElement, options: {
  basename?: string;
  onNavigate?: (location: Location) => void;
  initialPath?: string;
} = {}) => {
  const { basename = '/datasource', onNavigate, initialPath } = options;

  // 创建React根节点
  const root = createRoot(element);

  // 渲染应用
  root.render(
    <React.StrictMode>
      <ErrorBoundary>
        <Provider store={store}>
          <BrowserRouter basename={basename}>
            <ConfigProvider
              locale={zhCN}
              theme={{
                token: {
                  colorPrimary: '#1890ff',
                  borderRadius: 6,
                  fontSize: 14
                }
              }}
            >
              <ThemeProvider>
                <App />
              </ThemeProvider>
            </ConfigProvider>
          </BrowserRouter>
        </Provider>
      </ErrorBoundary>
    </React.StrictMode>
  );

  // 监听路由变化
  if (onNavigate) {
    // 这里可以添加路由变化监听逻辑
    window.addEventListener('popstate', () => {
      onNavigate(window.location);
    });
  }

  // 如果有初始路径，导航到该路径
  if (initialPath && initialPath !== window.location.pathname) {
    window.history.pushState(null, '', initialPath);
  }

  return {
    // 卸载函数
    unmount: () => {
      root.unmount();
    },
    
    // 更新函数
    update: (newOptions: typeof options) => {
      // 可以在这里处理配置更新
      console.log('Updating datasource app with options:', newOptions);
    }
  };
};

// 独立运行模式
if (!window.__POWERED_BY_QIANKUN__) {
  const container = document.getElementById('root');
  if (container) {
    mount(container);
  }
}
