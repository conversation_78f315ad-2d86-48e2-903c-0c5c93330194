{"name": "pageplug-datasource-frontend", "version": "1.0.0", "description": "PagePlug 数据源管理微前端应用", "main": "src/index.tsx", "scripts": {"start": "webpack serve --mode development", "build": "webpack --mode production", "build:dev": "webpack --mode development", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "keywords": ["react", "typescript", "microfrontend", "module-federation", "datasource"], "author": "PagePlug Team", "license": "MIT", "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "@reduxjs/toolkit": "^1.9.3", "react-redux": "^8.0.5", "antd": "^5.2.2", "styled-components": "^5.3.6", "@ant-design/icons": "^5.0.1", "axios": "^1.3.4", "react-query": "^3.39.3", "react-i18next": "^12.1.5", "i18next": "^22.4.10", "dayjs": "^1.11.7", "lodash": "^4.17.21", "classnames": "^2.3.2"}, "devDependencies": {"@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@types/styled-components": "^5.1.26", "@types/lodash": "^4.14.191", "@types/node": "^18.14.2", "typescript": "^4.9.5", "webpack": "^5.75.0", "webpack-cli": "^5.0.1", "webpack-dev-server": "^4.8.1", "@module-federation/webpack": "^0.0.5", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.10", "react-refresh": "^0.14.0", "html-webpack-plugin": "^5.5.0", "css-loader": "^6.7.3", "style-loader": "^3.3.2", "postcss": "^8.4.21", "postcss-loader": "^7.0.2", "tailwindcss": "^3.2.7", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/typography": "^0.5.9", "@tailwindcss/aspect-ratio": "^0.4.2", "autoprefixer": "^10.4.13", "ts-loader": "^9.4.2", "babel-loader": "^9.1.2", "@babel/core": "^7.21.0", "@babel/preset-env": "^7.20.2", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.21.0", "@babel/plugin-proposal-decorators": "^7.21.0", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.21.2", "babel-plugin-import": "^1.13.6", "core-js": "^3.29.0", "eslint": "^8.35.0", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-import": "^2.27.5", "eslint-import-resolver-typescript": "^3.5.3", "prettier": "^2.8.4", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.4.3", "ts-jest": "^29.0.5", "@testing-library/react": "^14.0.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/user-event": "^14.4.3", "jest-environment-jsdom": "^29.4.3", "@types/jest": "^29.4.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}