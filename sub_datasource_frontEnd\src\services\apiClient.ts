import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { message } from 'antd';

import { ApiResponse, ApiError } from '../types/api';
import { MockApi } from './mockApi';

// API配置
interface ApiConfig {
  baseURL: string;
  timeout: number;
  useMock: boolean;
}

// 默认配置
const defaultConfig: ApiConfig = {
  baseURL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000/api/v1',
  timeout: 10000,
  useMock: process.env.NODE_ENV === 'development' || process.env.REACT_APP_USE_MOCK === 'true'
};

// API客户端类
class ApiClient {
  private axiosInstance: AxiosInstance;
  private config: ApiConfig;

  constructor(config: Partial<ApiConfig> = {}) {
    this.config = { ...defaultConfig, ...config };
    
    this.axiosInstance = axios.create({
      baseURL: this.config.baseURL,
      timeout: this.config.timeout,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // 请求拦截器
    this.axiosInstance.interceptors.request.use(
      (config) => {
        // 添加认证token
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // 添加请求ID用于追踪
        config.headers['X-Request-ID'] = this.generateRequestId();

        console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, config.data);
        return config;
      },
      (error) => {
        console.error('[API Request Error]', error);
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.axiosInstance.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        console.log(`[API Response] ${response.config.url}`, response.data);
        return response;
      },
      (error) => {
        console.error('[API Response Error]', error);
        
        // 处理不同类型的错误
        if (error.response) {
          // 服务器返回错误状态码
          const { status, data } = error.response;
          
          switch (status) {
            case 401:
              message.error('认证失败，请重新登录');
              // 清除token并跳转到登录页
              localStorage.removeItem('auth_token');
              window.location.href = '/login';
              break;
            case 403:
              message.error('权限不足');
              break;
            case 404:
              message.error('请求的资源不存在');
              break;
            case 429:
              message.error('请求过于频繁，请稍后再试');
              break;
            case 500:
              message.error('服务器内部错误');
              break;
            default:
              message.error(data?.message || '请求失败');
          }
          
          return Promise.reject(data || error);
        } else if (error.request) {
          // 网络错误
          message.error('网络连接失败，请检查网络设置');
          return Promise.reject(new Error('Network Error'));
        } else {
          // 其他错误
          message.error('请求配置错误');
          return Promise.reject(error);
        }
      }
    );
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // 通用请求方法
  async request<T>(config: AxiosRequestConfig): Promise<ApiResponse<T>> {
    if (this.config.useMock) {
      // 使用模拟API
      return this.handleMockRequest<T>(config);
    }

    const response = await this.axiosInstance.request<ApiResponse<T>>(config);
    return response.data;
  }

  // 处理模拟请求
  private async handleMockRequest<T>(config: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const { method = 'GET', url = '', data } = config;
    const path = url.replace(this.config.baseURL, '');

    console.log(`[Mock API] ${method.toUpperCase()} ${path}`, data);

    try {
      // 根据路径和方法路由到对应的模拟API
      if (path.startsWith('/datasources')) {
        return this.handleDatasourceMockRequest<T>(method, path, data);
      } else if (path.startsWith('/plugins')) {
        return this.handlePluginMockRequest<T>(method, path, data);
      } else if (path.startsWith('/queries')) {
        return this.handleQueryMockRequest<T>(method, path, data);
      } else {
        throw new Error(`Mock API not implemented for ${path}`);
      }
    } catch (error) {
      console.error('[Mock API Error]', error);
      throw error;
    }
  }

  private async handleDatasourceMockRequest<T>(
    method: string,
    path: string,
    data?: any
  ): Promise<ApiResponse<T>> {
    const segments = path.split('/').filter(Boolean);
    
    switch (method.toUpperCase()) {
      case 'GET':
        if (segments.length === 1) {
          // GET /datasources
          return MockApi.getDatasources(data) as Promise<ApiResponse<T>>;
        } else if (segments.length === 2) {
          // GET /datasources/:id
          return MockApi.getDatasource(segments[1]) as Promise<ApiResponse<T>>;
        } else if (segments.length === 3 && segments[2] === 'structure') {
          // GET /datasources/:id/structure
          return MockApi.getDatasourceStructure(segments[1]) as Promise<ApiResponse<T>>;
        }
        break;
      case 'POST':
        if (segments.length === 1) {
          // POST /datasources
          return MockApi.createDatasource(data) as Promise<ApiResponse<T>>;
        } else if (segments.length === 3 && segments[2] === 'test') {
          // POST /datasources/:id/test
          return MockApi.testDatasourceConnection(segments[1]) as Promise<ApiResponse<T>>;
        } else if (segments.length === 3 && segments[2] === 'execute') {
          // POST /datasources/:id/execute
          return MockApi.executeQuery(segments[1], data) as Promise<ApiResponse<T>>;
        }
        break;
      case 'PUT':
        if (segments.length === 2) {
          // PUT /datasources/:id
          return MockApi.updateDatasource(segments[1], data) as Promise<ApiResponse<T>>;
        }
        break;
      case 'DELETE':
        if (segments.length === 2) {
          // DELETE /datasources/:id
          return MockApi.deleteDatasource(segments[1]) as Promise<ApiResponse<T>>;
        }
        break;
    }

    throw new Error(`Mock API route not found: ${method} ${path}`);
  }

  private async handlePluginMockRequest<T>(
    method: string,
    path: string,
    data?: any
  ): Promise<ApiResponse<T>> {
    const segments = path.split('/').filter(Boolean);
    
    switch (method.toUpperCase()) {
      case 'GET':
        if (segments.length === 1) {
          // GET /plugins
          return MockApi.getPlugins(data) as Promise<ApiResponse<T>>;
        } else if (segments.length === 2) {
          // GET /plugins/:id
          return MockApi.getPlugin(segments[1]) as Promise<ApiResponse<T>>;
        }
        break;
    }

    throw new Error(`Mock API route not found: ${method} ${path}`);
  }

  private async handleQueryMockRequest<T>(
    method: string,
    path: string,
    data?: any
  ): Promise<ApiResponse<T>> {
    const segments = path.split('/').filter(Boolean);
    
    switch (method.toUpperCase()) {
      case 'GET':
        if (segments.length === 1) {
          // GET /queries
          return MockApi.getQueries(data) as Promise<ApiResponse<T>>;
        }
        break;
      case 'POST':
        if (segments.length === 1) {
          // POST /queries
          return MockApi.createQuery(data) as Promise<ApiResponse<T>>;
        } else if (segments.length === 3 && segments[2] === 'execute') {
          // POST /queries/:id/execute
          return MockApi.executeQuery(segments[1], data) as Promise<ApiResponse<T>>;
        }
        break;
    }

    throw new Error(`Mock API route not found: ${method} ${path}`);
  }

  // 便捷方法
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: 'GET', url });
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: 'POST', url, data });
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: 'PUT', url, data });
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: 'DELETE', url });
  }
}

// 创建默认实例
export const apiClient = new ApiClient();

// 导出类型和实例
export { ApiClient };
export type { ApiConfig };
