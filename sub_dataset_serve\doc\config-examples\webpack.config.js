// webpack.config.js - 数据源微前端应用配置
const ModuleFederationPlugin = require('@module-federation/webpack')
const HtmlWebpackPlugin = require('html-webpack-plugin')
const path = require('path')

module.exports = (env, argv) => {
  const isProduction = argv.mode === 'production'
  
  return {
    mode: isProduction ? 'production' : 'development',
    entry: './src/index.tsx',
    
    devServer: {
      port: 3001,
      hot: true,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
        'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization',
      },
      historyApiFallback: true,
    },
    
    resolve: {
      extensions: ['.tsx', '.ts', '.js', '.jsx'],
      alias: {
        '@': path.resolve(__dirname, 'src'),
      },
    },
    
    module: {
      rules: [
        {
          test: /\.tsx?$/,
          use: [
            {
              loader: 'ts-loader',
              options: {
                transpileOnly: true,
              },
            },
          ],
          exclude: /node_modules/,
        },
        {
          test: /\.css$/,
          use: [
            'style-loader',
            'css-loader',
            {
              loader: 'postcss-loader',
              options: {
                postcssOptions: {
                  plugins: [
                    require('tailwindcss'),
                    require('autoprefixer'),
                  ],
                },
              },
            },
          ],
        },
        {
          test: /\.less$/,
          use: [
            'style-loader',
            'css-loader',
            {
              loader: 'less-loader',
              options: {
                lessOptions: {
                  modifyVars: {
                    '@primary-color': '#1890ff',
                    '@border-radius-base': '6px',
                  },
                  javascriptEnabled: true,
                },
              },
            },
          ],
        },
        {
          test: /\.(png|jpe?g|gif|svg)$/i,
          type: 'asset/resource',
        },
      ],
    },
    
    plugins: [
      new ModuleFederationPlugin({
        name: 'datasourceApp',
        filename: 'remoteEntry.js',
        exposes: {
          './DatasourceApp': './src/App',
          './DatasourceRoutes': './src/routes',
          './DatasourceStore': './src/store',
          './DatasourceComponents': './src/components',
        },
        shared: {
          react: {
            singleton: true,
            requiredVersion: '^18.2.0',
            eager: false,
          },
          'react-dom': {
            singleton: true,
            requiredVersion: '^18.2.0',
            eager: false,
          },
          'react-router-dom': {
            singleton: true,
            requiredVersion: '^6.0.0',
          },
          antd: {
            singleton: true,
            requiredVersion: '^5.0.0',
          },
          'styled-components': {
            singleton: true,
            requiredVersion: '^6.0.0',
          },
          '@reduxjs/toolkit': {
            singleton: true,
            requiredVersion: '^1.9.0',
          },
          'react-redux': {
            singleton: true,
            requiredVersion: '^8.0.0',
          },
        },
      }),
      
      new HtmlWebpackPlugin({
        template: './public/index.html',
        title: 'PagePlug 数据源管理',
        favicon: './public/favicon.ico',
      }),
    ],
    
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
          },
          antd: {
            test: /[\\/]node_modules[\\/]antd[\\/]/,
            name: 'antd',
            chunks: 'all',
          },
          styledComponents: {
            test: /[\\/]node_modules[\\/]styled-components[\\/]/,
            name: 'styled-components',
            chunks: 'all',
          },
        },
      },
    },
    
    devtool: isProduction ? 'source-map' : 'eval-source-map',
    
    output: {
      path: path.resolve(__dirname, 'dist'),
      filename: isProduction ? '[name].[contenthash].js' : '[name].js',
      chunkFilename: isProduction ? '[name].[contenthash].chunk.js' : '[name].chunk.js',
      publicPath: isProduction ? '/datasource-app/' : 'http://localhost:3001/',
      clean: true,
    },
  }
}
