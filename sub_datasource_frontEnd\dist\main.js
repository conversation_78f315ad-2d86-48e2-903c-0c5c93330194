(()=>{"use strict";var __webpack_modules__={7714:(__unused_webpack_module,__unused_webpack_exports,__webpack_require__)=>{eval("{\r\nPromise.all(/* import() */[__webpack_require__.e(96), __webpack_require__.e(876), __webpack_require__.e(212), __webpack_require__.e(714), __webpack_require__.e(76), __webpack_require__.e(299), __webpack_require__.e(185), __webpack_require__.e(255), __webpack_require__.e(662)]).then(__webpack_require__.bind(__webpack_require__, 9662));\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNzcxNC5qcyIsIm1hcHBpbmdzIjoiO0FBQ0EsK1VBQXFCLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wYWdlcGx1Zy1kYXRhc291cmNlLWZyb250ZW5kLy4vc3JjL2luZGV4LnRzeD9kOTg2Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIOW+ruWJjeerr+WFpeWPo+aWh+S7tlxuaW1wb3J0KCcuL2Jvb3RzdHJhcCcpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///7714\n\n}")}},__webpack_module_cache__={},deferred,inProgress,dataWebpackPrefix,parseVersion,versionLt,rangeToString,satisfy,exists,get,eagerOnly,findSingletonVersionKey,getInvalidSingletonVersionMessage,fail,warn,init,useFallback,loadSingletonVersion,installedModules,moduleToHandlerMapping,chunkMapping,startedInstallModules;function __webpack_require__(e){var _=__webpack_module_cache__[e];if(void 0!==_)return _.exports;var r=__webpack_module_cache__[e]={id:e,loaded:!1,exports:{}};return __webpack_modules__[e].call(r.exports,r,r.exports,__webpack_require__),r.loaded=!0,r.exports}__webpack_require__.m=__webpack_modules__,__webpack_require__.c=__webpack_module_cache__,deferred=[],__webpack_require__.O=(e,_,r,a)=>{if(!_){var i=1/0;for(u=0;u<deferred.length;u++){_=deferred[u][0],r=deferred[u][1],a=deferred[u][2];for(var t=!0,n=0;n<_.length;n++)(!1&a||i>=a)&&Object.keys(__webpack_require__.O).every(e=>__webpack_require__.O[e](_[n]))?_.splice(n--,1):(t=!1,a<i&&(i=a));if(t){deferred.splice(u--,1);var o=r();void 0!==o&&(e=o)}}return e}a=a||0;for(var u=deferred.length;u>0&&deferred[u-1][2]>a;u--)deferred[u]=deferred[u-1];deferred[u]=[_,r,a]},__webpack_require__.n=e=>{var _=e&&e.__esModule?()=>e.default:()=>e;return __webpack_require__.d(_,{a:_}),_},__webpack_require__.d=(e,_)=>{for(var r in _)__webpack_require__.o(_,r)&&!__webpack_require__.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:_[r]})},__webpack_require__.f={},__webpack_require__.e=e=>Promise.all(Object.keys(__webpack_require__.f).reduce((_,r)=>(__webpack_require__.f[r](e,_),_),[])),__webpack_require__.u=e=>(96===e?"vendors":e)+".js",__webpack_require__.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),__webpack_require__.hmd=e=>((e=Object.create(e)).children||(e.children=[]),Object.defineProperty(e,"exports",{enumerable:!0,set:()=>{throw new Error("ES Modules may not assign module.exports or exports.*, Use ESM export syntax, instead: "+e.id)}}),e),__webpack_require__.o=(e,_)=>Object.prototype.hasOwnProperty.call(e,_),inProgress={},dataWebpackPrefix="pageplug-datasource-frontend:",__webpack_require__.l=(e,_,r,a)=>{if(inProgress[e])inProgress[e].push(_);else{var i,t;if(void 0!==r)for(var n=document.getElementsByTagName("script"),o=0;o<n.length;o++){var u=n[o];if(u.getAttribute("src")==e||u.getAttribute("data-webpack")==dataWebpackPrefix+r){i=u;break}}i||(t=!0,(i=document.createElement("script")).charset="utf-8",i.timeout=120,__webpack_require__.nc&&i.setAttribute("nonce",__webpack_require__.nc),i.setAttribute("data-webpack",dataWebpackPrefix+r),i.src=e),inProgress[e]=[_];var c=(_,r)=>{i.onerror=i.onload=null,clearTimeout(p);var a=inProgress[e];if(delete inProgress[e],i.parentNode&&i.parentNode.removeChild(i),a&&a.forEach(e=>e(r)),_)return _(r)},p=setTimeout(c.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=c.bind(null,i.onerror),i.onload=c.bind(null,i.onload),t&&document.head.appendChild(i)}},__webpack_require__.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},__webpack_require__.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),(()=>{__webpack_require__.S={};var e={},_={};__webpack_require__.I=(r,a)=>{a||(a=[]);var i=_[r];if(i||(i=_[r]={}),!(a.indexOf(i)>=0)){if(a.push(i),e[r])return e[r];__webpack_require__.o(__webpack_require__.S,r)||(__webpack_require__.S[r]={});var t=__webpack_require__.S[r],n="pageplug-datasource-frontend",o=(e,_,r,a)=>{var i=t[e]=t[e]||{},o=i[_];(!o||!o.loaded&&(!a!=!o.eager?a:n>o.from))&&(i[_]={get:r,from:n,eager:!!a})},u=[];return"default"===r&&(o("@reduxjs/toolkit","1.9.7",()=>__webpack_require__.e(96).then(()=>()=>__webpack_require__(4636))),o("antd","5.26.6",()=>Promise.all([__webpack_require__.e(96),__webpack_require__.e(212),__webpack_require__.e(714)]).then(()=>()=>__webpack_require__(9392))),o("react-dom","18.3.1",()=>Promise.all([__webpack_require__.e(96),__webpack_require__.e(212)]).then(()=>()=>__webpack_require__(8143))),o("react-redux","8.1.3",()=>Promise.all([__webpack_require__.e(96),__webpack_require__.e(212),__webpack_require__.e(714)]).then(()=>()=>__webpack_require__(309))),o("react-router-dom","6.30.1",()=>Promise.all([__webpack_require__.e(96),__webpack_require__.e(212),__webpack_require__.e(714)]).then(()=>()=>__webpack_require__(8842))),o("react","18.3.1",()=>__webpack_require__.e(96).then(()=>()=>__webpack_require__(758))),o("styled-components","5.3.11",()=>Promise.all([__webpack_require__.e(96),__webpack_require__.e(212)]).then(()=>()=>__webpack_require__(2407)))),e[r]=u.length?Promise.all(u).then(()=>e[r]=1):1}}})(),(()=>{var e;__webpack_require__.g.importScripts&&(e=__webpack_require__.g.location+"");var _=__webpack_require__.g.document;if(!e&&_&&(_.currentScript&&"SCRIPT"===_.currentScript.tagName.toUpperCase()&&(e=_.currentScript.src),!e)){var r=_.getElementsByTagName("script");if(r.length)for(var a=r.length-1;a>-1&&(!e||!/^http(s?):/.test(e));)e=r[a--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/^blob:/,"").replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),__webpack_require__.p=e})(),parseVersion=e=>{var _=e=>e.split(".").map(e=>+e==e?+e:e),r=/^([^-+]+)?(?:-([^+]+))?(?:\+(.+))?$/.exec(e),a=r[1]?_(r[1]):[];return r[2]&&(a.length++,a.push.apply(a,_(r[2]))),r[3]&&(a.push([]),a.push.apply(a,_(r[3]))),a},versionLt=(e,_)=>{e=parseVersion(e),_=parseVersion(_);for(var r=0;;){if(r>=e.length)return r<_.length&&"u"!=(typeof _[r])[0];var a=e[r],i=(typeof a)[0];if(r>=_.length)return"u"==i;var t=_[r],n=(typeof t)[0];if(i!=n)return"o"==i&&"n"==n||"s"==n||"u"==i;if("o"!=i&&"u"!=i&&a!=t)return a<t;r++}},rangeToString=e=>{var _=e[0],r="";if(1===e.length)return"*";if(_+.5){r+=0==_?">=":-1==_?"<":1==_?"^":2==_?"~":_>0?"=":"!=";for(var a=1,i=1;i<e.length;i++)a--,r+="u"==(typeof(n=e[i]))[0]?"-":(a>0?".":"")+(a=2,n);return r}var t=[];for(i=1;i<e.length;i++){var n=e[i];t.push(0===n?"not("+o()+")":1===n?"("+o()+" || "+o()+")":2===n?t.pop()+" "+t.pop():rangeToString(n))}return o();function o(){return t.pop().replace(/^\((.+)\)$/,"$1")}},satisfy=(e,_)=>{if(0 in e){_=parseVersion(_);var r=e[0],a=r<0;a&&(r=-r-1);for(var i=0,t=1,n=!0;;t++,i++){var o,u,c=t<e.length?(typeof e[t])[0]:"";if(i>=_.length||"o"==(u=(typeof(o=_[i]))[0]))return!n||("u"==c?t>r&&!a:""==c!=a);if("u"==u){if(!n||"u"!=c)return!1}else if(n)if(c==u)if(t<=r){if(o!=e[t])return!1}else{if(a?o>e[t]:o<e[t])return!1;o!=e[t]&&(n=!1)}else if("s"!=c&&"n"!=c){if(a||t<=r)return!1;n=!1,t--}else{if(t<=r||u<c!=a)return!1;n=!1}else"s"!=c&&"n"!=c&&(n=!1,t--)}}var p=[],l=p.pop.bind(p);for(i=1;i<e.length;i++){var s=e[i];p.push(1==s?l()|l():2==s?l()&l():s?satisfy(s,_):!l())}return!!l()},exists=(e,_)=>e&&__webpack_require__.o(e,_),get=e=>(e.loaded=1,e.get()),eagerOnly=e=>Object.keys(e).reduce((_,r)=>(e[r].eager&&(_[r]=e[r]),_),{}),findSingletonVersionKey=(e,_,r)=>{var a=r?eagerOnly(e[_]):e[_];return Object.keys(a).reduce((e,_)=>!e||!a[e].loaded&&versionLt(e,_)?_:e,0)},getInvalidSingletonVersionMessage=(e,_,r,a)=>"Unsatisfied version "+r+" from "+(r&&e[_][r].from)+" of shared singleton module "+_+" (required "+rangeToString(a)+")",fail=e=>{throw new Error(e)},warn=e=>{"undefined"!=typeof console&&console.warn&&console.warn(e)},init=e=>function(_,r,a,i,t){var n=__webpack_require__.I(_);return n&&n.then&&!a?n.then(e.bind(e,_,__webpack_require__.S[_],r,!1,i,t)):e(_,__webpack_require__.S[_],r,a,i,t)},useFallback=(e,_,r)=>r?r():((e,_)=>fail("Shared module "+_+" doesn't exist in shared scope "+e))(e,_),loadSingletonVersion=init((e,_,r,a,i,t)=>{if(!exists(_,r))return useFallback(e,r,t);var n=findSingletonVersionKey(_,r,a);return satisfy(i,n)||warn(getInvalidSingletonVersionMessage(_,r,n,i)),get(_[r][n])}),installedModules={},moduleToHandlerMapping={212:()=>loadSingletonVersion("default","react",!1,[1,18,2,0],()=>__webpack_require__.e(96).then(()=>()=>__webpack_require__(758))),1714:()=>loadSingletonVersion("default","react-dom",!1,[1,18,2,0],()=>__webpack_require__.e(96).then(()=>()=>__webpack_require__(8143))),8076:()=>loadSingletonVersion("default","antd",!1,[1,5,2,2],()=>Promise.all([__webpack_require__.e(96),__webpack_require__.e(212),__webpack_require__.e(714)]).then(()=>()=>__webpack_require__(9392))),1299:()=>loadSingletonVersion("default","react-router-dom",!1,[1,6,8,1],()=>Promise.all([__webpack_require__.e(96),__webpack_require__.e(714)]).then(()=>()=>__webpack_require__(8842))),9235:()=>loadSingletonVersion("default","@reduxjs/toolkit",!1,[1,1,9,3],()=>__webpack_require__.e(96).then(()=>()=>__webpack_require__(4636))),8037:()=>loadSingletonVersion("default","react-redux",!1,[1,8,0,5],()=>Promise.all([__webpack_require__.e(96),__webpack_require__.e(212),__webpack_require__.e(714)]).then(()=>()=>__webpack_require__(309))),3017:()=>loadSingletonVersion("default","styled-components",!1,[1,5,3,6],()=>__webpack_require__.e(96).then(()=>()=>__webpack_require__(2407)))},chunkMapping={76:[8076],212:[212],255:[9235],299:[1299],662:[8037,3017],714:[1714]},startedInstallModules={},__webpack_require__.f.consumes=(e,_)=>{__webpack_require__.o(chunkMapping,e)&&chunkMapping[e].forEach(e=>{if(__webpack_require__.o(installedModules,e))return _.push(installedModules[e]);if(!startedInstallModules[e]){var r=_=>{installedModules[e]=0,__webpack_require__.m[e]=r=>{delete __webpack_require__.c[e],r.exports=_()}};startedInstallModules[e]=!0;var a=_=>{delete installedModules[e],__webpack_require__.m[e]=r=>{throw delete __webpack_require__.c[e],_}};try{var i=moduleToHandlerMapping[e]();i.then?_.push(installedModules[e]=i.then(r).catch(a)):r(i)}catch(e){a(e)}}})},(()=>{__webpack_require__.b=document.baseURI||self.location.href;var e={792:0,43:0};__webpack_require__.f.j=(_,r)=>{var a=__webpack_require__.o(e,_)?e[_]:void 0;if(0!==a)if(a)r.push(a[2]);else if(/^(212|299|43|714|76)$/.test(_))e[_]=0;else{var i=new Promise((r,i)=>a=e[_]=[r,i]);r.push(a[2]=i);var t=__webpack_require__.p+__webpack_require__.u(_),n=new Error;__webpack_require__.l(t,r=>{if(__webpack_require__.o(e,_)&&(0!==(a=e[_])&&(e[_]=void 0),a)){var i=r&&("load"===r.type?"missing":r.type),t=r&&r.target&&r.target.src;n.message="Loading chunk "+_+" failed.\n("+i+": "+t+")",n.name="ChunkLoadError",n.type=i,n.request=t,a[1](n)}},"chunk-"+_,_)}},__webpack_require__.O.j=_=>0===e[_];var _=(_,r)=>{var a,i,t=r[0],n=r[1],o=r[2],u=0;if(t.some(_=>0!==e[_])){for(a in n)__webpack_require__.o(n,a)&&(__webpack_require__.m[a]=n[a]);if(o)var c=o(__webpack_require__)}for(_&&_(r);u<t.length;u++)i=t[u],__webpack_require__.o(e,i)&&e[i]&&e[i][0](),e[i]=0;return __webpack_require__.O(c)},r=self.webpackChunkpageplug_datasource_frontend=self.webpackChunkpageplug_datasource_frontend||[];r.forEach(_.bind(null,0)),r.push=_.bind(null,r.push.bind(r))})(),__webpack_require__.nc=void 0;var __webpack_exports__=__webpack_require__.O(void 0,[43],()=>__webpack_require__(7714));__webpack_exports__=__webpack_require__.O(__webpack_exports__)})();