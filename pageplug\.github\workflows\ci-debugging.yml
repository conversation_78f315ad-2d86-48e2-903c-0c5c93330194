name: CI Debugging

on:
  # This line enables manual triggering of this workflow.
  workflow_dispatch:
    inputs:
      run_id:
        description: "Run id to download artifacts:"
        required: true
        type: number

jobs:
  ci-debugger:
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch'
    defaults:
      run:
        shell: bash
    strategy:
      fail-fast: false
      matrix:
        job: [0]

    # Service containers to run with this job. Required for running tests
    services:
      # Label used to access the service container
      redis:
        # Docker Hub image for Redis
        image: redis
        ports:
          # Opens tcp port 6379 on the host and service container
          - 6379:6379
      mongo:
        image: mongo
        ports:
          - 27017:27017

    steps:
      - name: Set up Depot CLI
        uses: depot/setup-action@v1

      - name: Checkout the head commit of the branch
        uses: actions/checkout@v4

      - name: Download Docker image artifact
        uses: dawidd6/action-download-artifact@v2
        with:
          name: cicontainer
          run_id: ${{ github.event.inputs.run_id }}

      - name: Load Docker image from tar file
        run: |
          gunzip cicontainer.tar.gz
          docker load -i cicontainer.tar

      - name: Create folder
        if: steps.run_result.outputs.run_result != 'success'
        env:
          APPSMITH_LICENSE_KEY: ${{ secrets.APPSMITH_LICENSE_KEY }}
        working-directory: "."
        run: |
          mkdir -p cicontainerlocal/stacks/configuration/

      - name: Run docker image
        if: steps.run_result.outputs.run_result != 'success'
        env:
          APPSMITH_LICENSE_KEY: ${{ secrets.APPSMITH_LICENSE_KEY }}
        working-directory: "."
        run: |
          sudo /etc/init.d/ssh stop ;
          mkdir -p ~/git-server/keys
          mkdir -p ~/git-server/repos
          docker run --name test-event-driver -d -p 22:22 -p 5001:5001 -p 3306:3306 \
          -p 5432:5432 -p 28017:27017 -p 25:25 -p 5000:5000 -p 3001:3000 --privileged --pid=host --ipc=host --volume /:/host -v ~/git-server/keys:/git-server/keys \
          -v ~/git-server/repos:/git-server/repos  appsmith/test-event-driver:latest
          cd cicontainerlocal
          docker run -d --name appsmith -p 80:80 \
            -v "$PWD/stacks:/appsmith-stacks" -e APPSMITH_LICENSE_KEY=$APPSMITH_LICENSE_KEY \
            -e APPSMITH_DISABLE_TELEMETRY=true \
            -e APPSMITH_CLOUD_SERVICES_BASE_URL=http://host.docker.internal:5001 \
            --add-host=host.docker.internal:host-gateway \
            cicontainer

      - name: Execute access to ci-debug.sh
        if: steps.run_result.outputs.run_result != 'success'
        working-directory: "."
        run: |
          chmod +x ci-debug.sh

      # Start tmate session for tunnelling
      - name: Setup tmate session
        if: steps.run_result.outputs.run_result != 'success'
        uses: mxschmitt/action-tmate@v3
